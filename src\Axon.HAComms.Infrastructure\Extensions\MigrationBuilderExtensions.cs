﻿using Microsoft.EntityFrameworkCore.Migrations.Operations.Builders;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Text.RegularExpressions;

namespace Axon.HAComms.Infrastructure.Extensions
{
    public static class MigrationBuilderExtensions
    {
        public static OperationBuilder<SqlOperation> SqlFile(this MigrationBuilder migrationBuilder, string sqlFileName)
        {
            return migrationBuilder.Sql(ReadFile(sqlFileName));
        }

        public static OperationBuilder<SqlOperation> SqlFileExec(this MigrationBuilder migrationBuilder, string sqlFileName)
        {
            var sqlScript = ReadFile(sqlFileName);

            var r = new Regex("\\s*\\n+\\s*GO\\s*\\n*\\s*\\z*", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(500));

            return migrationBuilder.Sql(string.Join("\nGO\n", Process(r.Split(sqlScript))));
        }

        public static string ReadFile(string sqlFileName)
        {
            var assembly = typeof(MigrationBuilderExtensions).Assembly;

            using var stream = assembly.GetManifestResourceStream(assembly.GetName().Name + ".Scripts." + sqlFileName);
            using var reader = new StreamReader(stream!);
            return reader.ReadToEnd();
        }

        private static IEnumerable<string> Process(string[] operations)
        {
            foreach (string op in operations)
            {
                if (!string.IsNullOrWhiteSpace(op))
                    yield return $"EXEC('\n{op.Replace("'", "''")}\n');";
            }
        }
    }
}
