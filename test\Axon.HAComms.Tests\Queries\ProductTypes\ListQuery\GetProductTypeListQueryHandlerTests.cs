﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.ProductTypes.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.ProductTypes;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.ProductTypes.ListQuery;

public class GetProductTypeListQueryHandlerTests
{
    private readonly GetProductTypesListQueryHandler handler;
    private readonly IProductTypesRepository productTypesRepo;

    public GetProductTypeListQueryHandlerTests()
    {
        productTypesRepo = Substitute.For<IProductTypesRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductTypesMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetProductTypesListQueryHandler(productTypesRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(3);

        productTypesRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetProductTypesListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(3);

        productTypesRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetProductTypesListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }
}
