using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Products.Update;

[UsedImplicitly]
public class UpdateProductCommandValidator : AbstractValidator<UpdateProductCommandRequest>
{
    public UpdateProductCommandValidator(IProductsRepository productsRepo, IProductExtensionsRepository productExtensionsRepo)
    {
        ClassLevelCascadeMode = CascadeMode.Stop;

        RuleFor(x => x.Id)
            .NotEmpty();

        this.AddProductRequestValidation();

        RuleFor(x => x)
            .Must(request =>
            {
                return !productsRepo.ExistsAsync(x => string.Equals(x.Name, request.Name) && x.Id != request.Id).GetAwaiter().GetResult();
            })
            .WithName("NameAlreadyExists")
            .WithMessage(x => $"Entity with Name = ({x.Name}) already exists.");
    }
}
