﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.DrugSubstances.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Update;

public class UpdateDrugSubstancesCommandHandlerTests
{
    private readonly UpdateDrugSubstanceCommandHandler sut;
    private readonly IDrugSubstancesRepository drugSubstancesRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public UpdateDrugSubstancesCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DrugSubstancesMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<UpdateDrugSubstanceCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.DRUG_SUBSTANCE_UPDATED, AuditEventCategory.DRUG_SUBSTANCES,
            AuditEventDescription.DRUG_SUBSTANCE_UPDATE, Arg.Any<DrugSubstance>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());
        sut = new UpdateDrugSubstanceCommandHandler(drugSubstancesRepo, mapper, logger,
            correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var entity = new DrugSubstance { Name = drugSubstanceName, Code = drugSubstanceCode, Description = drugSubstanceDescription };
        drugSubstancesRepo.GetItemAsync(1).Returns(entity);

        var request = new UpdateDrugSubstanceCommandRequest(1, entity.Name, entity.Code, entity.Description);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(1);

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DRUG_SUBSTANCE_UPDATED, AuditEventCategory.DRUG_SUBSTANCES, AuditEventDescription.DRUG_SUBSTANCE_UPDATE,
                Arg.Any<DrugSubstance>(), Arg.Any<Func<Task>>());
    }
}
