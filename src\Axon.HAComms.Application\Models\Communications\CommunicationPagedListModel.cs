using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Application.Models.Communications
{
    public class CommunicationPagedListModel
    {
		[Required]
		public int Id { get; set; }
        public string? Subject { get; set; } 
        public string? ProductNames { get; set; }
        public int CountryId { get; set; }
        public string CountryName { get; set; }
        public DateTime DateOfCommunication { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public bool IsCompleted { get; set; }

        public CommunicationPagedListModel(
            int id,
            string? subject,
            int countryId,
            string countryName,
            string productNames,
            DateTime dateOfCommunication,
            DateTime? createdDate,
            string? createdBy,
            bool isCompleted)
        {
            Id = id;
            Subject = subject;
            CountryId = countryId;
            CountryName = countryName;
            ProductNames = productNames;
            DateOfCommunication = dateOfCommunication;
            CreatedDate = createdDate;
            CreatedBy = createdBy;
            IsCompleted = isCompleted;
        }
    }
}
