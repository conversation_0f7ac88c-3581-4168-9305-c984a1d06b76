﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.HAComms.Application.Commands.Comments.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Domain.Constants;

namespace Axon.HAComms.Tests.Commands.Comments.Delete;

public class DeleteCommentCommandHandlerTests
{
    private readonly DeleteCommentCommandHandler handler;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteCommentCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        var userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS,
            AuditEventDescription.COMMENT_DELETE, Arg.Any<Comment>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        var logger = Substitute.For<ILogger<DeleteCommentCommandHandler>>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        handler = new DeleteCommentCommandHandler(commentsRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var communicationId = Fake.Communication.Id;
        var productExtensionId = Fake.ProductExtension.Id;

        var comment1 = new Comment(Fake.Comment.Id)
        {
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
            CommentProductExtensions = new List<CommentProductExtension>()
            {
                new CommentProductExtension() { ProductExtensionId = productExtensionId, ProductExtension = new ProductExtension() { Product = new Product() } }
            },
            CommunicationId = communicationId,
        };

        var request = new DeleteCommentCommandRequest()
        {
            Id = comment1.Id,
        };

        commentsRepo.GetItemByIdAsync(request.Id).Returns(comment1);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(true);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        await auditService
           .ReceivedWithAnyArgs(1)
           .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS, AuditEventDescription.COMMENT_DELETE,
                Arg.Any<Comment>(), Arg.Any<Func<Task>>());
    }


    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var request = new DeleteCommentCommandRequest()
        {
            Id = Fake.Comment.Id,
        };

        // Act
        Func<Task> result = async () => { await handler.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_OnlyOneCommentForProductForCommunication_ThrowsAssociationException()
    {
        // Arrange
        var communicationId = Fake.Communication.Id;
        var productExtensionId = Fake.ProductExtension.Id;

        var comment = new Comment(Fake.Comment.Id)
        {
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
            CommentProductExtensions = new List<CommentProductExtension>()
            {
                new CommentProductExtension() { ProductExtensionId = productExtensionId, ProductExtension = new ProductExtension() { Product = new Product() } }
            },
            CommunicationId = communicationId,
        };

        var request = new DeleteCommentCommandRequest()
        {
            Id = comment.Id,
        };

        commentsRepo.GetItemByIdAsync(request.Id).Returns(comment);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(false);

        // Act
        Func<Task> result = async () => { await handler.Handle(request, CancellationToken.None); };

        // Assert
        var exception = await result.Should().ThrowAsync<Exception>();
        exception.And.Message.Should().Contain("Cannot delete the last comment for a communication");
    }
}
