ARG versionNo=0.0.0.0
ARG nugetSource=''
ARG nugetPassword=''
ARG projectName=Axon.HAComms
ARG configuration=Release
ARG lastCommitHash=''

# Use Alpine Base Image
FROM mcr.microsoft.com/dotnet/aspnet:8.0-noble-chiseled-extra AS base
ARG versionNo
EXPOSE 80
EXPOSE 443

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS publish
ARG nugetSource
ARG nugetPassword
ARG projectName
ARG versionNo
ARG configuration
WORKDIR /
COPY . .

# Restore & Publish for target runtime
RUN dotnet nuget add source ${nugetSource} -n Phlexglobal -u ignore -p ${nugetPassword} --store-password-in-clear-text \
 && dotnet restore test/${projectName}.Tests/${projectName}.Tests.csproj \
 && dotnet restore test/${projectName}.ArchTests/${projectName}.ArchTests.csproj \
 && dotnet restore test/${projectName}.IntegrationTests/${projectName}.IntegrationTests.csproj --runtime linux-musl-x64 \
 && dotnet publish src/${projectName}.Api/${projectName}.Api.csproj -c ${configuration} /property:Version=${versionNo} -o /app/publish \
    --runtime linux-musl-x64 \
 && dotnet nuget remove source Phlexglobal

# Stage 1, Test application
FROM publish AS testrunner
WORKDIR /
ARG projectName
ARG configuration
ENV configuration_env=${configuration}
ENV testProjectName=test/${projectName}.Tests/${projectName}.Tests.csproj
ENV archTestProjectName=test/${projectName}.ArchTests/${projectName}.ArchTests.csproj
ENV intTestProjectName=test/${projectName}.IntegrationTests/${projectName}.IntegrationTests.csproj

RUN chmod +x /scripts/entrypoint.sh
ENTRYPOINT ["/scripts/entrypoint.sh"]

# Stage 2, Run application
FROM base AS final
ARG projectName
ARG lastCommitHash

WORKDIR /app
COPY --from=publish /app/publish .

# Impersonate
USER dotnetuser
ENV LASTCOMMITHASH=$lastCommitHash
ENTRYPOINT ["dotnet", "Axon.HAComms.Api.dll"]
