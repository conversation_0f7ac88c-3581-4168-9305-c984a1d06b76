﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Communications.PagedListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Communications.ListQuery
{
    public class GetCommunicationsPagedListQueryHandlerTests
    {
        private readonly GetCommunicationsPagedListQueryHandler handler;
        private readonly ICommunicationsRepository communicationsRepo;

        public GetCommunicationsPagedListQueryHandlerTests()
        {
            communicationsRepo = Substitute.For<ICommunicationsRepository>();
            var mockMapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new CommunicationsMappingProfile());
                cfg.AddProfile(new ProductsMappingProfile());
                cfg.AddProfile(new DrugSubstancesMappingProfile());
                cfg.AddProfile(new ProductExtensionsMappingProfile());
                cfg.AddProfile(new DosageFormsMappingProfile());
                cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
                cfg.AddProfile(new CommentsMappingProfile());
                cfg.AddProfile(new CountryMappingProfile());
            });
            var mapper = mockMapper.CreateMapper();
            handler = new GetCommunicationsPagedListQueryHandler(communicationsRepo, mapper);
        }

        [Fact]
        public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
        {
            // Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(101);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 100, 10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().HaveCount(1);
        }

        [Fact]
        public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
        {
            //Arrange
            var subject = Fake.Communication.Subject;
            var communication = new CommunicationsViewBuilder()
                .WithSubject(subject)
                .Build();

            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(24);
            communications.Add(communication);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(new[] { $"subject=>{subject}" }, 0, 10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().Contain(x => x.Subject != null && x.Subject.Equals(subject));
        }

        [Fact]
        public async Task Handle_ValidFilterCountryRequest_ReturnsCorrectFilteredItem()
        {
            //Arrange
            var countryId = Fake.Country.Id;
            var communication = new CommunicationsViewBuilder()
                .WithCountryId(countryId)
                .Build();

            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(24);
            communications.Add(communication);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(new[] { $"countryid=>{countryId}" }, 0, 10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().Contain(x => x.CountryId == countryId);
            result.Data.Should().NotContain(x => x.CountryId != countryId);
        }

        [Fact]
        public async Task Handle_ValidDateFilterRequest_ReturnsCorrectFilteredItem()
        {
            //Arrange
            var createdDate = new DateTime(2023, 04, 11);
            var dateOfCommunication = new DateTime(2022, 10, 25);
            var createdBy = Fake.Communication.CreatedBy;
            var productName = Fake.Product.Name;

            var communication = new CommunicationsViewBuilder()
                .WithCreatedDate(createdDate)
                .WithDateOfCommunication(dateOfCommunication)
                .WithCreatedBy(createdBy)
                .WithProductNames(productName)
                .Build();

            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(24);
            communications.Add(communication);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);

            var request = new GetCommunicationsPagedListQueryRequest(new[] { "createddate=>2023", "dateofcommunication=>2022", $"createdby=>{createdBy}", $"product=>{productName}" }, 0, 10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().Contain(x =>
                x.CreatedDate.Equals(createdDate) &&
                x.DateOfCommunication.Equals(dateOfCommunication) &&
                (x.CreatedBy != null && x.CreatedBy.Contains(createdBy)) &&
                (x.ProductNames != null && x.ProductNames.Contains(productName)));
        }

        [Fact]
        public async Task Handle_ValidOrderRequest_ReturnsAscOrderedItems()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(15);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 0, 10, "subject=>asc");

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().BeInAscendingOrder(x => x.Subject);
        }

        [Fact]
        public async Task Handle_ValidOrderRequest_ReturnsDescOrderedItems()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(15);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 0, 10, "subject=>desc");

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().BeInDescendingOrder(x => x.Subject);
        }

        [Fact]
        public async Task Handle_ValidOrderRequest_ByDefaultReturnsItemsSortedBySubject()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(15);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 0, 10, "INVALID_KEY=>INVALID_VALUE");

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().BeInAscendingOrder(x => x.Subject);
        }


        [Fact]
        public async Task Handle_PassNullOrderParam_ReturnsOrderedItemsBySubject()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(105);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 10, 50, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().BeInAscendingOrder(x => x.Subject);
        }

        [Fact]
        public async Task Handle_PassDateOrderDesc_ReturnsOrderedItems()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(20);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 10, 10, "createddate=>desc");

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().BeInDescendingOrder(x => x.CreatedDate);
        }

        [Fact]
        public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(105);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), -10, 10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().HaveCount(10);
        }

        [Fact]
        public async Task Handle_PassInvalidTake_ReturnsFirstPage()
        {
            //Arrange
            var communications = TestEntitiesGenerator<CommunicationsView, CommunicationsViewBuilder>.Generate(50);
            var mock = communications.BuildMock();
            communicationsRepo.GetCommunicationsView().Returns(mock);
            var request = new GetCommunicationsPagedListQueryRequest(Array.Empty<string>(), 10, -10, null);

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().NotBeEmpty();
            result.Data.Should().HaveCount(40);
        }
    }
}
