﻿using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Tests.Common.Builders
{
    public class DosageFormSdkModelBuilder
    {
        private int id = Fake.DosageForm.Id;
        private string name = Fake.DosageForm.Name;

        public static DosageFormSdkModelBuilder Default() => new();

        public DosageFormSdkModelBuilder WithName(string dosageFormName)
        {
            name = dosageFormName;
            return this;
        }

        public DosageFormSdkModelBuilder WithId(int dosageFormId)
        {
            id = dosageFormId;
            return this;
        }

        public DosageFormModel Build()
        {
            return new(id, name);
        }
    }
}
