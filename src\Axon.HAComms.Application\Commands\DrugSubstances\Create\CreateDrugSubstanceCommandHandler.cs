﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.DrugSubstances.Create;

internal class CreateDrugSubstanceCommandHandler(
    IDrugSubstancesRepository repoDrugSubstances,
    IMapper mapper,
    ILogger<CreateDrugSubstanceCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<CreateDrugSubstanceCommandRequest, CreateDrugSubstanceCommandResponse>
{
    public async Task<CreateDrugSubstanceCommandResponse> Handle(CreateDrugSubstanceCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = new DrugSubstance();
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.DRUG_SUBSTANCE_CREATED, AuditEventCategory.DRUG_SUBSTANCES, AuditEventDescription.DRUG_SUBSTANCE_CREATE, entity,
            async () =>
            {
                entity.Name = string.IsNullOrEmpty(request.Name.Trim()) ? Constants.NotAssigned : request.Name;
                entity.Description = request.Description;
                entity.Code = request.Code;
                repoDrugSubstances.AddItem(entity);
                await repoDrugSubstances.SaveChangesAsync(userProvider);
                logger.LogInformation("Drug Substance {DrugSubstance} added successfully.", entity.Name);
            });

        var response = mapper.Map<CreateDrugSubstanceCommandResponse>(entity);
        return response;
    }
}
