/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Api.Sdk.Net.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDosageFormsApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <returns>CreateDosageFormCommandResponse</returns>
        CreateDosageFormCommandResponse CreateDosageForm(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateDosageFormCommandResponse</returns>
        ApiResponse<CreateDosageFormCommandResponse> CreateDosageFormWithHttpInfo(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void DeleteDosageForm(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteDosageFormWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>DosageFormModelApiListResult</returns>
        DosageFormModelApiListResult GetDosageFormList(string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of DosageFormModelApiListResult</returns>
        ApiResponse<DosageFormModelApiListResult> GetDosageFormListWithHttpInfo(string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>DosageFormPagedListModelApiPagedListResult</returns>
        DosageFormPagedListModelApiPagedListResult GetPagedDosageFormsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of DosageFormPagedListModelApiPagedListResult</returns>
        ApiResponse<DosageFormPagedListModelApiPagedListResult> GetPagedDosageFormsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <returns>UpdateDosageFormCommandResponse</returns>
        UpdateDosageFormCommandResponse UpdateDosageForm(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateDosageFormCommandResponse</returns>
        ApiResponse<UpdateDosageFormCommandResponse> UpdateDosageFormWithHttpInfo(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDosageFormsApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateDosageFormCommandResponse</returns>
        System.Threading.Tasks.Task<CreateDosageFormCommandResponse> CreateDosageFormAsync(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateDosageFormCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CreateDosageFormCommandResponse>> CreateDosageFormWithHttpInfoAsync(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteDosageFormAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteDosageFormWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DosageFormModelApiListResult</returns>
        System.Threading.Tasks.Task<DosageFormModelApiListResult> GetDosageFormListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DosageFormModelApiListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<DosageFormModelApiListResult>> GetDosageFormListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DosageFormPagedListModelApiPagedListResult</returns>
        System.Threading.Tasks.Task<DosageFormPagedListModelApiPagedListResult> GetPagedDosageFormsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DosageFormPagedListModelApiPagedListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<DosageFormPagedListModelApiPagedListResult>> GetPagedDosageFormsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateDosageFormCommandResponse</returns>
        System.Threading.Tasks.Task<UpdateDosageFormCommandResponse> UpdateDosageFormAsync(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateDosageFormCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UpdateDosageFormCommandResponse>> UpdateDosageFormWithHttpInfoAsync(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDosageFormsApi : IDosageFormsApiSync, IDosageFormsApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class DosageFormsApi : IDisposable, IDosageFormsApi
    {
        private Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public DosageFormsApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public DosageFormsApi(string basePath)
        {
            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public DosageFormsApi(Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DosageFormsApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DosageFormsApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DosageFormsApi(HttpClient client, Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DosageFormsApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public DosageFormsApi(Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient client, Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient asyncClient, Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <returns>CreateDosageFormCommandResponse</returns>
        public CreateDosageFormCommandResponse CreateDosageForm(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDosageFormCommandResponse> localVarResponse = CreateDosageFormWithHttpInfo(tenant, createDosageFormCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateDosageFormCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDosageFormCommandResponse> CreateDosageFormWithHttpInfo(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->CreateDosageForm");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createDosageFormCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CreateDosageFormCommandResponse>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateDosageFormCommandResponse</returns>
        public async System.Threading.Tasks.Task<CreateDosageFormCommandResponse> CreateDosageFormAsync(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDosageFormCommandResponse> localVarResponse = await CreateDosageFormWithHttpInfoAsync(tenant, createDosageFormCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateDosageFormCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDosageFormCommandResponse>> CreateDosageFormWithHttpInfoAsync(string tenant, CreateDosageFormCommandRequest? createDosageFormCommandRequest = default(CreateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->CreateDosageForm");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createDosageFormCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CreateDosageFormCommandResponse>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void DeleteDosageForm(int id, string tenant)
        {
            DeleteDosageFormWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> DeleteDosageFormWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->DeleteDosageForm");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/{tenant}/v1/DosageForms/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteDosageFormAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteDosageFormWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> DeleteDosageFormWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->DeleteDosageForm");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/{tenant}/v1/DosageForms/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>DosageFormModelApiListResult</returns>
        public DosageFormModelApiListResult GetDosageFormList(string tenant)
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormModelApiListResult> localVarResponse = GetDosageFormListWithHttpInfo(tenant);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of DosageFormModelApiListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormModelApiListResult> GetDosageFormListWithHttpInfo(string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->GetDosageFormList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<DosageFormModelApiListResult>("/{tenant}/v1/DosageForms/all", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetDosageFormList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DosageFormModelApiListResult</returns>
        public async System.Threading.Tasks.Task<DosageFormModelApiListResult> GetDosageFormListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormModelApiListResult> localVarResponse = await GetDosageFormListWithHttpInfoAsync(tenant, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DosageFormModelApiListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormModelApiListResult>> GetDosageFormListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->GetDosageFormList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<DosageFormModelApiListResult>("/{tenant}/v1/DosageForms/all", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetDosageFormList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>DosageFormPagedListModelApiPagedListResult</returns>
        public DosageFormPagedListModelApiPagedListResult GetPagedDosageFormsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormPagedListModelApiPagedListResult> localVarResponse = GetPagedDosageFormsListWithHttpInfo(tenant, filters, skip, take, order);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of DosageFormPagedListModelApiPagedListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormPagedListModelApiPagedListResult> GetPagedDosageFormsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->GetPagedDosageFormsList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<DosageFormPagedListModelApiPagedListResult>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedDosageFormsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DosageFormPagedListModelApiPagedListResult</returns>
        public async System.Threading.Tasks.Task<DosageFormPagedListModelApiPagedListResult> GetPagedDosageFormsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormPagedListModelApiPagedListResult> localVarResponse = await GetPagedDosageFormsListWithHttpInfoAsync(tenant, filters, skip, take, order, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DosageFormPagedListModelApiPagedListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DosageFormPagedListModelApiPagedListResult>> GetPagedDosageFormsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->GetPagedDosageFormsList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<DosageFormPagedListModelApiPagedListResult>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedDosageFormsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <returns>UpdateDosageFormCommandResponse</returns>
        public UpdateDosageFormCommandResponse UpdateDosageForm(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDosageFormCommandResponse> localVarResponse = UpdateDosageFormWithHttpInfo(tenant, updateDosageFormCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateDosageFormCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDosageFormCommandResponse> UpdateDosageFormWithHttpInfo(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->UpdateDosageForm");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateDosageFormCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<UpdateDosageFormCommandResponse>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateDosageFormCommandResponse</returns>
        public async System.Threading.Tasks.Task<UpdateDosageFormCommandResponse> UpdateDosageFormAsync(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDosageFormCommandResponse> localVarResponse = await UpdateDosageFormWithHttpInfoAsync(tenant, updateDosageFormCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDosageFormCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateDosageFormCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDosageFormCommandResponse>> UpdateDosageFormWithHttpInfoAsync(string tenant, UpdateDosageFormCommandRequest? updateDosageFormCommandRequest = default(UpdateDosageFormCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DosageFormsApi->UpdateDosageForm");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateDosageFormCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<UpdateDosageFormCommandResponse>("/{tenant}/v1/DosageForms", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateDosageForm", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
