﻿using Axon.HAComms.Application.Commands.RoutesOfAdministration.Delete;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Delete
{
    public class DeleteRouteOfAdministrationValidatorTests
    {
        private readonly DeleteRouteOfAdministrationCommandValidator sut;

        public DeleteRouteOfAdministrationValidatorTests()
        {
            sut = new DeleteRouteOfAdministrationCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var result = sut.TestValidate(new DeleteRouteOfAdministrationCommandRequest(0));
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var result = sut.TestValidate(new DeleteRouteOfAdministrationCommandRequest(1));
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
