﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IProductTypesRepository : IRepository<ProductType>
{
    Task<IEnumerable<ProductType>> GetItemsAsync();

    IQueryable<ProductType> GetQueryableItems();

    Task<ProductType[]> GetAllByIdsAsync(params int[] ids);

    Task<ProductType?> GetItemByFilterAsync(Expression<Func<ProductType, bool>> filter);
}
