﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Extensions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class TagConfiguration : BaseEntityConfiguration<Tag>
{
    protected override void ConfigureEntity(EntityTypeBuilder<Tag> builder)
    {
        builder.Property(e => e.Name)
            .ConfigureNameField();

        builder.Property(e => e.Description)
            .ConfigureDescriptionField();
    }
}
