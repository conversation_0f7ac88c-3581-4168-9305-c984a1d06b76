﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.DosageForms.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DosageForms.Update;

public class UpdateDosageFormCommandHandlerTests
{
    private readonly UpdateDosageFormCommandHandler sut;
    private readonly IDosageFormsRepository dosageFormRepository;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public UpdateDosageFormCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DosageFormsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        dosageFormRepository = Substitute.For<IDosageFormsRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<UpdateDosageFormCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.DOSAGE_FORM_UPDATED, AuditEventCategory.DOSAGE_FORMS,
            AuditEventDescription.DOSAGE_FORM_UPDATE, Arg.Any<DosageForm>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        sut = new UpdateDosageFormCommandHandler(dosageFormRepository, mapper, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var dosageFormName = Fake.DosageForm.Name;
        var id = Fake.DosageForm.Id;

        var entity = new DosageForm(id) { Name = dosageFormName };
        dosageFormRepository.GetItemAsync(1).Returns(entity);

        var request = new UpdateDosageFormCommandRequest(1, entity.Name);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(id);

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DOSAGE_FORM_UPDATED, AuditEventCategory.DOSAGE_FORMS, AuditEventDescription.DOSAGE_FORM_UPDATE,
                Arg.Any<DosageForm>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new DosageForm { Name = Fake.DosageForm.Name };
        var request = new UpdateDosageFormCommandRequest(Fake.DosageForm.Id, entity.Name);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
