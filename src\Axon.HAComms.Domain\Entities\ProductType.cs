﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class ProductType : MultiTenantEntity
{
    public ProductType()
    {
    }

    public ProductType(int id)
    {
        this.Id = id;
    }

    public string Name { get; set; } = string.Empty;

    public ICollection<Product>? Products { get; set; } = new HashSet<Product>();

    public ICollection<ProductProductTypes> ProductProductTypes { get; set; } = new HashSet<ProductProductTypes>();
}
