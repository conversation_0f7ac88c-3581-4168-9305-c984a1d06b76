﻿using Autofac;
using Microsoft.Extensions.Configuration;

namespace Axon.HAComms.Infrastructure.IoC
{
    public class HaCommsModule : Module
    {
        private readonly IConfiguration configuration;

        public HaCommsModule(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            //Placeholder for Autofac module registration
        }
    }
}
