using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Communications.Delete;

internal class DeleteCommunicationCommandHandler(
    ICommentsRepository commentsRepo,
    ICommunicationsRepository communicationsRepo,
    ILogger<DeleteCommunicationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteCommunicationCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteCommunicationCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await communicationsRepo.GetCommunicationAsync(request.Id);

        if (entity.IsCompleted)
        {
            throw new InvalidOperationException("Cannot delete completed communication!");
        }

        await auditService.LogAsync(correlationIdProvider.Provide(), clientDetailsProvider.Provide(), AuditEventType.COMMUNICATION_DELETED,
            AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_DELETE, entity, FuncDeleteCommunication);

        return Result.Success();

        async Task FuncDeleteCommunication()
        {
            var comments = await commentsRepo.GetFilteredComments(filter: c => c.CommunicationId == request.Id).ToArrayAsync(cancellationToken);
            commentsRepo.DeleteItems(comments);
            await commentsRepo.SaveChangesAsync(userProvider);

            entity.Delete();
            communicationsRepo.UpdateItem(entity);
            await communicationsRepo.SaveChangesAsync(userProvider);

            logger.LogInformation("Communication {Subject} deleted successfully.", entity.Subject);
        }
    }
}

