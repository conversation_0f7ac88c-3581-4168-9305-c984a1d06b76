﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Axon.HAComms.Api.JsonHandling
{
    public class BooleanConverter : JsonConverter<bool>
    {
        public override bool Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType is JsonTokenType.True or JsonTokenType.False)
            {
                return reader.GetBoolean();
            }
            else if (reader.TokenType is JsonTokenType.String)
            {
                if (bool.TryParse(reader.GetString(), out bool boolValue))
                {
                    return boolValue;
                }
                else
                {
                    throw new JsonException("Invalid bool format. Please provide bool in valid format.");
                }
            }

            throw new JsonException("Invalid JSON token type for bool.");
        }

        public override void Write(Utf8JsonWriter writer, bool value, JsonSerializerOptions options)
        {
            writer.WriteBooleanValue(value);
        }
    }
}
