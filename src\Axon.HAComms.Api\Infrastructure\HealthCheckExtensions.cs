﻿using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Newtonsoft.Json;
using System.Net.Mime;
using System.Reflection;
using System.Text.Json;

namespace Axon.HAComms.Api.Infrastructure
{
    public static class HealthCheckExtensions
    {
        private static readonly JsonSerializerOptions JsonOptions = new() { WriteIndented = true };
        private static readonly string[] Tags = new[] { "ready" };

        /// <summary>
        /// Adds the HealthCheck settings for Startup
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration">The IConfiguration variable from initialisation</param>
        /// <returns></returns>
        public static void AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHealthChecks()
                .AddCheck("HealthCheck", () =>
                    HealthCheckResult.Healthy("Health is OK!"), tags: Tags);

        }

        public static IEndpointRouteBuilder MapHealthChecks(IEndpointRouteBuilder endpoints)
        {
            IDictionary<HealthStatus, int> resultStatusCodes = new Dictionary<HealthStatus, int>
            {
                [HealthStatus.Healthy] = StatusCodes.Status200OK,
                [HealthStatus.Degraded] = StatusCodes.Status200OK,
                [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
            };

            endpoints.MapHealthChecks("/health", new HealthCheckOptions()
            {
                ResponseWriter = WriteHealthInformationResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            //basic readiness check placeholder
            endpoints.MapHealthChecks("/health/readiness", new HealthCheckOptions()
            {
                Predicate = (check) => check.Tags.Contains("ready"),
                ResponseWriter = WriteReadinessResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            //basic liveness check placeholder
            endpoints.MapHealthChecks("/health/liveness", new HealthCheckOptions()
            {
                Predicate = (_) => false,
                ResponseWriter = WriteReadinessResponse,
                AllowCachingResponses = false,
                ResultStatusCodes = resultStatusCodes
            });

            return endpoints;
        }

        /// <summary>
        /// Writes the readiness response including all check information and current status.
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <param name="result">The result.</param>
        /// <returns></returns>
        private static Task WriteReadinessResponse(HttpContext httpContext, HealthReport result)
        {
            httpContext.Response.ContentType = MediaTypeNames.Application.Json;
            httpContext.Response.Headers.Append("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

            var json = JsonConvert.SerializeObject(
                new
                {
                    status = result.Status.ToString(),
                    results = result.Entries.Select(entry =>
                        new
                        {
                            Name = entry.Key,
                            Details = new
                            {
                                status = entry.Value.Status.ToString(),
                                description = entry.Value.Description,
                                data = entry.Value.Data.Select(d => new
                                {
                                    d.Key,
                                    d.Value
                                })
                            }
                        })
                }, Formatting.Indented);

            return httpContext.Response.WriteAsync(json);
        }

        /// <summary>
        /// Writes the health information response including App Name and Version
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <param name="result">The result.</param>
        /// <returns></returns>
        private static Task WriteHealthInformationResponse(HttpContext httpContext,
            HealthReport result)
        {
            httpContext.Response.ContentType = MediaTypeNames.Application.Json;
            httpContext.Response.Headers.Append("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

            var applicationName = Assembly.GetEntryAssembly()?.GetName().Name;
            var json = new
            {
                status = result.Status.ToString(),
                app = applicationName,
                version = VersionConfiguration.DisplayVersion
            };
            return httpContext.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(json, options: JsonOptions));
        }
    }
}
