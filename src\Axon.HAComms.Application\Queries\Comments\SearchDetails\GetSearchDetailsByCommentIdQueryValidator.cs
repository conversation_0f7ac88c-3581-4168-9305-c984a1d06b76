﻿using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Queries.Comments.SearchDetails
{
    [UsedImplicitly]
    public class GetSearchDetailsByCommentIdQueryValidator : AbstractValidator<GetSearchDetailsByCommentIdQueryRequest>
    {
        public GetSearchDetailsByCommentIdQueryValidator()
        {
            RuleFor(x => x.CommunicationId)
                .NotEmpty()
                .GreaterThan(0);

            RuleFor(x => x.CommentId)
               .NotEmpty()
               .GreaterThan(0);
        }
    }
}
