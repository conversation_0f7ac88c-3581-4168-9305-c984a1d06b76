﻿using System.ComponentModel.DataAnnotations;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.ProductExtensions;

namespace Axon.HAComms.Application.Models.Products;

public class ProductRequestModel(
    string name,
    bool isActive,
    ICollection<ProductExtensionModel> productExtensions,
    List<int> drugSubstanceIds,
    List<int> productTypeIds,
    string notAssigned = default!)
    : IProductRequest
{
    [Required]
    public string Name { get; } = name;

    public bool IsActive { get; } = isActive;

    public List<int> ProductTypeIds { get; } = productTypeIds;

    public List<int> DrugSubstanceIds { get; set; } = drugSubstanceIds;

    public ICollection<ProductExtensionModel> ProductExtensions { get; set; } = productExtensions;

    // Only needed for checking for PCID duplicates
    public string NotAssigned { get; set; } = notAssigned;
}
