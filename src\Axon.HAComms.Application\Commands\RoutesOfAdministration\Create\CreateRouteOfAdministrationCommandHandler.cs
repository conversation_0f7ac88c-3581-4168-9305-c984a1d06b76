﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;

internal class CreateRouteOfAdministrationCommandHandler(
    IRouteOfAdministrationRepository repoRoutesOfAdministration,
    IMapper mapper,
    ILogger<CreateRouteOfAdministrationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<CreateRouteOfAdministrationCommandRequest, CreateRouteOfAdministrationCommandResponse>
{
    public async Task<CreateRouteOfAdministrationCommandResponse> Handle(CreateRouteOfAdministrationCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = new RouteOfAdministration();
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.ROUTE_OF_ADMINISTRATION_CREATED, AuditEventCategory.ROUTES_OF_ADMINISTRATION, AuditEventDescription.ROUTE_OF_ADMINISTRATION_CREATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                repoRoutesOfAdministration.AddItem(entity);
                await repoRoutesOfAdministration.SaveChangesAsync(userProvider);
                logger.LogInformation("Route Of Administration {Route} added successfully.", entity.Name);
            });

        return mapper.Map<CreateRouteOfAdministrationCommandResponse>(entity);
    }
}
