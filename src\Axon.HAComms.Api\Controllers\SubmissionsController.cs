﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Queries.Submissions.ListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Submissions")]
public class SubmissionsController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all submission numbers
    /// </summary>
    [HttpGet("submission-numbers", Name = "GetAllSubmissionNumbers")]
    //[HasPermissions(nameof(HacommsPermissions.ViewSubmissionNumber))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<string>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllSubmissionNumbersAsync() =>
        await Send(new GetSubmissionNumberListQueryRequest());

}
