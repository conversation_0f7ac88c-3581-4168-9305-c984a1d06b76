using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Application.Models.Search;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Application.Queries.Comments.SearchDetails;

internal class GetSearchDetailsByCommentIdQueryHandler(
    ICommunicationsRepository communicationsRepository,
    ICommentsRepository commentsRepository,
    IMapper mapper,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IAuditService auditService)
    : IRequestHandler<GetSearchDetailsByCommentIdQueryRequest, SearchDetailsModel>
{
    public async Task<SearchDetailsModel> Handle(GetSearchDetailsByCommentIdQueryRequest request, CancellationToken cancellationToken)
    {
        var communication = await communicationsRepository.GetItemAsync(request.CommunicationId);
        var result = mapper.Map<SearchDetailsModel>(communication);

        var comment = await commentsRepository.GetItemWithAllIncludesAsync(request.CommentId);
        result.Comment = mapper.Map<CommentDtoModel>(comment);

#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        var comments = await commentsRepository.GetFilteredComments(
                                                    include: x => x.Include(c => c.ProductExtensions)
                                                                    .ThenInclude(c => c.Product),
                                                    filter: c => c.CommunicationId == request.CommunicationId)
                                                .AsNoTracking()
                                                .ToArrayAsync(cancellationToken: cancellationToken);
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.

        result.ContainsGeneralGuidanceComments = Array.Exists(comments, c => c.IsGeneralGuidance);
        var commentProducts = comments.Where(c => !c.IsGeneralGuidance && c.ProductExtensions != null).Select(x =>
            x.ProductExtensions!.Count > 0
                ? new ProductDtoModel(x.ProductExtensions.First().Product.Id, x.ProductExtensions.First().Product.Name)
                : new ProductDtoModel(0, string.Empty)).DistinctBy(x => x.Id).ToList();
        result.AllProducts = commentProducts;

        auditService.Log(
           correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
           AuditEventType.SEARCH_RESULT_VIEWED, AuditEventCategory.SEARCH, AuditEventDescription.SEARCH_OPEN_RESULT, result);

        return result;
    }
}
