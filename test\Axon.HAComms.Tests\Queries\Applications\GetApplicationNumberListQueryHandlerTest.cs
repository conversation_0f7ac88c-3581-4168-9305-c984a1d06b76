﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Queries.Applications.ListQuery;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Applications
{
    public class GetApplicationNumberListQueryHandlerTest
    {
        private readonly GetApplicationNumberListQueryHandler handler;
        private readonly IApplicationsRepository applicationsRepository;

        public GetApplicationNumberListQueryHandlerTest()
        {
            applicationsRepository = Substitute.For<IApplicationsRepository>();
            handler = new GetApplicationNumberListQueryHandler(applicationsRepository);
        }

        [Fact]
        public async Task Handle_ListRequest_ReturnsCorrectItems()
        {
            //Arrange
            var queryableItems = new List<string>
            {
                Fake.Application.Number,
                Fake.Application.Number,
                Fake.Application.Number
            };

            applicationsRepository.GetNumbersAsync().Returns(queryableItems);

            var request = new GetApplicationNumberListQueryRequest();

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().HaveCount(3);
            result.Data.Should().Contain(queryableItems[0]);
            result.Data.Should().Contain(queryableItems[1]);
            result.Data.Should().Contain(queryableItems[2]);
        }

        [Fact]
        public async Task Handle_ListRequest_ReturnsCorrectOrder()
        {
            //Arrange
            var queryableItems = new List<string>
            {
                "test3",
                "test2",
                "test1"
            };

            applicationsRepository.GetNumbersAsync().Returns(queryableItems);

            var request = new GetApplicationNumberListQueryRequest();

            // Act
            var result = await handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.Should().HaveCount(3);
            result.Data.Should().BeInDescendingOrder();
        }
    }
}
