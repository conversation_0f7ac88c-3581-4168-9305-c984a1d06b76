﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Queries.Submissions.ListQuery;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Submissions;

public class GetSubmissionNumberListQueryHandlerTest
{
    private readonly GetSubmissionNumberListQueryHandler handler;
    private readonly ISubmissionsRepository submissionsRepository;

    public GetSubmissionNumberListQueryHandlerTest()
    {
        submissionsRepository = Substitute.For<ISubmissionsRepository>();
        handler = new GetSubmissionNumberListQueryHandler(submissionsRepository);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = new List<string>
        {
            Fake.Submission.Number,
            Fake.Submission.Number,
            Fake.Submission.Number
        };

        submissionsRepository.GetNumbersAsync().Returns(queryableItems);

        var request = new GetSubmissionNumberListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().Contain(queryableItems[0]);
        result.Data.Should().Contain(queryableItems[1]);
        result.Data.Should().Contain(queryableItems[2]);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = new List<string>
        {
            "test3",
            "test2",
            "test1"
        };

        submissionsRepository.GetNumbersAsync().Returns(queryableItems);

        var request = new GetSubmissionNumberListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInDescendingOrder();
    }
}
