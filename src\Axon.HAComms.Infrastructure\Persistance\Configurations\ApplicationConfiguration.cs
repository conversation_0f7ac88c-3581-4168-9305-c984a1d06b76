﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ApplicationEntity = Axon.HAComms.Domain.Entities.Application;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ApplicationConfiguration : IEntityTypeConfiguration<ApplicationEntity>
{
    public void Configure(EntityTypeBuilder<ApplicationEntity> builder)
    {        
        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
