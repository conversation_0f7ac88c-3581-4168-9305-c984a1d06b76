﻿using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ApplicationEntity = Axon.HAComms.Domain.Entities.Application;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ApplicationConfiguration : BaseEntityConfiguration<ApplicationEntity>
{
    protected override void ConfigureEntity(EntityTypeBuilder<ApplicationEntity> builder)
    {
    }
}
