﻿using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Communications.Complete
{
    [UsedImplicitly]
    public class CompleteCommunicationCommandValidator : AbstractValidator<CompleteCommunicationCommandRequest>
    {
        public CompleteCommunicationCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty();
        }
    }
}
