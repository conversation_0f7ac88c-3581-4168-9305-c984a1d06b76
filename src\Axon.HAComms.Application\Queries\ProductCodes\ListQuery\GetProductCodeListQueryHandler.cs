﻿using Axon.HAComms.Application.Common.Interfaces;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.ProductCodes.ListQuery;

public class GetProductCodeListQueryHandler(IProductExtensionsRepository repo) : IRequestHandler<GetProductCodeListQueryRequest, ApiListResult<string>>
{
    public async Task<ApiListResult<string>> <PERSON>le(GetProductCodeListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetFilteredListAsync(
            x => !string.IsNullOrEmpty(x.PCID) && x.IsActive,
            x => x.PCID);
        return new ApiListResult<string>(entities.OrderBy(x => x));
    }
}
