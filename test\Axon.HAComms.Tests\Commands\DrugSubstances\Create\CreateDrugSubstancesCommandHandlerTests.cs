﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.DrugSubstances.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Create;

public class CreateDrugSubstancesCommandHandlerTests
{
    private readonly CreateDrugSubstanceCommandHandler sut;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public CreateDrugSubstancesCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DrugSubstancesMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<CreateDrugSubstanceCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        sut = new CreateDrugSubstanceCommandHandler(drugSubstancesRepo, mapper, logger,
            correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DRUG_SUBSTANCE_CREATED, AuditEventCategory.DRUG_SUBSTANCES, AuditEventDescription.DRUG_SUBSTANCE_CREATE,
                Arg.Any<DrugSubstance>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsCorrectEntity()
    {
        // Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var request = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.DRUG_SUBSTANCE_CREATED,
                AuditEventCategory.DRUG_SUBSTANCES,
                AuditEventDescription.DRUG_SUBSTANCE_CREATE,
                Arg.Any<DrugSubstance>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Name.Should().Be(request.Name);
        result.Code.Should().Be(request.Code);
        result.Description.Should().Be(request.Description);
    }
}
