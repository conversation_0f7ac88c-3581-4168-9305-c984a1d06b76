using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Communications.Update;

[UsedImplicitly]
public class UpdateCommunicationCommandValidator : AbstractValidator<UpdateCommunicationCommandRequest>
{
    public UpdateCommunicationCommandValidator(ISubmissionTypesRepository subTypeRepo, ICountriesRepository countryRepo)
    {
        RuleFor(x => x.Id)
            .NotEmpty();
        this.AddCommunicationRequestValidation(subTypeRepo, countryRepo);
    }
}
