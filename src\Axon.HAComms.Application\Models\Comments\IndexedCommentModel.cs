﻿using Autofac;
using Azure.Search.Documents.Indexes;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Axon.HAComms.Application.Models.Comments
{
    public class IndexedCommentModel
    {
        [Key] [JsonPropertyName("id")] public required string Id { get; set; }

        [SearchableField(AnalyzerName = "custom-analyzer"), SimpleField(IsSortable = true), JsonPropertyName("description")]
        public string? Description { get; set; }

        [SearchableField(), SimpleField(IsSortable = true), JsonPropertyName("subject")]
        public string? Subject { get; set; }

        [SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("productId")]
        public string? ProductId { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("routeOfAdministrationIds")]
        public ICollection<string>? RouteOfAdministrationIds { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true), JsonPropertyName("productCodes")]
        public ICollection<string>? ProductCodes { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("productCodeNames")]
        public string? ProductCodeNames { get; set; }

        [SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("submissionTypeId")]
        public string? SubmissionTypeId { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true), JsonPropertyName("dateOfCommunication")]
        public DateTime? DateOfCommunication { get; set; }

        [SearchableField(IsSortable = true), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("dosageFormIds")]
        public ICollection<string>? DosageFormIds { get; set; }

        [SearchableField(IsSortable = true), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("drugSubstanceIds")]
        public ICollection<string>? DrugSubstanceIds { get; set; }

        [SearchableField(IsSortable = true), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("tagIds")]
        public ICollection<string>? TagIds { get; set; }

        [SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("countryId")]
        public string? CountryId { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("applicationIds")]
        public ICollection<string>? ApplicationIds { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("submissionIds")]
        public ICollection<string>? SubmissionIds { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true), JsonPropertyName("applicationNumbers")]
        public ICollection<string>? ApplicationNumbers { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("applicationNumberNames")]
        public string? ApplicationNumberNames { get; set; }

        [SearchableField(), SimpleField(IsFilterable = true), JsonPropertyName("submissionNumbers")]
        public ICollection<string>? SubmissionNumbers { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("submissionNumberNames")]
        public string? SubmissionNumberNames { get; set; }

        [SimpleField(), JsonPropertyName("communicationId")]
        public string? CommunicationId { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("productName")]
        public string? ProductName { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("countryName")]
        public string? CountryName { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("drugSubstanceNames")]
        public string? DrugSubstanceNames { get; set; }

        [SimpleField(IsSortable = true, IsFilterable = true), JsonPropertyName("isGeneralGuidance")]
        public bool? IsGeneralGuidance { get; set; }

        [SimpleField(IsSortable = true, IsFilterable = true), JsonPropertyName("isQuestionIncluded")]
        public bool? IsQuestionIncluded { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("dosageFormNames")]
        public string? DosageFormNames { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("routeOfAdministrationNames")]
        public string? RouteOfAdministrationNames { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("submissionTypeName")]
        public string? SubmissionTypeName { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("tagNames")]
        public string? TagNames { get; set; }

        [SimpleField(IsSortable = true, IsFilterable = true, IsHidden = true), JsonPropertyName("tenant")]
        public string? Tenant { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("birdsLinkToBIResponse")]
        public string? BIRDSLinkToBIResponse { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("birdsLinkToBISAMP")]
        public string? BIRDSLinkToBISAMP { get; set; }

        [SearchableField(AnalyzerName = "custom-analyzer"), SimpleField(IsSortable = true), JsonPropertyName("question")]
        public string? Question { get; set; }

        [SearchableField(AnalyzerName = "custom-analyzer"), SimpleField(IsSortable = true), JsonPropertyName("response")]
        public string? Response { get; set; }

        [SearchableField(IsSortable = true), SimpleField(IsFilterable = true, IsHidden = true), JsonPropertyName("productTypeIds")]
        public ICollection<string>? ProductTypeIds { get; set; }

        [SimpleField(IsSortable = true), JsonPropertyName("productTypeNames")]
        public string? ProductTypeNames { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true), JsonPropertyName("LastUpdatedDate")]
        public DateTime? LastUpdatedDate { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true), JsonPropertyName("IsDeleted")]
        public string? IsDeleted { get; set; }
    }
}
