using AutoMapper;
using Axon.HAComms.Application.Commands.Communications.Create;
using Axon.HAComms.Application.Commands.Communications.Update;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Models.Search;
using Axon.HAComms.Domain.Entities;
using CommunicationEntity = Axon.HAComms.Domain.Entities.Communication;

namespace Axon.HAComms.Application.Common.Mappings;

public class CommunicationsMappingProfile : Profile
{
    public CommunicationsMappingProfile()
    {
        CreateMap<CommunicationEntity, CommunicationModel>()
            .ForMember(dest => dest.Products, o => o.Ignore())
            .ForMember(dest => dest.GeneralGuidanceCommentsCount, o => o.Ignore());
        CreateMap<CommunicationEntity, SearchDetailsModel>()
            .ForMember(dest => dest.Country, o => o.MapFrom(src => src.Country != null ? src.Country.Name : string.Empty))
            .ForMember(dest => dest.SubmissionType, o => o.MapFrom(src => src.SubmissionType != null ? src.SubmissionType.Name : string.Empty))
            .ForMember(dest => dest.Comment, o => o.Ignore())
            .ForMember(dest => dest.ContainsGeneralGuidanceComments, o => o.Ignore())
            .ForMember(dest => dest.AllProducts, o => o.Ignore());
        CreateMap<CommunicationsView, CommunicationPagedListModel>();
        CreateMap<CommunicationEntity, CreateCommunicationCommandResponse>();
        CreateMap<CreateCommunicationCommandRequest, CommunicationEntity>()
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.Country, o => o.Ignore())
            .ForMember(dest => dest.SubmissionType, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore())
            .ForMember(dest => dest.IsCompleted, o => o.Ignore());
        CreateMap<UpdateCommunicationCommandRequest, CommunicationEntity>()
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.Country, o => o.Ignore())
            .ForMember(dest => dest.SubmissionType, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore())
            .ForMember(dest => dest.IsCompleted, o => o.Ignore());
        CreateMap<CommunicationEntity, UpdateCommunicationCommandResponse>();
    }
}
