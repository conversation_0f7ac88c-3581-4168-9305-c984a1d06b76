﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class ReinstateCommunicationIntegrationTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly HACommsContext dbContext = fixture.DbContext;
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());

    [Fact]
    public async Task ReinstateCommunication_ValidCommunicationId_ReturnsOk()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);
        var communicationId = response.Id;

        await communicationApi.CompleteCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        var communication = await communicationApi.GetCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        communication.Id.Should().Be(communicationId);
        communication.IsCompleted.Should().BeTrue();

        //Act
        await communicationApi.ReinstateCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        communication = await communicationApi.GetCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(communicationId);
        communication.Subject.Should().Be(request.Subject);
        communication.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public async Task ReinstateCommunication_ReinstatedCommunication_ThrowsEntityNotFoundException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);

        //Act
        var reinstateCommunicationResponse = () => communicationApi.ReinstateCommunicationAsync(createCommunicationResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await reinstateCommunicationResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains($"Entity \\\"Communication\\\" ({createCommunicationResponse.Id}) was not found", exception.And.Message);
    }

    [Fact]
    public async Task ReinstateCommunication_InvalidCommunication_ThrowsEntityNotFoundException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var invalidId = Fake.GetRandomInt(createCommunicationResponse.Id + 1, createCommunicationResponse.Id + 50);

        //Act
        var reinstateCommunicationResponse = () => communicationApi.ReinstateCommunicationAsync(invalidId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await reinstateCommunicationResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains($"Entity \\\"Communication\\\" ({invalidId}) was not found", exception.And.Message);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
