﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteCommunicationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteCommunications_ValidCommunicationId_ReturnsOk()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var communication = CommunicationBuilder.Default()
            .WithIsCompleted(false)
            .WithCountryId(country.Id)
            .WithSubmissionTypeId(submissionType.Id)
            .Build();

        dbContext.Communications.Add(communication);
        await dbContext.SaveChangesAsync();

        //Act
        await communicationApi.DeleteCommunicationAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var communicationResponse = () => communicationApi.GetCommunicationAsync(communication.Id, TenantConstants.DEFAULT_TENANT);
        var exception = await communicationResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"Communication\\\" ({communication.Id}) was not found.", exception.And.Message);
    }

    [Fact]
    public async Task DeleteCommunications_InvalidCommunicationId_ThrowsException()
    {
        //Arrange
        var communicationId = Fake.Communication.Id;

        //Act
        var response = () => communicationApi.DeleteCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception =  await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"Communication\\\" ({communicationId}) was not found.", exception.And.Message);
    }

    [Fact]
    public async Task DeleteCommunications_InCompletedState_ThrowsException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var communication = CommunicationBuilder.Default()
            .WithIsCompleted(true)
            .WithCountryId(country.Id)
            .WithSubmissionTypeId(submissionType.Id)
            .Build();

        dbContext.Communications.Add(communication);
        await dbContext.SaveChangesAsync();

        //Act
        var response = () => communicationApi.DeleteCommunicationAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("Cannot delete completed communication!", exception.And.Message);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        await dbContext.SaveChangesAsync();
    }
}
