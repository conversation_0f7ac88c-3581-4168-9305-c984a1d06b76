image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: latest

ingress:
  tls:
    - tlsSecretName: tls-app-eu-smartphlex-com
      hosts:
        - app-eu.smartphlex.com
        - app.smartphlex.com
  hosts:
    - host: app-eu.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)
    - host: app.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)

replicas: 2
minAvailability: 1

keyVaultName: hac-prod-kv-eun
clientId: b07bf619-7ce5-4dd5-8fa6-14fb944f8433 #Smartphlex prodeu

corsOriginUrl0: https://app-eu.smartphlex.com
corsOriginUrl1: https://app.smartphlex.com

azureSearch:
  isEnabled: true
  serviceName: ss-prod-ss-eun
  IndexName: hacomms-index-prodeu
  IndexerName: hacomms-indexer-prodeu
  DataSourceName: hacomms-db-prodeu
  Interval: 60

azureWorkload:
  appName: axon-hacomms-prod
  clientId: a2a1c66b-ebcf-4aee-93a6-c3e8d4552792
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AppScope: "api://smartphlex-prodeu/.default"
ApiHost: "https://app-eu.smartphlex.com/api/core"
GrpcHost: "http://axon-core-api-grpc.axon-core-prod.svc.cluster.local:9090"

DataProtectionBlobStorageUri: 'https://axnprodstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-prod-kv-eun.vault.azure.net/keys/AxonDataProtection'