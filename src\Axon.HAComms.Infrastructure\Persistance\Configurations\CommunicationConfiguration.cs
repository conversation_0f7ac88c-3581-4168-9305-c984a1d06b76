﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class CommunicationConfiguration : BaseEntityConfiguration<Communication>
{
    protected override void ConfigureEntity(EntityTypeBuilder<Communication> builder)
    {
        builder.ToTable("Communications");
    }
}
