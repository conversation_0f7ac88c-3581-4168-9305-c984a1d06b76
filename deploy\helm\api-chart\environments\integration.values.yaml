image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: dev

ingress:
  tls:
    - tlsSecretName: **********************************
      hosts:
        - app-integration.smartphlex.com
  hosts:
    - host: app-integration.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)

replicas: 1
minAvailability: 0

keyVaultName: hac-int-kv-eun
clientId: 38a74d6a-86ad-460a-997e-d6b3384eba63 #Smartphlex int

corsOriginUrl0: https://app-integration.smartphlex.com
corsOriginUrl1: https://localhost:4070

azureSearch:
  isEnabled: true
  serviceName: ss-nonprod-ss-eun
  IndexName: hacomms-index-int
  IndexerName: hacomms-indexer-int
  DataSourceName: hacomms-db-int

azureWorkload:
  appName: axon-hacomms-int
  clientId: 1e20a901-473c-4b77-baac-030cbe7649a5
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AppScope: "api://smartphlex-int/.default"
ApiHost: "https://app-integration.smartphlex.com/api/core"
GrpcHost: "http://axon-core-api-grpc.axon-core-int.svc.cluster.local:9090"

DataProtectionBlobStorageUri: 'https://axnintstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-int-kv-eun.vault.azure.net/keys/AxonDataProtection'