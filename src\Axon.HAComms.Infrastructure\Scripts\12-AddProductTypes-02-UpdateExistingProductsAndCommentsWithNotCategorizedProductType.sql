INSERT INTO ProductProductTypes (ProductTypeId, ProductId, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
SELECT PT.Id as ProductTypeId, DP.Id as ProductId, DP.CreatedDate, DP.CreatedBy, DP.LastUpdatedDate, DP.LastUpdatedBy, DP.IsDeleted FROM DrugProducts as DP
JOIN ProductTypes as PT ON 1 = 1
WHERE PT.Name like 'Not Categorized'

INSERT INTO CommentProductTypes (ProductTypeId, CommentId, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
SELECT PT.Id as ProductTypeId, C.Id as CommentId, C.CreatedDate, C.<PERSON>, C.LastUpdatedDate, C.LastUpdatedBy, C.IsDeleted FROM Comments as C
JOIN ProductTypes as PT ON 1 = 1
WHERE PT.Name like 'Not Categorized'