﻿using AutoMapper;
using Axon.HAComms.Application.Commands.DosageForms.Create;
using Axon.HAComms.Application.Commands.DosageForms.Update;
using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class DosageFormsMappingProfile : Profile
    {
        public DosageFormsMappingProfile()
        {
            CreateMap<DosageForm, DosageFormModel>();
            CreateMap<DosageFormModel, DosageForm>()
                .ForMember(dest => dest.Tenant, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.ProductExtensions, o => o.Ignore());
            CreateMap<CreateDosageFormCommandRequest, DosageForm>()
                .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<DosageForm, CreateDosageFormCommandResponse>();
            CreateMap<UpdateDosageFormCommandRequest, DosageForm>()
               .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
               .ForMember(dest => dest.IsDeleted, o => o.Ignore())
               .ForMember(dest => dest.CreatedDate, o => o.Ignore())
               .ForMember(dest => dest.CreatedBy, o => o.Ignore())
               .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
               .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
               .ForMember(dest => dest.ExternalId, o => o.Ignore())
               .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<DosageForm, UpdateDosageFormCommandResponse>();
        }
    }
}
