services:
  axon-hacomms-api:
    image: axon-hacomms-api:${VERSION_NO}
    build:
      context: .
      dockerfile: "src/Axon.HAComms.Api/Dockerfile"
      target: "final"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
        lastCommitHash: ${LAST_COMMIT_HASH}
    networks:
      - overlay

  axon-hacomms-api-tests:
    image: axon-hacomms-api-tests:${VERSION_NO}
    build:
      context: .
      dockerfile: "src/Axon.HAComms.Api/Dockerfile"
      target: "testrunner"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
    depends_on:
      - sql
    networks:
      - overlay

  sql:
    image: "mcr.microsoft.com/mssql/server:2022-latest"
    container_name: sql_server2022
    ports:
      - "1433:1433"
    environment:
      - ACCEPT_EULA=y
      - MSSQL_SA_PASSWORD=qvnKfW?^23k69WH#
    networks:
      - overlay
    healthcheck:
        test: ["CMD", "/opt/mssql-tools/bin/sqlcmd", "-Usa", "-P", "qvnKfW?^23k69WH#", "-Q", "select 1"]
        interval: 1s
        retries: 20

networks:
  overlay:
