﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class CountriesRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<CountriesRepository> logger)
    : SqlServerRepository<Country>(context, tenant, logger), ICountriesRepository
{
    public async Task<IEnumerable<Country>> GetItemsAsync()
    {
        return await context
            .Set<Country>()
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<bool> ExistsAsync(Expression<Func<Country, bool>> filter)
    {
        return await context.Set<Country>().AsNoTracking().AnyAsync(filter);
    }
}
