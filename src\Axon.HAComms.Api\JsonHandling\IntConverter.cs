﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Axon.HAComms.Api.JsonHandling
{
    public class IntConverter : JsonConverter<int>
    {
        public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetInt32();
            }
            else if (reader.TokenType is JsonTokenType.String)
            {
                if (int.TryParse(reader.GetString(), out int number))
                {
                    return number;
                }
                else
                {
                    throw new JsonException("Invalid int format. Please provide number in valid format.");
                }
            }

            throw new JsonException("Invalid JSON token type for int.");
        }

        public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
        {
            writer.WriteNumberValue(value);
        }
    }
}
