﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Countries;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetCountryListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CountriesApi api = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetCountryList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentCountriesCount = dbContext.Countries.Count();
        var country1 = new Country() { Name = Fake.Country.Name };
        var country2 = new Country() { Name = Fake.Country.Name };
        var country3 = new Country() { Name = Fake.Country.Name };
        await dbContext.Countries.AddRangeAsync(country1, country2, country3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await api.GetCountryListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentCountriesCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(country1.Name);
        responseNames.Should().Contain(country2.Name);
        responseNames.Should().Contain(country3.Name);

        dbContext.Countries.RemoveRange(country1, country2, country3);
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public Task DisposeAsync() => Task.CompletedTask;
}
