﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DrugSubstances;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateDrugSubstancesIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DrugSubstancesApi apiDrugSubstances = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateDrugSubstances_ValidRequest_ReturnsOk()
    {
        //Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;
        var requestObj1 = new CreateDrugSubstanceCommandRequest(Fake.DrugSubstance.Name, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);

        responseObj1.Should().NotBeNull();
        var requestObj2 = new UpdateDrugSubstanceCommandRequest(responseObj1.Id, name: drugSubstanceName, code: drugSubstanceCode, description: drugSubstanceDescription);

        await apiDrugSubstances.UpdateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, requestObj2);
        var responseObj = await apiDrugSubstances.GetDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        var sut = responseObj.Data.SingleOrDefault(s => s.Id == responseObj1.Id);
        sut.Should().NotBeNull();
        sut?.Name.Should().Be(requestObj2.Name);
        sut?.Code.Should().Be(requestObj2.Code);
        sut?.Description.Should().Be(requestObj2.Description);
    }

    [Fact]
    public async Task UpdateDrugSubstances_WithDuplicateCode_ThrowsAlreadyExistsException()
    {
        //Arrange
        var drugSubstanceCode1 = Fake.DrugSubstance.Code;
        var drugSubstanceCode2 = Fake.DrugSubstance.Code;

        var requestObj1 = new CreateDrugSubstanceCommandRequest(Fake.DrugSubstance.Name, drugSubstanceCode1, Fake.DrugSubstance.Description);
        var requestObj2 = new CreateDrugSubstanceCommandRequest(Fake.DrugSubstance.Name, drugSubstanceCode2, Fake.DrugSubstance.Description);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);
        var responseObj2 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        //Act
        var requestObj3 = new UpdateDrugSubstanceCommandRequest(responseObj1.Id, Fake.DrugSubstance.Name, drugSubstanceCode2, Fake.DrugSubstance.Description);

        var drugSubstanceResponse = () => apiDrugSubstances.UpdateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, updateDrugSubstanceCommandRequest: requestObj3);

        //Assert
        var exception = await drugSubstanceResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("AlreadyExists");
    }

    [Fact]
    public async Task UpdateDrugSubstances_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var drugSubstanceName1 = Fake.DrugSubstance.Name;
        var drugSubstanceName2 = Fake.DrugSubstance.Name;

        var requestObj1 = new CreateDrugSubstanceCommandRequest(drugSubstanceName1, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);
        var requestObj2 = new CreateDrugSubstanceCommandRequest(drugSubstanceName2, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);
        var responseObj2 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        //Act
        var requestObj3 = new UpdateDrugSubstanceCommandRequest(responseObj1.Id, drugSubstanceName2, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);

        var drugSubstanceResponse = () => apiDrugSubstances.UpdateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, updateDrugSubstanceCommandRequest: requestObj3);

        //Assert
        var exception = await drugSubstanceResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("AlreadyExists");
    }

    [Fact]
    public async Task UpdateDrugSubstances_NonExistingEntity_ThrowsException()
    {
        //Arrange
        var drugSubstanceId = Fake.DrugSubstance.Id;
        var requestObj = new UpdateDrugSubstanceCommandRequest(drugSubstanceId, Fake.DrugSubstance.Name, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);

        //Act
        var response = () => apiDrugSubstances.UpdateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, updateDrugSubstanceCommandRequest: requestObj);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"DrugSubstance\\\" ({drugSubstanceId}) was not found.", exception.And.Message);
    }

    [Fact]
    public async Task UpdateDrugSubstances_EmptyName_ReturnsOk()
    {
        //Arrange
        var requestObj1 = new CreateDrugSubstanceCommandRequest(string.Empty, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);
        var requestObj2 = new CreateDrugSubstanceCommandRequest(Fake.DrugSubstance.Name, Fake.DrugSubstance.Code, Fake.DrugSubstance.Description);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);
        var responseObj2 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        var requestObj3 = new UpdateDrugSubstanceCommandRequest(responseObj1.Id, name: string.Empty, code: Fake.DrugSubstance.Code, description: Fake.DrugSubstance.Description);
        await apiDrugSubstances.UpdateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, requestObj3);

        var responseObj3 = await apiDrugSubstances.GetDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj3.Should().NotBeNull();
        var sut = responseObj3.Data.SingleOrDefault(s => s.Id == responseObj1.Id);
        sut.Should().NotBeNull();
        sut?.Name.Should().Be(Constants.NotAssigned);
        sut?.Code.Should().Be(requestObj3.Code);
        sut?.Description.Should().Be(requestObj3.Description);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        await dbContext.SaveChangesAsync();
    }
}
