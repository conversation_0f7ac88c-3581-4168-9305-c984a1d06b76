using Autofac.Multitenant;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Domain.Constants;
using MediatR;

namespace Axon.HAComms.Application.Commands.Comments.Search;

internal class SearchCommentCommandHandler(
    IAzureSearchHelper searchHelper,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IAuditService auditService,
    ITenantIdentificationStrategy tenantIdentificationStrategy) : IRequestHandler<SearchCommentCommandRequest, SearchCommentCommandResponse>
{
    public async Task<SearchCommentCommandResponse> Handle(SearchCommentCommandRequest request, CancellationToken cancellationToken)
    {
        var commentsFilter = string.Empty;
        tenantIdentificationStrategy.TryIdentifyTenant(out var tenant);

        commentsFilter = commentsFilter
            .AddGreaterThanFilter(request.StartDate, "dateOfCommunication")
            .AddLessThanFilter(request.EndDate, "dateOfCommunication")
            .AddSearchInFilter(request.Products?.Select(x => x.Id).ToList() ?? [], "productId")
            .AddSearchInFilter(request.Countries?.Select(x => x.Id).ToList() ?? [], "countryId")
            .AddSearchInFilter(request.SubmissionTypes?.Select(x => x.Id).ToList() ?? [], "submissionTypeId")
            .AddAnySearchInFilter(request.Tags?.Select(x => x.Id).ToList() ?? [], "tagIds")
            .AddAnySearchInFilter(request.ProductCodes ?? [], "productCodes")
            .AddAnySearchInFilter(request.DosageForms?.Select(x=>x.Id).ToList() ?? [], "dosageFormIds")
            .AddAnySearchInFilter(request.RoutesOfAdministration?.Select(x => x.Id).ToList() ?? [], "routeOfAdministrationIds")
            .AddAnySearchInFilter(request.DrugSubstances?.Select(x => x.Id).ToList() ?? [], "drugSubstanceIds")
            .AddAnySearchInFilter(request.ApplicationNumbers ?? [], "applicationNumbers")
            .AddAnySearchInFilter(request.SubmissionNumbers ?? [], "submissionNumbers")
            .AddAnySearchInFilter(request.ProductTypes?.Select(x => x.Id).ToList() ?? [], "productTypeIds")
            .AddEqualFilter(request.IsGeneralGuidance, "isGeneralGuidance")
            .AddEqualFilter(request.IsQuestionIncluded, "isQuestionIncluded")
            .AddEqualFilter(tenant, "tenant");

        string[] returnFields =
        [
            "description", "productCodes", "productCodeNames", "dateOfCommunication", "productName", "countryName", "drugSubstanceNames", "id", "communicationId",
            "applicationNumbers", "applicationNumberNames", "submissionNumbers", "submissionNumberNames", "dosageFormNames", "routeOfAdministrationNames",
            "submissionTypeName", "tagNames",
            "productTypeNames", "isQuestionIncluded", "isGeneralGuidance", "birdsLinkToBIResponse", "birdsLinkToBISAMP", "question", "response"
        ];

        var textSearchResults = await searchHelper.TextSearchAsync<IndexedCommentModel>(
            searchText: request.SearchText?.Trim().EscapeSpecialCharacters() ?? string.Empty,
            skip: request.Skip,
            take: request.Take,
            sort: request.Sort,
            selectFields: returnFields,
            filter: commentsFilter,
            fuzzy: request.Fuzzy ?? false);

        
        var results = textSearchResults.GetResults();

        var auditEventType = request.RequestType switch
        {
            AuditEventSource.SEARCH_LIST => AuditEventType.SEARCH_EXECUTED,
            AuditEventSource.SEARCH_EXPORT => AuditEventType.SEARCH_FILE_EXPORTED,
            _ => AuditEventType.SEARCH_EXECUTED
        };

        var auditEventDescription = request.RequestType switch
        {
            AuditEventSource.SEARCH_LIST => AuditEventDescription.SEARCH_EXECUTE,
            AuditEventSource.SEARCH_EXPORT => AuditEventDescription.SEARCH_FILE_EXPORT,
            _ => AuditEventDescription.SEARCH_EXECUTE
        };

        var searchFilters = new
        {
            request.StartDate,
            request.EndDate,
            Products = request.Products != null ? string.Join(", ", request.Products.Select(x => x.Name)) : string.Empty,
            Countries = request.Countries != null ? string.Join(", ", request.Countries.Select(x => x.Name)) : string.Empty,
            SubmissionTypes = request.SubmissionTypes != null ? string.Join(", ", request.SubmissionTypes.Select(x => x.Name)) : string.Empty,
            Tags = request.Tags != null ? string.Join(", ", request.Tags.Select(x => x.Name)) : string.Empty,
            DosageForms = request.DosageForms != null ? string.Join(", ", request.DosageForms.Select(x => x.Name)) : string.Empty,
            RoutesOfAdministration = request.RoutesOfAdministration != null ? string.Join(", ", request.RoutesOfAdministration.Select(x => x.Name)) : string.Empty,
            DrugSubstances = request.DrugSubstances != null ? string.Join(", ", request.DrugSubstances.Select(x => x.Name)) : string.Empty,
            ProductTypes = request.ProductTypes != null ? string.Join(", ", request.ProductTypes.Select(x => x.Name)) : string.Empty,
            request.ProductCodes,
            request.ApplicationNumbers,
            request.SubmissionNumbers,
            request.IsGeneralGuidance,
            request.IsQuestionIncluded,
            request.Fuzzy,
            request.SearchText,
            tenant,
            TotalResults = (int?)textSearchResults.TotalCount,
        };

        auditService.Log(correlationIdProvider.Provide(), clientDetailsProvider.Provide(), auditEventType, AuditEventCategory.SEARCH, auditEventDescription, searchFilters);

        return new SearchCommentCommandResponse
        {
            Comments = results?.Select(x => new SearchComment
            {
                CommentId = Convert.ToInt32(x.Document.Id),
                CommunicationId = Convert.ToInt32(x.Document.CommunicationId),
                Description = x.Document.Description,
                ProductCodes = !string.IsNullOrEmpty(x.Document.ProductCodeNames) ? string.Join(", ", x.Document.ProductCodeNames) : null,
                DateOfCommunication = x.Document.DateOfCommunication,
                CountryName = x.Document.CountryName,
                ProductName = x.Document.ProductName,
                DrugSubstanceNames = x.Document.DrugSubstanceNames,
                ApplicationNumberNames =
                    !string.IsNullOrEmpty(x.Document.ApplicationNumberNames)
                        ? string.Join(", ", x.Document.ApplicationNumberNames.Split(", ").Distinct())
                        : null,
                SubmissionNumberNames = x.Document.SubmissionNumberNames,
                DosageFormNames =
                    !string.IsNullOrEmpty(x.Document.DosageFormNames) ? string.Join(" | ", x.Document.DosageFormNames.Split("| ").Distinct()) : "",
                RouteOfAdministrationNames =
                    !string.IsNullOrEmpty(x.Document.RouteOfAdministrationNames)
                        ? string.Join(", ", x.Document.RouteOfAdministrationNames.Split(", ").Distinct())
                        : "",
                SubmissionTypeName = x.Document.SubmissionTypeName,
                TagNames = x.Document.TagNames,
                ProductTypeNames =
                    !string.IsNullOrEmpty(x.Document.ProductTypeNames) ? string.Join(", ", x.Document.ProductTypeNames.Split(", ").Distinct()) : null,
                IsQuestionIncluded = x.Document.IsQuestionIncluded,
                IsGeneralGuidance = x.Document.IsGeneralGuidance,
                BIRDSLinkToBIResponse = x.Document.BIRDSLinkToBIResponse,
                BIRDSLinkToBISAMP = x.Document.BIRDSLinkToBISAMP,
                Question = x.Document.Question,
                Response = x.Document.Response
            }),
            Total = (int?)textSearchResults.TotalCount,
        };
    }
}
