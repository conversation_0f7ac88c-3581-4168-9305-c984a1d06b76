﻿using Autofac.Multitenant;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using NSubstitute;
using Xunit;
using Axon.HAComms.Application.Commands.Comments.Search;
using Axon.HAComms.Application.Models.Comments;
using Azure.Search.Documents.Models;
using Azure.Search.Documents;
using Azure;
using Azure.Search.Documents.Indexes;
using Microsoft.Extensions.Options;
using Axon.HAComms.Application.Helpers;
using Microsoft.Extensions.Logging;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Application.Models.SubmissionType;
using Axon.HAComms.Application.Models.Tags;
using Axon.HAComms.Application.Models.ProductType;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Tests.Builders.Country;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Builders.Tags;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using Axon.HAComms.Tests.Builders.SubmissionType;
using Axon.HAComms.Tests.Builders.ProductTypes;
using Axon.HAComms.Tests.Builders.Products;

namespace Axon.HAComms.Tests.Commands.Comments.Search;

public class SearchCommentCommandHandlerTests
{
    private readonly SearchCommentCommandHandler handler;
    private readonly SearchClient searchClient;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public SearchCommentCommandHandlerTests()
    {
        searchClient = Substitute.For<SearchClient>();
        var searchIndexClient = Substitute.For<SearchIndexClient>();
        var searchIndexerClient = Substitute.For<SearchIndexerClient>();
        var option = Substitute.For<IOptionsMonitor<SearchSettings>>();
        var logger = Substitute.For<ILogger<AzureSearchHelper>>();

        IAzureSearchHelper helper = new AzureSearchHelper(searchClient, searchIndexClient, searchIndexerClient, option, logger);

        correlationId = Guid.NewGuid();
        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider.Provide().Returns(correlationId);

        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");
        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider.Provide().Returns(clientDetails);

        var tenantIdentificationStrategy = Substitute.For<ITenantIdentificationStrategy>();
        auditService = Substitute.For<IAuditService>();

        handler = new SearchCommentCommandHandler(helper, correlationIdProvider, clientDetailsProvider, auditService, tenantIdentificationStrategy);
    }

    [Fact]
    public async Task Handle_WithStartAndEndDatesAsDateOfCommunication_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            DateTime.UtcNow,
            DateTime.UtcNow,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_SearchWithFilters_LogsAuditForSearch()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            DateTime.UtcNow,
            DateTime.UtcNow,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            Fake.GetRandomString(20),
            null,
            null,
            0,
            10,
            string.Empty,
            true,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, new {});
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.SEARCH_EXECUTED, AuditEventCategory.SEARCH, AuditEventDescription.SEARCH_EXECUTE, Arg.Any<dynamic>());
    }

    [Fact]
    public async Task Handle_SearchWithFilters_LogsAuditForExport()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            DateTime.UtcNow,
            DateTime.UtcNow,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            Fake.GetRandomString(20),
            null,
            null,
            0,
            10,
            string.Empty,
            true,
            AuditEventSource.SEARCH_EXPORT);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, new { });
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.SEARCH_FILE_EXPORTED, AuditEventCategory.SEARCH, AuditEventDescription.SEARCH_FILE_EXPORT, Arg.Any<dynamic>());
    }

    [Fact]
    public async Task Handle_WithProductIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            TestEntitiesGenerator<ProductDtoModel, ProductDtoModelBuilder>.Generate(2),
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithDosageFormIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            TestEntitiesGenerator<DosageFormModel, DosageFormModelBuilder>.Generate(3),
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithRouteOfAdministrationIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            TestEntitiesGenerator<RouteOfAdministrationModel, RouteOfAdministrationModelBuilder>.Generate(3),
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithTagIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            TestEntitiesGenerator<TagModel, TagModelBuilder>.Generate(3),
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithProductTypeIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            TestEntitiesGenerator<ProductTypeModel, ProductTypeModelBuilder>.Generate(2),
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithGeneralGuidance_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            true,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithQuestionIncluded_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            null,
            true,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithDrugSubstanceId_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            TestEntitiesGenerator<DrugSubstanceDtoModel, DrugSubstanceDtoModelBuilder>.Generate(2),
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithCountryIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            TestEntitiesGenerator<CountryModel, CountryModelBuilder>.Generate(3),
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithSubmissionTypeIds_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            TestEntitiesGenerator<SubmissionTypeModel, SubmissionTypeModelBuilder>.Generate(3),
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithApplicationNumbers_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [Fake.Application.Number, Fake.Application.Number, Fake.Application.Number],
            [],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithSubmissionNumbers_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [Fake.Submission.Number, Fake.Submission.Number, Fake.Submission.Number],
            [],
            [],
            [],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithProductCodes_ReturnsSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [Fake.ProductExtension.PCID, Fake.ProductExtension.PCID, Fake.ProductExtension.PCID],
            string.Empty,
            false,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }

    [Fact]
    public async Task Handle_WithEmptyFilters_ReturnsAllSearchResults()
    {
        // Arrange
        var (request, searchResults) = SetupSearchData(
            null,
            null,
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            [],
            string.Empty,
            null,
            null,
            0,
            10,
            string.Empty,
            false,
            AuditEventSource.SEARCH_LIST);

        searchClient.SearchAsync<IndexedCommentModel>(Arg.Any<string>(), Arg.Any<SearchOptions>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(Response.FromValue(searchResults, Substitute.For<Response>())));

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Comments?.Count().Should().Be((int?)searchResults.TotalCount);
    }
   
    [Fact]
    public void ConvertToFuzzySearchString_ReturnsStringWithTilde()
    {
        // Arrange
        List<string> searchTerms = ["test", "test with other words", "test with tilde at the end~", "  test  with  double  spaces  "];
        var convertedSearchTerms = new List<string>();

        // Double Tilde does not influence the search results
        List<string> resultTerms = ["test~", "test~ with~ other~ words~", "test~ with~ tilde~ at~ the~ end~~", "~ ~ test~ ~ with~ ~ double~ ~ spaces~ ~ ~"];


        // Act
        foreach (var term in searchTerms)
        {
            convertedSearchTerms.Add(AzureSearchHelper.ConvertToFuzzySearchString(term));
        }

        // Assert
        convertedSearchTerms.Should().NotBeNull();
        convertedSearchTerms.Should().Equal(resultTerms);
    }

    private static (SearchCommentCommandRequest, SearchResults<IndexedCommentModel>) SetupSearchData(
        DateTime? startDate,
        DateTime? endDate,
        List<ProductDtoModel> products,
        List<DosageFormModel> dosageForms,
        List<RouteOfAdministrationModel> routeOfAdministrations,
        List<DrugSubstanceDtoModel> drugSubstances,
        List<CountryModel> countries,
        List<SubmissionTypeModel> submissionTypes,
        List<string> applicationNumbers,
        List<string> submissionNumbers,
        List<TagModel> tags,
        List<ProductTypeModel> productTypes,
        List<string> productCodes,
        string searchText,
        bool? isGeneralGuidance,
        bool? isQuestionIncluded,
        int skip,
        int take,
        string sort,
        bool fuzzy,
        string requestType)
    {
        var request = new SearchCommentCommandRequest(startDate,
            endDate,
            products,
            dosageForms,
            routeOfAdministrations,
            drugSubstances,
            countries,
            submissionTypes,
            applicationNumbers,
            submissionNumbers,
            tags,
            productTypes,
            productCodes,
            searchText,
            isGeneralGuidance,
            isQuestionIncluded,
            skip,
            take,
            sort,
            fuzzy,
            requestType);

        var searchResults = SearchModelFactory.SearchResults(new[]
        {
            SearchModelFactory.SearchResult(new IndexedCommentModel()
            {
                Id = Fake.Comment.Id.ToString(),
                Description = Fake.Comment.Description,
                IsGeneralGuidance = isGeneralGuidance,
                IsQuestionIncluded = isQuestionIncluded,
                CommunicationId = Fake.Communication.Id.ToString(),
                ProductCodes = [Fake.ProductExtension.PCID],
                ProductName = Fake.Product.Name,
                DateOfCommunication = DateTime.UtcNow,
                CountryName = Fake.Country.Name,
                DrugSubstanceNames = string.Join(Fake.DrugSubstance.Name + "|" + Fake.DrugSubstance.Code, Fake.DrugSubstance.Name + "|" + Fake.DrugSubstance.Code, ", "),
                ApplicationNumbers = [Fake.Application.Number, Fake.Application.Number],
                SubmissionNumbers = [Fake.Submission.Number, Fake.Submission.Number],
                ProductId = Fake.Product.Id.ToString(),
                BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
                BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
                ProductTypeNames = Fake.ProductType.Name,
                Subject = Fake.Communication.Subject,
                RouteOfAdministrationIds =[Fake.RouteOfAdministration.Id.ToString(), Fake.RouteOfAdministration.Id.ToString()],
                ProductCodeNames = string.Join(Fake.ProductExtension.PCID, Fake.ProductExtension.PCID, ", "),
                SubmissionTypeId = Fake.SubmissionType.Id.ToString(),
                DosageFormIds = [Fake.DosageForm.Id.ToString(), Fake.DosageForm.Id.ToString()],
                DrugSubstanceIds = [Fake.DrugSubstance.Id.ToString(), Fake.DrugSubstance.Id.ToString()],
                TagIds = [Fake.Tag.Id.ToString(), Fake.Tag.Id.ToString()],
                CountryId = Fake.Country.Id.ToString(),
                ApplicationIds = [Fake.Application.Id.ToString(), Fake.Application.Id.ToString()],
                SubmissionIds = [Fake.Submission.Id.ToString(),Fake.Submission.Id.ToString()],
                ProductTypeIds = [Fake.ProductType.Id.ToString(), Fake.ProductType.Id.ToString()],
                IsDeleted = Fake.Comment.IsDeleted.ToString()
            }, null, null),
            SearchModelFactory.SearchResult(new IndexedCommentModel()
            {
                Id = Fake.Comment.Id.ToString(),
                Description = Fake.Comment.Description,
                IsGeneralGuidance = isGeneralGuidance,
                IsQuestionIncluded = isQuestionIncluded,
                CommunicationId = Fake.Communication.Id.ToString(),
                ProductCodes = [Fake.ProductExtension.PCID],
                ProductName = Fake.Product.Name,
                DateOfCommunication = DateTime.UtcNow,
                CountryName = Fake.Country.Name,
                DrugSubstanceNames = string.Join(Fake.DrugSubstance.Name + "|" + Fake.DrugSubstance.Code, Fake.DrugSubstance.Name + "|" + Fake.DrugSubstance.Code, ", "),
                ApplicationNumbers = [Fake.Application.Number, Fake.Application.Number],
                SubmissionNumbers = [Fake.Submission.Number, Fake.Submission.Number],
                ProductId = Fake.Product.Id.ToString(),
                BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
                BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
                ProductTypeNames =  Fake.ProductType.Name,
                Subject = Fake.Communication.Subject,
                RouteOfAdministrationIds =[Fake.RouteOfAdministration.Id.ToString(), Fake.RouteOfAdministration.Id.ToString()],
                ProductCodeNames = string.Join(Fake.ProductExtension.PCID, Fake.ProductExtension.PCID, ", "),
                SubmissionTypeId = Fake.SubmissionType.Id.ToString(),
                DosageFormIds = [Fake.DosageForm.Id.ToString(), Fake.DosageForm.Id.ToString()],
                DrugSubstanceIds = [Fake.DrugSubstance.Id.ToString(), Fake.DrugSubstance.Id.ToString()],
                TagIds = [Fake.Tag.Id.ToString(), Fake.Tag.Id.ToString()],
                CountryId = Fake.Country.Id.ToString(),
                ApplicationIds = [Fake.Application.Id.ToString(), Fake.Application.Id.ToString()],
                SubmissionIds = [Fake.Submission.Id.ToString(),Fake.Submission.Id.ToString()],
                ProductTypeIds = [Fake.ProductType.Id.ToString(), Fake.ProductType.Id.ToString()],
                IsDeleted = Fake.Comment.IsDeleted.ToString()
            }, null, null)
        }, 2, null, null, Substitute.For<Response>());

        return (request, searchResults);
    }
}
