﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Tags.Create;

internal class CreateTagCommandHandler(ITagRepository tagsRepo,
    IMapper mapper,
    ILogger<CreateTagCommandHandler> logger,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider) : IRequestHandler<CreateTagCommandRequest, CreateTagCommandResponse>
{
    public async Task<CreateTagCommandResponse> Handle(CreateTagCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = mapper.Map<Tag>(request);

        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.TAG_CREATED, AuditEventCategory.TAGS, AuditEventDescription.TAG_CREATE, entity,
            async () =>
            {
                entity.Name = string.IsNullOrEmpty(request.Name.Trim()) ? Constants.NotAssigned : request.Name;
                entity.Description = request.Description;
                tagsRepo.AddItem(entity);
                await tagsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Tag {Tag} added successfully.", entity.Name);
            });

        return mapper.Map<CreateTagCommandResponse>(entity);
    }
}
