﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Entities.Base;
using Phlex.Core.Multitenancy;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance;

public class MultitenantHacommsDbContext : HACommsContext
{
    private readonly ITenant tenant;

    public MultitenantHacommsDbContext(DbContextOptions<HACommsContext> options, ITenant tenant) : base(options)
    {
        this.tenant = tenant;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Domain.Entities.Application>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<Submission>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<Communication>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier && !x.IsDeleted);
        modelBuilder.Entity<Comment>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier && !x.IsDeleted);
        modelBuilder.Entity<CommentDrugSubstances>().HasQueryFilter(x => x.Comment!.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<CommentProductExtension>().HasQueryFilter(x => x.Comment!.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<CommentTags>().HasQueryFilter(x => x.Comment!.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<DrugSubstance>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<DosageForm>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<RouteOfAdministration>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<Product>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<ProductExtension>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<ProductExtensionRouteOfAdministration>().HasQueryFilter(x => x.ProductExtension.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<Tag>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<CommunicationsView>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier && !x.IsDeleted);
        modelBuilder.Entity<ProductType>().HasQueryFilter(x => x.Tenant == this.tenant.Identifier);
        modelBuilder.Entity<ProductProductTypes>().HasQueryFilter(x => x.Product!.Tenant == this.tenant.Identifier);
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        foreach (var entityEntry in ChangeTracker.Entries())
        {
            if (entityEntry is { Entity: MultiTenantEntity entity, State: EntityState.Added or EntityState.Modified })
            {
                entity.Tenant = this.tenant.Identifier;
            }
        }

        return base.SaveChangesAsync(cancellationToken);
    }
}
