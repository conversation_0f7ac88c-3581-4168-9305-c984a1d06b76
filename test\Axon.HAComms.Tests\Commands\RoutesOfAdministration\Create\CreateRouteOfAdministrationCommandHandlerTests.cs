﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Create;

public class CreateRouteOfAdministrationCommandHandlerTests
{
    private readonly CreateRouteOfAdministrationCommandHandler sut;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public CreateRouteOfAdministrationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var routesOfAdministrationRepo = Substitute.For<IRouteOfAdministrationRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<CreateRouteOfAdministrationCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        sut = new CreateRouteOfAdministrationCommandHandler(routesOfAdministrationRepo, mapper, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;

        var request = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.ROUTE_OF_ADMINISTRATION_CREATED, AuditEventCategory.ROUTES_OF_ADMINISTRATION, AuditEventDescription.ROUTE_OF_ADMINISTRATION_CREATE,
                Arg.Any<RouteOfAdministration>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsCorrectEntity()
    {
        // Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;

        var request = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.ROUTE_OF_ADMINISTRATION_CREATED,
                AuditEventCategory.ROUTES_OF_ADMINISTRATION,
                AuditEventDescription.ROUTE_OF_ADMINISTRATION_CREATE,
                Arg.Any<RouteOfAdministration>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Name.Should().Be(request.Name);
    }
}
