﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Helpers;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Products.Create;

internal class CreateProductCommandHandler(
    IProductsRepository productsRepo,
    IDrugSubstancesRepository drugSubstancesRepo,
    IRouteOfAdministrationRepository routeOfAdministrationRepo,
    IDosageFormsRepository dosageFormsRepo,
    IProductTypesRepository productTypesRepo,
    IProductExtensionsRepository productExtensionsRepo,
    IMapper mapper,
    ILogger<CreateProductCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<CreateProductCommandRequest, CreateProductCommandResponse>
{
    public async Task<CreateProductCommandResponse> Handle(CreateProductCommandRequest request, CancellationToken cancellationToken)
    {
        var drugSubstancesIds = request.DrugSubstanceIds.Distinct().ToArray();
        var drugSubstances = await drugSubstancesRepo.GetAllByIdsAsync(drugSubstancesIds);
        drugSubstancesIds.ValidateAllIdsExist<DrugSubstance>(drugSubstances.Select(d => d.Id).ToArray());

        var productTypesIds = request.ProductTypeIds.Distinct().ToArray();
        var productTypes = await productTypesRepo.GetAllByIdsAsync(productTypesIds);
        productTypesIds.ValidateAllIdsExist<ProductType>(productTypes.Select(d => d.Id).ToArray());

        await ProductsHelper.ValidateProductTypeCombinationAsync(productTypesRepo, request.ProductTypeIds);

        var productExtensions = request.ProductExtensions.ToArray();
        var dosageFormIds = productExtensions.Select(x => x.DosageFormId).Distinct().ToArray();
        var dosageForms = await dosageFormsRepo.GetAllByIdsAsync(dosageFormIds);
        dosageFormIds.ValidateAllIdsExist<DosageForm>(dosageForms.Select(d => d.Id).ToArray());

        var routeOfAdministrationIds = productExtensions.SelectMany(x => x.RouteOfAdministrationIds).Distinct().ToArray();
        var routeOfAdministrationEntities = await routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministrationIds);
        routeOfAdministrationIds.ValidateAllIdsExist<RouteOfAdministration>(routeOfAdministrationEntities.Select(r => r.Id).ToArray());

        var entity = new Product();
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.DRUG_PRODUCT_CREATED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_CREATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                entity.IsActive = true;
                entity.DrugSubstanceProducts = drugSubstances.Select(ds => new DrugSubstanceDrugProduct { DrugSubstancesId = ds.Id, ProductsId = entity.Id }).ToList();
                entity.ProductProductTypes = productTypes.Select(pt => new ProductProductTypes { ProductTypeId = pt.Id, ProductId = entity.Id }).ToList();
                
                productsRepo.AddItem(entity);
                await productsRepo.SaveChangesAsync(userProvider);

                var productExtensionEntities = productExtensions.ToProductExtensionEntity(dosageForms, routeOfAdministrationEntities, entity);
                foreach (var extension in productExtensionEntities)
                {
                    productExtensionsRepo.AddItem(extension);
                }

                await productExtensionsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Product {ProductName} added successfully.", entity.Name);
            });

        var response = mapper.Map<CreateProductCommandResponse>(entity);
        return response;
    }

}
