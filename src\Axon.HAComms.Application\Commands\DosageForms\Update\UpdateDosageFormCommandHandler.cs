using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.DosageForms.Update;

public class UpdateDosageFormCommandHandler(
    IDosageFormsRepository repoDosageForms,
    IMapper mapper,
    ILogger<UpdateDosageFormCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<UpdateDosageFormCommandRequest, UpdateDosageFormCommandResponse>
{
    public async Task<UpdateDosageFormCommandResponse> Handle(UpdateDosageFormCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoDosageForms.GetItemAsync(request.Id);

        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DOSAGE_FORM_UPDATED, AuditEventCategory.DOSAGE_FORMS, AuditEventDescription.DOSAGE_FORM_UPDATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                repoDosageForms.UpdateItem(entity);
                await repoDosageForms.SaveChangesAsync(userProvider);
                logger.LogInformation("Dosage form {DosageForm} updated successfully.", entity.Name);
            });

        return mapper.Map<UpdateDosageFormCommandResponse>(entity);
    }
}
