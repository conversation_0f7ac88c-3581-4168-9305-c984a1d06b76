﻿-- To be executed on dev

DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

UPDATE [dbo].[Communications]
   SET [CountryId] = 184 -- To Switzerland
   
DELETE FROM [dbo].[Countries] WHERE Id != 184

INSERT INTO [dbo].[Countries] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
VALUES 
(N'Afghanistan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Albania', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Algeria', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Andorra', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Angola', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Antigua and Barbuda', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Argentina', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Armenia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Aruba', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Australia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Austria', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Azerbaijan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bahamas', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bahrain', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bangladesh', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Barbados', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Belarus', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Belgium', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Belize', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Benin', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bhutan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bolivia (Plurinational State of Bolivia)', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bosnia and Herzegovina', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Botswana', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Brazil', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Brunei Darussalam', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Bulgaria', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Burkina Faso', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Burundi', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cabo Verde', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cambodia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cameroon', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Canada', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Central African Republic', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Chad', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Chile', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'China', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Colombia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Comoros', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Congo', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cook Islands', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Costa Rica', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cote d''Ivoire', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Croatia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cuba', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Cyprus', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Czechia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Democratic People''s Republic of Korea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Democratic Republic of the Congo', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Denmark', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Djibouti', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Dominica', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Dominican Republic', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Ecuador', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Egypt', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'El Salvador', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Equatorial Guinea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Eritrea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Estonia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Eswatini', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Ethiopia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Eurasian Economic Union (EAEU)', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'European Union', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Fiji', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Finland', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'France', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Gabon', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Gambia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Georgia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Germany', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Ghana', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Greece', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Grenada', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Guatemala', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Guinea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Guinea-Bissau', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Guyana', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Haiti', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Honduras', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Hong Kong', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Hungary', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Iceland', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'India', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Indonesia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Iran (Islamic Republic of Iran)', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Iraq', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Ireland', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Israel', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Italy', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Jamaica', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Japan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Jordan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Kazakhstan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Kenya', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Kiribati', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Kuwait', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Kyrgyzstan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Lao People''s Democratic Republic', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Latvia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Lebanon', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Lesotho', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Liberia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Libya', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Liechtenstein', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Lithuania', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Luxembourg', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Madagascar', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Malawi', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Malaysia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Maldives', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mali', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Malta', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Marshall Islands', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mauritania', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mauritius', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mexico', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Micronesia (Federated States of Micronesia)', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Monaco', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mongolia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Montenegro', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Morocco', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Mozambique', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Myanmar', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'N/A', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Namibia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Nauru', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Nepal', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'New Zealand', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Nicaragua', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Niger', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Nigeria', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Niue', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'North Macedonia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Norway', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Oman', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Pakistan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Palau', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Palestine', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Panama', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Papua New Guinea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Paraguay', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Peru', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Philippines', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Poland', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Portugal', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Qatar', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Republic of Korea', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Republic of Moldova', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Romania', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Russian Federation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Rwanda', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Saint Kitts and Nevis', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Saint Lucia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Saint Vincent and the Grenadines', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Samoa', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'San Marino', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sao Tome and Principe', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Saudi Arabia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Senegal', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Serbia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Seychelles', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sierra Leone', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Singapore', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sint Maarten', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Slovakia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Slovenia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Solomon Islands', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Somalia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'South Africa', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'South Sudan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Spain', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sri Lanka', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sudan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Suriname', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Sweden', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Syrian Arab Republic', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Taiwan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Tajikistan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Thailand', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'The Netherlands', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Timor-Leste', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Togo', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Tonga', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Trinidad and Tobago', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Tunisia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Türkiye', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Turkmenistan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Tuvalu', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Uganda', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Ukraine', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'United Arab Emirates', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'United Kingdom of Great Britain and Northern Ireland', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'United Republic of Tanzania', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'United States of America', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Uruguay', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Uzbekistan', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Vanuatu', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Venezuela', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Vietnam', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Yemen', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Zambia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Zimbabwe', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted);
