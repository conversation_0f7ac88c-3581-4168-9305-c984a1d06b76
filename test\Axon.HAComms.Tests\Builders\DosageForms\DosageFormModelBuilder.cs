﻿using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.DosageForms
{
    public class DosageFormModelBuilder : IBuilder<DosageFormModel>
    {
        private int id = Fake.DosageForm.Id;
        private string name = Fake.DosageForm.Name;

        public static DosageFormModelBuilder Default() => new();

        public DosageFormModelBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public DosageFormModelBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public DosageFormModel Build()
        {
            return new(name)
            {
                Id = id,
            };
        }
    }
}
