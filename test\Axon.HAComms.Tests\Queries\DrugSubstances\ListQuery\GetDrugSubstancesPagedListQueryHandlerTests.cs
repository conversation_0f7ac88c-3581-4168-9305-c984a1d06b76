﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Queries.DrugSubstances.PagedListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using FluentAssertions;
using NSubstitute;
using Xunit;
using Axon.HAComms.Tests.Common;
using MockQueryable.NSubstitute;

namespace Axon.HAComms.Tests.Queries.DrugSubstances.ListQuery;

public class GetDrugSubstancesPagedListQueryHandlerTests
{
    private readonly GetDrugSubstancesPagedListQueryHandler handler;
    private readonly IDrugSubstancesRepository drugSubstancesRepo;

    public GetDrugSubstancesPagedListQueryHandlerTests()
    {
        drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        handler = new GetDrugSubstancesPagedListQueryHandler(drugSubstancesRepo);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(101);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 100, 10, null);

        // Act

        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var code = Fake.DrugSubstance.Code;
        var name = Fake.DrugSubstance.Name;
        var substance = new DrugSubstancesBuilder().WithCode(code).WithName(name).Build();
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(24);
        substances.Add(substance);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(new[] { $"code=>{code}", $"name=>{name}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Code != null && x.Code.Equals(code) && x.Name != null && x.Name.Equals(name));
    }

    [Fact]
    public async Task Handle_ValidDateFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var createdDate = new DateTime(2023, 04, 11);
        var lastUpdatedDate = new DateTime(2022, 10, 25);
        var lastUpdatedBy = Fake.DrugSubstance.LastUpdatedBy;

        var substance = new DrugSubstancesBuilder().WithCreatedDate(createdDate)
            .WithLastUpdatedDate(lastUpdatedDate)
            .WithLastUpdatedBy(lastUpdatedBy).Build();
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(24);
        substances.Add(substance);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(new[] { $"createdDate=>{createdDate.Year}", $"lastUpdatedDate=>{lastUpdatedDate.Year}", $"lastUpdatedBy=>{lastUpdatedBy.Substring(0, 5)}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x =>
            x.CreatedDate.Equals(createdDate) &&
            x.LastUpdatedDate.Equals(lastUpdatedDate) &&
            (x.LastUpdatedBy != null && x.LastUpdatedBy.Contains(lastUpdatedBy)));
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ReturnsAscOrderedItems()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(15);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 0, 10, "code=>asc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Code);
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ReturnsDescOrderedItems()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(15);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 0, 10, "code=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.Code);
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ByDefaultReturnsItemsSortedByName()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(15);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 0, 10, "INVALID_KEY=>INVALID_VALUE");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }


    [Fact]
    public async Task Handle_PassNullOrderParam_ReturnsOrderedItemsByCode()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(105);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 10, 50, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Code);
    }

    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(105);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), -10, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassDateOrderDesc_ReturnsOrderedItems()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate();
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 10, 10, "createdDate=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.CreatedDate);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var substances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(50);
        var mock = substances.BuildMock();

        drugSubstancesRepo.GetQueryableItems().Returns(mock);

        var request = new GetDrugSubstancesPagedListQueryRequest(Array.Empty<string>(), 10, -10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(40);
    }
}
