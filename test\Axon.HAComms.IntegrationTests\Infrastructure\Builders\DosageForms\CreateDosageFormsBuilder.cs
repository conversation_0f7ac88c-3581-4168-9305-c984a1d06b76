using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;

public class CreateDosageFormsBuilder
{
    private string name = Fake.DosageForm.Name;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static CreateDosageFormsBuilder Default() => new();

    public DosageForm Build()
    {
        return new DosageForm()
        {
            Name = name,
            Tenant = tenant
        };
    }

    public CreateDosageFormsBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CreateDosageFormsBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
