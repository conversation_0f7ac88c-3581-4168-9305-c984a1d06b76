﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Controllers;
using Axon.HAComms.ArchTests.Extensions;
using MassTransit.Internals;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetArchTest.Rules;
using Shouldly;
using System.Reflection;
using Xunit;

namespace Axon.HAComms.ArchTests;

public class ApiStandardsTests : TestBase
{
    [Fact]
    public void ClaimsBased_Api_Endpoints_Should_Be_Secured()
    {
        Types.InAssembly(ApplicationAssembly)
            .That()
            .Inherit(typeof(ApiControllerBase))
            .GetTypes()
            .ThatAreClaimsBasedControllers()
            .SelectMany(GetControllerActions)
            .ShouldHaveAttribute([typeof(HasPermissionsAttribute)]);
    }

    [Fact]
    public void ClaimsBased_Api_Endpoints_Should_Be_Secured_With_Correct_Permissions()
    {
        var failures = CheckEndpointPermissions(
            new PermissionValidator(),
            Types.InAssembly(ApplicationAssembly)
                .That()
                .Inherit(typeof(ApiControllerBase))
                .GetTypes()
                .ThatAreClaimsBasedControllers()
                .SelectMany(GetControllerActions)
                .ToList());

        failures.ShouldBeEmpty();
    }

    [Fact]
    public void Api_Endpoints_Should_Be_Authenticated()
    {
        IEnumerable<(Type controllerType, bool hasClassLevelAuthorisation)> controllers = Types.InAssembly(ApplicationAssembly)
            .That()
            .Inherit(typeof(ControllerBase))
            .GetTypes()
            .Where(x => !x.IsAbstract)
            .Select(x => (x, ControllerHasAuthorizeAttribute(x)));

        //If controller does not have a top level attribute (or on its base class) then every method needs to explictly say
        //whether only authenticated users can call it (AuthorizeAttribute) or that unauthenticated users can (AllowAnonymousAttribute)

        controllers.Where(x => !x.hasClassLevelAuthorisation)
            .Select(x => x.controllerType)
            .SelectMany(GetControllerActions)
            .ShouldHaveAttribute([typeof(AuthorizeAttribute), typeof(AllowAnonymousAttribute)]);
    }

    private static bool ControllerHasAuthorizeAttribute(Type controller)
    {
        if (controller.HasAttribute<AuthorizeAttribute>() || controller.HasAttribute<AllowAnonymousAttribute>())
        {
            return true;
        }

        if (controller.BaseType != null)
        {
            return ControllerHasAuthorizeAttribute(controller.BaseType);
        }

        return false;
    }

    private static IEnumerable<string> CheckEndpointPermissions(PermissionValidator permissionValidator, IEnumerable<MethodInfo> types)
    {
        return types.SelectMany(mi =>
        {
            var failures = new List<string>();

            failures.AddRange(permissionValidator.Validate(mi));

            return failures;
        });
    }

    private static MethodInfo[] GetControllerActions(Type t) =>
        t.GetMethods(BindingFlags.DeclaredOnly
                     | BindingFlags.Public
                     | BindingFlags.Instance);

}

public static class ArchTestExtensionsHelpers
{
    //Opt out list for api endpoints that are NOT claims based.
    //That way new controllers are picked up by default and have to be opted out of security attribute scanning
    private static readonly string[] NotClaimsBasedApis = [];
    public static IEnumerable<Type> ThatAreClaimsBasedControllers(this IEnumerable<Type> types) =>
        types.Where(t => !NotClaimsBasedApis.Any(a => t.Name.Equals(a)));
}
