﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Applications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetApplicationNumberListIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ApplicationsApi applicationApi = new ApplicationsApi(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetApplicationNumberDistinctList_GetRequest_ReturnsOk()
    {
        //Arrange
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 10);

        var applicationsCount = dbContext.Applications.Count();

        await CommunicationsTestEntitiesBuilder.Build(dbContext, products, 3);

        //Act
        var responseObj = await applicationApi.GetAllApplicationNumbersAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(applicationsCount + 3);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Communications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
