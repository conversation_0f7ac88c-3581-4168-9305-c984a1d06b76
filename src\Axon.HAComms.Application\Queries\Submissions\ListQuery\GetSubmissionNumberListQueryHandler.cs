﻿using Axon.HAComms.Application.Common.Interfaces;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Submissions.ListQuery;

public class GetSubmissionNumberListQueryHandler(ISubmissionsRepository repo) : IRequestHandler<GetSubmissionNumberListQueryRequest, ApiListResult<string>>
{
    public async Task<ApiListResult<string>> Handle(GetSubmissionNumberListQueryRequest request, CancellationToken cancellationToken)
    {
        var submissionNumbers = await repo.GetNumbersAsync();
        return new ApiListResult<string>(submissionNumbers);
    }
}
