﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;

//TODO: refactor common methods with CreateCommentCommandRequestBuilder into abstract class 
public class UpdateCommentCommandRequestBuilder
{
    private readonly string birdsLinkToBiResponse = Fake.Comment.BIRDSLinkToBIResponse;
    private readonly string birdsLinkToBisamp = Fake.Comment.BIRDSLinkToBISAMP;
    private readonly int communicationId = Fake.Communication.Id;

    private int id = Fake.Comment.Id;
    private string description = Fake.Comment.Description;
    private string question = Fake.Comment.Question;
    private string response = Fake.Comment.Response;
    private List<ProductExtensionCommentModel> productExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = Fake.Comment.RouteOfAdministrationIds, ProductExtensionId = Fake.Comment.ProductExtensionId } };
    private List<int> drugSubstanceIds = Fake.Comment.DrugSubstanceIds;
    private List<int> tagIds = Fake.Comment.TagIds;
    private bool isGeneralGuidance;
    private bool isQuestionIncluded;

    public static UpdateCommentCommandRequestBuilder Default() => new();

    public UpdateCommentCommandRequest Build()
    {
        return new UpdateCommentCommandRequest(communicationId, description, question, response, birdsLinkToBiResponse, birdsLinkToBisamp, productExtensions, drugSubstanceIds, tagIds, isGeneralGuidance, isQuestionIncluded, id);
    }

    public UpdateCommentCommandRequestBuilder WithId(int commentId)
    {
        id = commentId;
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithDescription(string commentDescription)
    {
        description = commentDescription;
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithQuestion(string commentQuestion)
    {
        question = commentQuestion;
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithResponse(string commentResponse)
    {
        response = commentResponse;
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithProductExtensions(params ProductExtensionCommentModel[] commentProductExtensions)
    {
        productExtensions = commentProductExtensions.ToList();
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithDrugSubstanceIds(params int[] commentDrugSubstanceIds)
    {
        drugSubstanceIds = [.. commentDrugSubstanceIds];
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithTagIds(params int[] commentTagIds)
    {
        tagIds = [.. commentTagIds];
        return this;
    }

    public UpdateCommentCommandRequestBuilder AsGeneralGuidance()
    {
        this.isGeneralGuidance = true;
        return this;
    }

    public UpdateCommentCommandRequestBuilder AsNonGeneralGuidance()
    {
        this.isGeneralGuidance = false;
        return this;
    }

    public UpdateCommentCommandRequestBuilder AsCommentOnly()
    {
        this.isQuestionIncluded = false;
        return this;
    }

    public UpdateCommentCommandRequestBuilder WithQuestionIncluded()
    {
        this.isQuestionIncluded = true;
        return this;
    }
}
