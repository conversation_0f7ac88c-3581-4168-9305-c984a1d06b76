﻿using MediatR;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Models.Application;

namespace Axon.HAComms.Application.Commands.Communications.Update;

public class UpdateCommunicationCommandRequest(
    int id,
    string subject,
    DateTime dateOfCommunication,
    int submissionTypeId,
    int countryId,
    ICollection<ApplicationModel> applications
) : CommunicationRequestModel(subject, dateOfCommunication, submissionTypeId, countryId, applications), IRequest<UpdateCommunicationCommandResponse>
{
    public int Id { get; } = id;
}
