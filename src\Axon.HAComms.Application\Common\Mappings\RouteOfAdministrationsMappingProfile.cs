﻿using AutoMapper;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class RouteOfAdministrationsMappingProfile : Profile
    {
        public RouteOfAdministrationsMappingProfile()
        {
            CreateMap<RouteOfAdministration, RouteOfAdministrationModel>();
            CreateMap<RouteOfAdministrationModel, RouteOfAdministration>()
                .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
                .ForMember(dest => dest.ProductExtensionRouteOfAdministrations, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<CreateRouteOfAdministrationCommandRequest, RouteOfAdministration>()
                .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
                .ForMember(dest => dest.ProductExtensionRouteOfAdministrations, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<UpdateRouteOfAdministrationCommandRequest, RouteOfAdministration>()
                .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
                .ForMember(dest => dest.ProductExtensionRouteOfAdministrations, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<RouteOfAdministration, CreateRouteOfAdministrationCommandResponse>();
            CreateMap<RouteOfAdministration, UpdateRouteOfAdministrationCommandRequest>();
            CreateMap<RouteOfAdministration, UpdateRouteOfAdministrationCommandResponse>();
        }
    }
}
