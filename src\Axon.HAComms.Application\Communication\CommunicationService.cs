﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Application.Communication;

internal class CommunicationService(
    IProductsRepository productsRepo,
    IProductExtensionsRepository productExtensionsRepo,
    IDrugSubstancesRepository drugSubstancesRepo,
    ITagRepository tagRepo) : ICommunicationService
{
    public async Task ValidateAndCreateCommentAsync(CommentRequestModel request, Comment comment)
    {
        List<CommentProductExtension> commentProductExtensionsList = [];
        List<DrugSubstance> drugSubstanceEntities = [];

        if (request is { IsGeneralGuidance: false, ProductExtensions: not null })
        {
            var productExtensionsIds = request.ProductExtensions.Select(r => r.ProductExtensionId).ToArray();
            var productExtensionsList = await productExtensionsRepo
                .GetQueryableItems()
                .Include(pe => pe.RouteOfAdministrations)
                .Where(pe => productExtensionsIds.Contains(pe.Id))
                .AsNoTracking().ToListAsync();

            if (productExtensionsList.Count != productExtensionsIds.Length)
            {
                throw new EntityNotFoundException(nameof(ProductExtension), string.Join(",", productExtensionsIds));
            }

            var productIds = productExtensionsList.Select(pe => pe.ProductId).Distinct().ToArray();
            if (productIds.Length > 1)
            {
                throw new InvalidOperationException("Product Extensions per comment must be for the same product.");
            }

            var product = await productsRepo
                .GetQueryableItems()
                .Include(p => p.DrugSubstances)
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.Id == productIds.Single());
            if (product == null)
            {
                throw new EntityNotFoundException(nameof(Product), productIds.Single());
            }

            var drugSubstanceIds = request.DrugSubstanceIds?.Distinct().ToArray() ?? [];
            drugSubstanceEntities = await drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds);
            drugSubstanceIds.ValidateAllIdsExist<DrugSubstance>(product.DrugSubstances.Select(s => s.Id).ToArray());

            foreach (var requestProductExtension in request.ProductExtensions)
            {
                var productExtension = productExtensionsList.Single(pe => pe.Id == requestProductExtension.ProductExtensionId);
                var routeOfAdministrationIds = requestProductExtension.RouteOfAdministrationIds.Distinct().ToArray();
                routeOfAdministrationIds.ValidateAllIdsExist<RouteOfAdministration>(productExtension.RouteOfAdministrations.Select(r => r.Id).ToArray());

                var commentProductExtension = new CommentProductExtension()
                {
                    ProductExtensionId = productExtension.Id,
                    CommentProductExtensionRoutesOfAdministrations = routeOfAdministrationIds
                        .Select(routeId => new CommentProductExtensionRoutesOfAdministration() { RouteOfAdministrationId = routeId }).ToList()
                };

                commentProductExtensionsList.Add(commentProductExtension);
            }
        }

        var tagIds = request.TagIds.Distinct().ToArray();
        var tagEntities = await tagRepo.GetAllByIdsAsync(tagIds);
        tagIds.ValidateAllIdsExist<Tag>(tagEntities.Select(r => r.Id).ToArray());

        comment.Description = request.IsQuestionIncluded ? null : request.Description;
        comment.Question = request.IsQuestionIncluded ? request.Question : null;
        comment.Response = request.IsQuestionIncluded ? request.Response : null;
        comment.CommentProductExtensions = !request.IsGeneralGuidance ? commentProductExtensionsList : null;
        comment.BIRDSLinkToBIResponse = request.BIRDSLinkToBIResponse;
        comment.BIRDSLinkToBISAMP = request.BIRDSLinkToBISAMP;
        comment.IsGeneralGuidance = request.IsGeneralGuidance;
        comment.IsQuestionIncluded = request.IsQuestionIncluded;

        // Solves issue when comment is updated without changing any properties (refer to integration test UpdateComment_NonGenericComment_CommentOnly_NoChanges_ReturnsOk)
        comment.SetCommentDrugSubstances(drugSubstanceEntities);
        comment.SetCommentTags(tagEntities);
    }
}
