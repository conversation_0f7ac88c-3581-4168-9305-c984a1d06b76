﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CompleteCommunicationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly HACommsContext dbContext = fixture.DbContext;
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());

    [Fact]
    public async Task CompleteCommunication_ValidCommunicationId_ReturnsOk()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        //Act
        await communicationApi.CompleteCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);
        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.IsCompleted.Should().BeTrue();
    }

    [Fact]
    public async Task CompleteCommunication_CompletedCommunication_ThrowsEntityNotFoundException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();
        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        await communicationApi.CompleteCommunicationAsync(createCommunicationResponse.Id, TenantConstants.DEFAULT_TENANT);
        var communicationResponse = await communicationApi.GetCommunicationAsync(createCommunicationResponse.Id, TenantConstants.DEFAULT_TENANT);
        communicationResponse.Should().NotBeNull();
        communicationResponse.IsCompleted.Should().BeTrue();

        //Act
        var completeCommunicationResponse = () => communicationApi.CompleteCommunicationAsync(createCommunicationResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await completeCommunicationResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains($"Entity \\\"Communication\\\" ({createCommunicationResponse.Id}) was not found", exception.And.Message);
    }

    [Fact]
    public async Task CompleteCommunication_InvalidCommunication_ThrowsEntityNotFoundException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = createdProduct.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();
        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var invalidId = Fake.GetRandomInt(createCommunicationResponse.Id + 1, createCommunicationResponse.Id + 50);

        //Act
        var completeCommunicationResponse = () => communicationApi.CompleteCommunicationAsync(invalidId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await completeCommunicationResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains($"Entity \\\"Communication\\\" ({invalidId}) was not found", exception.And.Message);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
            
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DosageForms.Clear();
        await dbContext.SaveChangesAsync();
    }
}
