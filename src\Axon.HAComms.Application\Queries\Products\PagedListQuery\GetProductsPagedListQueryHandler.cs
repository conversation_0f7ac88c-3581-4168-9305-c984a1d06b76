﻿using AutoMapper;
using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Application.Models.Products;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Products.PagedListQuery;

internal class GetProductsPagedListQueryHandler(
    IProductExtensionsRepository productExtensionsRepo,
    IMapper mapper) : IRequestHandler<GetProductsPagedListQueryRequest, ApiPagedListResult<ProductPagedListModel>>
{
    public async Task<ApiPagedListResult<ProductPagedListModel>> Handle(GetProductsPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildProductExtensions(request.Filters);
        var query = productExtensionsRepo.GetQueryableItemsWithIncludes().AsNoTracking();

        var entities = await query
            .FilterItems(expression, sub => sub.OrderBy(x => x.Product.Id), request.Skip, request.Take)
            .ToListAsync(cancellationToken: cancellationToken);
        var products = new List<ProductPagedListModel>();
        var productExtensionGroups = entities.GroupBy(x => x.ProductId);

        foreach (var productExtensionGroup in productExtensionGroups)
        {
            var currentProductId = productExtensionGroup.Key;
            var firstProduct = productExtensionGroup.First();
            var product = new ProductPagedListModel(currentProductId,
                firstProduct.Product.Name,
                firstProduct.Product.DrugSubstances.Select(d => new DrugSubstanceCodeAndName(d.Name!, d.Code)).ToList(),
                mapper.Map<ICollection<ProductExtensionModel>>(productExtensionGroup),
                firstProduct.Product.IsActive);
            products.Add(product);
        }

        return new ApiPagedListResult<ProductPagedListModel>(
            products,
            new()
            {
                TotalItemCount = expression != null
                    ? await query.Where(expression).GroupBy(e => e.Product.Id).CountAsync(cancellationToken)
                    : await query.GroupBy(p => p.Product.Id).CountAsync(cancellationToken),
                Limit = request.Take,
            });
    }
}
