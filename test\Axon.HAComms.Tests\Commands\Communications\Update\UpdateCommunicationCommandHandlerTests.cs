﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Communications.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Applications;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Update;

public class UpdateCommunicationCommandHandlerTests
{
    private readonly UpdateCommunicationCommandHandler handler;
    private readonly ICommunicationsRepository communicationRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IUserProvider userProvider;
    private readonly IAuditService auditService;

    public UpdateCommunicationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommunicationsMappingProfile());
            cfg.AddProfile(new CommentsMappingProfile());
            cfg.AddProfile(new ApplicationsMappingProfile());
            cfg.AddProfile(new SubmissionsMappingProfile());
        });
        var logger = Substitute.For<ILogger<UpdateCommunicationCommandHandler>>();
        var mapper = mockMapper.CreateMapper();
        communicationRepo = Substitute.For<ICommunicationsRepository>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_UPDATED, AuditEventCategory.COMMUNICATIONS,
            AuditEventDescription.COMMUNICATION_UPDATE, Arg.Any<Communication>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        handler = new UpdateCommunicationCommandHandler(communicationRepo, mapper, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var newSubject = Fake.Communication.Subject;
        var application = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var communication = new CommunicationsBuilder().Build();
        communicationRepo.GetItemAsync(Arg.Any<int>()).Returns(communication);

        var request = new UpdateCommunicationCommandRequest(
            communication.Id,
            newSubject,
            communication.DateOfCommunication,
            communication.SubmissionTypeId,
            communication.CountryId,
            [application]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_UPDATED, AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_UPDATE,
                Arg.Any<Communication>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var newSubject = Fake.Communication.Subject;
        var application = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var communication = new CommunicationsBuilder().Build();
        communicationRepo.GetItemAsync(Arg.Any<int>()).Returns(communication);

        var request = new UpdateCommunicationCommandRequest(
            communication.Id,
            newSubject,
            communication.DateOfCommunication,
            communication.SubmissionTypeId,
            communication.CountryId,
            [application]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(communication.Id);
        await communicationRepo.Received().GetItemAsync(request.Id);
        communicationRepo.Received().UpdateItem(Arg.Any<Communication>());
        await communicationRepo.Received(1).SaveChangesAsync(userProvider);
    }

    [Fact]
    public async Task Handle_CompletedCommunication_ThrowsException()
    {
        // Arrange
        var newSubject = Fake.Communication.Subject;
        var application = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var communication = new CommunicationsBuilder().Build();
        communication.IsCompleted = true;
        communicationRepo.GetItemAsync(Arg.Any<int>()).Returns(communication);

        var request = new UpdateCommunicationCommandRequest(
            communication.Id,
            newSubject,
            communication.DateOfCommunication,
            communication.SubmissionTypeId,
            communication.CountryId,
            [application]);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<InvalidOperationException>();
    }
}
