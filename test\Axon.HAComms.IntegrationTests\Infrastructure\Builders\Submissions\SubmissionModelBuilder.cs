﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;

public class SubmissionModelBuilder
{
    private string number = Fake.Submission.Number;

    public SubmissionModel Build()
    {
        return new SubmissionModel
        {
            Number = number
        };
    }

    public static SubmissionModelBuilder Default() => new();

    public SubmissionModelBuilder WithNumber(string number)
    {
        this.number = number;
        return this;
    }
}
