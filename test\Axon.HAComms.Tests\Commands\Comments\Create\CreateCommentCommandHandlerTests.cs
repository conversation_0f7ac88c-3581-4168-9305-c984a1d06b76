﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Comments.Create;

public class CreateCommentCommandHandlerTests
{
    private readonly CreateCommentCommandHandler handler;
    private readonly ICommunicationsRepository communicationsRepo;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IUserProvider userProvider;
    private readonly IAuditService auditService;

    public CreateCommentCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");
        
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommentsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        var logger = Substitute.For<ILogger<CreateCommentCommandHandler>>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        communicationsRepo = Substitute.For<ICommunicationsRepository>();
        var communicationService = Substitute.For<ICommunicationService>();
        handler = new CreateCommentCommandHandler(communicationService, commentsRepo, communicationsRepo, mapper, logger, correlationIdProvider, clientDetailsProvider,
            userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var comment = new Comment()
        {
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
            ProductExtensions = new List<ProductExtension> { new() { ProductId = Fake.Product.Id } },
        };

        var request = new CreateCommentCommandRequest()
        {
            Description = comment.Description,
            BIRDSLinkToBIResponse = comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = comment.BIRDSLinkToBISAMP,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new()
                {
                    ProductExtensionId = comment.ProductExtensions.First().Id, RouteOfAdministrationIds = [Fake.RouteOfAdministration.Id]
                }
            },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.DrugSubstance.Id],
            CommunicationId = Fake.Communication.Id
        };

        communicationsRepo.GetItemAsync(request.CommunicationId).Returns(new Communication());

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.COMMENT_CREATED,
                AuditEventCategory.COMMENTS,
                AuditEventDescription.COMMENT_CREATE,
                Arg.Any<Comment>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(0);
        commentsRepo.ReceivedWithAnyArgs(1).AddItem(default!);
        await commentsRepo.Received(1).SaveChangesAsync(userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var comment = new Comment()
        {
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
            ProductExtensions = new List<ProductExtension> { new() { ProductId = Fake.Product.Id } },
        };

        var request = new CreateCommentCommandRequest()
        {
            Description = comment.Description,
            BIRDSLinkToBIResponse = comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = comment.BIRDSLinkToBISAMP,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new()
                {
                    ProductExtensionId = comment.ProductExtensions.First().Id, RouteOfAdministrationIds = [Fake.RouteOfAdministration.Id]
                }
            },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.DrugSubstance.Id],
            CommunicationId = Fake.Communication.Id
        };

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_CREATED, AuditEventCategory.COMMENTS,
            AuditEventDescription.COMMENT_CREATE, Arg.Any<Comment>(), Arg.Any<Func<Task>>());
    }
}
