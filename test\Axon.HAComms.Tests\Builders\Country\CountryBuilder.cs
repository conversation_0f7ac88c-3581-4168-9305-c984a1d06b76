﻿using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Country
{
	public class CountryBuilder : IBuilder<Domain.Entities.Country>
    {
		private string name;
        private string region;
        private DateTime createdDate;
		private DateTime lastUpdatedDate;
		private string lastUpdatedBy;
		private string createdBy;

        public CountryBuilder()
		{
			this.name = Fake.Country.Name;
			this.createdDate = DateTime.Now;
			this.lastUpdatedDate = DateTime.Now;
			this.lastUpdatedBy = Fake.Country.LastUpdatedBy;
			this.createdBy = Fake.Country.CreatedBy;
			this.region = Fake.Country.Region;
		}

		public Domain.Entities.Country Build()
		{
			return new()
			{
				Name = name,
				CreatedDate = createdDate,
				LastUpdatedDate = lastUpdatedDate,
				LastUpdatedBy = lastUpdatedBy,
			    CreatedBy = createdBy,
				Region = region
			};
		}

        public CountryBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }
    }
}
