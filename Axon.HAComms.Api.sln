﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32228.430
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Api", "src\Axon.HAComms.Api\Axon.HAComms.Api.csproj", "{A4B0AEB1-DE90-4855-8020-F354ED64E424}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Domain", "src\Axon.HAComms.Domain\Axon.HAComms.Domain.csproj", "{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Infrastructure", "src\Axon.HAComms.Infrastructure\Axon.HAComms.Infrastructure.csproj", "{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Application", "src\Axon.HAComms.Application\Axon.HAComms.Application.csproj", "{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Tests", "test\Axon.HAComms.Tests\Axon.HAComms.Tests.csproj", "{1B257E5E-60F0-410F-BEFF-9F5B04C76854}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.IntegrationTests", "test\Axon.HAComms.IntegrationTests\Axon.HAComms.IntegrationTests.csproj", "{910E874B-FEB9-4327-B97F-673D4F2D47AF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.ArchTests", "test\Axon.HAComms.ArchTests\Axon.HAComms.ArchTests.csproj", "{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{CD00D495-865C-471F-AF4F-93260F96214C}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		Axon.HAComms.Api.sln.DotSettings = Axon.HAComms.Api.sln.DotSettings
		azure-pipelines.yml = azure-pipelines.yml
		README.md = README.md
	EndProjectSection
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{8BB05A4F-DCB7-4BB5-B453-C721315C414A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docker", "docker", "{08A70B87-C806-424D-9CFA-9E41989973FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Api.Sdk.Net", "src\Axon.HAComms.Api.Sdk.Net\Axon.HAComms.Api.Sdk.Net.csproj", "{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Api.Sdk.TypeScript", "src\Axon.HAComms.Api.Sdk.Typescript\Axon.HAComms.Api.Sdk.TypeScript.csproj", "{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sdk", "sdk", "{2CCB7A22-365A-48EC-BE5C-16AEC6E62079}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{C3E14DFD-E438-40F3-9692-BF2762725188}"
	ProjectSection(SolutionItems) = preProject
		scripts\GenerateSdks.ps1 = scripts\GenerateSdks.ps1
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{95F28C3D-E128-4F52-86BA-10D1A4F4AE6A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.Tests.Common", "test\Axon.HAComms.Tests.Common\Axon.HAComms.Tests.Common.csproj", "{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Debug|x64.ActiveCfg = Debug|x64
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Debug|x64.Build.0 = Debug|x64
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Release|Any CPU.Build.0 = Release|Any CPU
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Release|x64.ActiveCfg = Release|x64
		{A4B0AEB1-DE90-4855-8020-F354ED64E424}.Release|x64.Build.0 = Release|x64
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Debug|x64.ActiveCfg = Debug|x64
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Debug|x64.Build.0 = Debug|x64
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Release|x64.ActiveCfg = Release|x64
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A}.Release|x64.Build.0 = Release|x64
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Debug|x64.ActiveCfg = Debug|x64
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Debug|x64.Build.0 = Debug|x64
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Release|x64.ActiveCfg = Release|x64
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5}.Release|x64.Build.0 = Release|x64
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Debug|x64.Build.0 = Debug|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Release|x64.ActiveCfg = Release|Any CPU
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030}.Release|x64.Build.0 = Release|Any CPU
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Debug|x64.ActiveCfg = Debug|x64
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Debug|x64.Build.0 = Debug|x64
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Release|x64.ActiveCfg = Release|x64
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854}.Release|x64.Build.0 = Release|x64
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Debug|x64.ActiveCfg = Debug|x64
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Debug|x64.Build.0 = Debug|x64
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Release|Any CPU.Build.0 = Release|Any CPU
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Release|x64.ActiveCfg = Release|x64
		{910E874B-FEB9-4327-B97F-673D4F2D47AF}.Release|x64.Build.0 = Release|x64
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Debug|x64.ActiveCfg = Debug|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Debug|x64.Build.0 = Debug|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Release|Any CPU.Build.0 = Release|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Release|x64.ActiveCfg = Release|Any CPU
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04}.Release|x64.Build.0 = Release|Any CPU
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Debug|x64.ActiveCfg = Debug|x64
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Debug|x64.Build.0 = Debug|x64
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Release|x64.ActiveCfg = Release|x64
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A}.Release|x64.Build.0 = Release|x64
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Debug|x64.Build.0 = Debug|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Release|x64.ActiveCfg = Release|Any CPU
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091}.Release|x64.Build.0 = Release|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Debug|x64.Build.0 = Debug|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Release|x64.ActiveCfg = Release|Any CPU
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E}.Release|x64.Build.0 = Release|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Debug|x64.Build.0 = Debug|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Release|x64.ActiveCfg = Release|Any CPU
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A4B0AEB1-DE90-4855-8020-F354ED64E424} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{85AD78FD-4928-45A8-9D34-06AA8D6A7D0A} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{A1F32075-0ED9-4EBD-B25F-9FE267B755D5} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{B4F4F821-2B0A-4C4E-ABBE-6C19DD2AD030} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{1B257E5E-60F0-410F-BEFF-9F5B04C76854} = {95F28C3D-E128-4F52-86BA-10D1A4F4AE6A}
		{910E874B-FEB9-4327-B97F-673D4F2D47AF} = {95F28C3D-E128-4F52-86BA-10D1A4F4AE6A}
		{287C9640-F168-4ACD-8E29-8DAB4EFB8E04} = {95F28C3D-E128-4F52-86BA-10D1A4F4AE6A}
		{8BB05A4F-DCB7-4BB5-B453-C721315C414A} = {08A70B87-C806-424D-9CFA-9E41989973FC}
		{DAC9EB35-3E5C-4B4F-B431-3352D69FE091} = {2CCB7A22-365A-48EC-BE5C-16AEC6E62079}
		{33D3EAB4-82B4-4A84-840C-B0FFE981CF5E} = {2CCB7A22-365A-48EC-BE5C-16AEC6E62079}
		{2CCB7A22-365A-48EC-BE5C-16AEC6E62079} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{A9D0AA7F-AC78-434C-A8D3-71257C9A956D} = {95F28C3D-E128-4F52-86BA-10D1A4F4AE6A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {017230C6-8D38-4A98-A6B9-AD7C073DF3EF}
	EndGlobalSection
EndGlobal
