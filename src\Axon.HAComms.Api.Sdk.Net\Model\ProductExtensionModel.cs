/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// ProductExtensionModel
    /// </summary>
    [DataContract(Name = "ProductExtensionModel")]
    public partial class ProductExtensionModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductExtensionModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="productId">productId.</param>
        /// <param name="pcid">pcid.</param>
        /// <param name="dosageFormId">dosageFormId.</param>
        /// <param name="dosageFormName">dosageFormName.</param>
        /// <param name="routeOfAdministrationIds">routeOfAdministrationIds.</param>
        /// <param name="isActive">isActive.</param>
        /// <param name="routeOfAdministrations">routeOfAdministrations.</param>
        public ProductExtensionModel(int id = default(int), int productId = default(int), string pcid = default(string), int dosageFormId = default(int), string dosageFormName = default(string), List<int> routeOfAdministrationIds = default(List<int>), bool isActive = default(bool), List<RouteOfAdministrationModel> routeOfAdministrations = default(List<RouteOfAdministrationModel>))
        {
            this.Id = id;
            this.ProductId = productId;
            this.Pcid = pcid;
            this.DosageFormId = dosageFormId;
            this.DosageFormName = dosageFormName;
            this.RouteOfAdministrationIds = routeOfAdministrationIds;
            this.IsActive = isActive;
            this.RouteOfAdministrations = routeOfAdministrations;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets ProductId
        /// </summary>
        [DataMember(Name = "productId", EmitDefaultValue = false)]
        public int ProductId { get; set; }

        /// <summary>
        /// Gets or Sets Pcid
        /// </summary>
        [DataMember(Name = "pcid", EmitDefaultValue = true)]
        public string Pcid { get; set; }

        /// <summary>
        /// Gets or Sets DosageFormId
        /// </summary>
        [DataMember(Name = "dosageFormId", EmitDefaultValue = false)]
        public int DosageFormId { get; set; }

        /// <summary>
        /// Gets or Sets DosageFormName
        /// </summary>
        [DataMember(Name = "dosageFormName", EmitDefaultValue = true)]
        public string DosageFormName { get; set; }

        /// <summary>
        /// Gets or Sets RouteOfAdministrationIds
        /// </summary>
        [DataMember(Name = "routeOfAdministrationIds", EmitDefaultValue = true)]
        public List<int> RouteOfAdministrationIds { get; set; }

        /// <summary>
        /// Gets or Sets IsActive
        /// </summary>
        [DataMember(Name = "isActive", EmitDefaultValue = true)]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or Sets RouteOfAdministrations
        /// </summary>
        [DataMember(Name = "routeOfAdministrations", EmitDefaultValue = true)]
        public List<RouteOfAdministrationModel> RouteOfAdministrations { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ProductExtensionModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  ProductId: ").Append(ProductId).Append("\n");
            sb.Append("  Pcid: ").Append(Pcid).Append("\n");
            sb.Append("  DosageFormId: ").Append(DosageFormId).Append("\n");
            sb.Append("  DosageFormName: ").Append(DosageFormName).Append("\n");
            sb.Append("  RouteOfAdministrationIds: ").Append(RouteOfAdministrationIds).Append("\n");
            sb.Append("  IsActive: ").Append(IsActive).Append("\n");
            sb.Append("  RouteOfAdministrations: ").Append(RouteOfAdministrations).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
