﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using FluentAssertions;
using NSubstitute;
using Xunit;
using Axon.HAComms.Tests.Common;
using MockQueryable.NSubstitute;
using Axon.HAComms.Tests.Builders.Tags;
using Axon.HAComms.Application.Queries.Tags.PagedListQuery;

namespace Axon.HAComms.Tests.Queries.Tags.ListQuery;

public class GetTagsPagedListQueryHandlerTests
{
    private readonly GetTagsPagedListQueryHandler handler;
    private readonly ITagRepository tagsRepo;

    public GetTagsPagedListQueryHandlerTests()
    {
        tagsRepo = Substitute.For<ITagRepository>();
        handler = new GetTagsPagedListQueryHandler(tagsRepo);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(101);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), 100, 10, null);

        // Act			
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var name = Fake.Tag.Name;
        var tag = new TagBuilder().WithName(name).Build();
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(24);
        tags.Add(tag);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(new[] { $"name=>{name}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Name != null && x.Name.Equals(name));
    }

    [Fact]
    public async Task Handle_ValidDateFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var createdDate = new DateTime(2023, 04, 11);
        var createdBy = Fake.Tag.CreatedBy;
        var lastUpdatedDate = new DateTime(2022, 10, 25);
        var lastUpdatedBy = Fake.Tag.LastUpdatedBy;

        var tag = new TagBuilder()
            .WithCreatedDate(createdDate)
            .WithCreatedBy(createdBy)
            .WithLastUpdatedDate(lastUpdatedDate)
            .WithLastUpdatedBy(lastUpdatedBy).Build();
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(24);
        tags.Add(tag);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(new[] { $"createdDate=>{createdDate.Year}", $"createdBy=>{createdBy.Substring(0, 5)}", $"lastUpdatedDate=>{lastUpdatedDate.Year}", $"lastUpdatedBy=>{lastUpdatedBy.Substring(0, 5)}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x =>
            x.CreatedDate.Equals(createdDate) &&
            (x.CreatedBy != null && x.CreatedBy.Equals(createdBy)) &&
            x.LastUpdatedDate.Equals(lastUpdatedDate) &&
            (x.LastUpdatedBy != null && x.LastUpdatedBy.Contains(lastUpdatedBy)));
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(15);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), 0, 10, "name=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassNullOrderParam_ReturnsOrderedItemsByCode()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(105);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), 10, 50, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(105);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), -10, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassDateOrderDesc_ReturnsOrderedItems()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate();
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), 10, 10, "createdDate=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.CreatedDate);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(50);
        var mock = tags.BuildMock();

        tagsRepo.GetQueryableItems().Returns(mock);

        var request = new GetTagsPagedListQueryRequest(Array.Empty<string>(), 10, -10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(40);
    }
}
