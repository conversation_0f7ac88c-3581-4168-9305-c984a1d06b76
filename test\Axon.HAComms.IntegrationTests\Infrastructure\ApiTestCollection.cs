﻿using Xunit;

namespace Axon.HAComms.IntegrationTests.Infrastructure
{
    [CollectionDefinition(TestCollectionIDs.IntegrationTests)]
    public class ApiTestCollection : ICollectionFixture<ApiTestFixture>
    {
        // This class has no code, and is never created. Its purpose is simply
        // to be the place to apply [CollectionDefinition] and all the
        // ICollectionFixture<> interfaces.
    }

    public static class TestCollectionIDs
    {
        public const string IntegrationTests = nameof(IntegrationTests);
    }
}
