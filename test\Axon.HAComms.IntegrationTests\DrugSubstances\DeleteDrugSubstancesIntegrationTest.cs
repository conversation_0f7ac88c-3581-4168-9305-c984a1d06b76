﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Common.Builders;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DrugSubstances;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteDrugSubstancesIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DrugSubstancesApi drugSubstanceApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteDrugSubstances_ValidRequest_ReturnsOk()
    {
        //Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var drugSubstance = CreateDrugSubstancesBuilder.Default()
            .WithName(drugSubstanceName)
            .WithCode(drugSubstanceCode)
            .WithDescription(drugSubstanceDescription)
            .Build();

        dbContext.DrugSubstances.Add(drugSubstance);
        await dbContext.SaveChangesAsync();

        //Act
        await drugSubstanceApi.DeleteDrugSubstanceAsync(drugSubstance.Id, TenantConstants.DEFAULT_TENANT);
        var responseObj = await drugSubstanceApi.GetDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        Assert.DoesNotContain(responseObj.Data, x => x.Id == drugSubstance.Id);
    }

    [Fact]
    public async Task DeleteDrugSubstances_InvalidDrugSubstanceId_ThrowsException()
    {
        //Arrange
        var drugSubstanceId = Fake.DrugSubstance.Id;

        //Act
        var response = () => drugSubstanceApi.DeleteDrugSubstanceAsync(drugSubstanceId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"DrugSubstance\\\" ({drugSubstanceId}) was not found.", exception.And.Message);
    }

    [Fact]
    public async Task DeleteDrugSubstances_WithAssociatedComments_ThrowsAssociationException()
    {
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(2);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();

        var getProductResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = getProductResponse.ProductExtensions[0].Id,
                RouteOfAdministrationIds = getProductResponse.ProductExtensions[0]
                    .RoutesOfAdministration.Select(r => r.Id)
                    .ToList()
            })
            .WithDrugSubstanceIds(getProductResponse.DrugSubstances.Select(d => d.Id)
                .ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id)
                .ToArray())
            .Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();
        
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        //Act
        var drugSubstanceResponse = () => drugSubstanceApi.DeleteDrugSubstance(drugSubstanceIds[0], TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = drugSubstanceResponse.Should().Throw<ApiException>();
        exception.And.Message.Should().Contain("AssociationExistsException");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.DrugSubstances.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
