﻿using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Application.Models.ProductType;
using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Models.Products
{
    public class ProductModel
    {
        public int Id { get; set; }

        [Required]
        public string Name { get; set; }

        public bool IsActive { get; set; }

        public ICollection<DrugSubstanceModel> DrugSubstances { get; set; }

        public ICollection<ProductTypeModel> ProductTypes { get; set; }

        public ICollection<ProductExtensionResponseModel> ProductExtensions { get; set; }

        public ProductModel(
            string name, 
            ICollection<DrugSubstanceModel> drugSubstances, 
            ICollection<ProductTypeModel> productTypes,
            ICollection<ProductExtensionResponseModel> productExtensions, 
            bool isActive)
        {
            Name = name;
            DrugSubstances = drugSubstances;
            ProductTypes = productTypes;
            ProductExtensions = productExtensions;  
            IsActive = isActive;    
        }
    }
}
