using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;

public class CreateProductCommandRequestBuilder
{
    private string name = Fake.Product.Name;
    private bool isActive = Fake.Product.IsActive;
    private List<int> drugSubstanceIds = Fake.Product.DrugSubstanceIds;
    private List<int> productTypeIds = Fake.Product.ProductTypeIds;
    private List<ProductExtensionModel> productExtensions = [];

    public CreateProductCommandRequest Build()
    {
        return new CreateProductCommandRequest(name, isActive, productTypeIds, drugSubstanceIds, productExtensions, Constants.NotAssigned);
    }

    public CreateProductCommandRequestBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CreateProductCommandRequestBuilder WithIsActive(bool isActive)
    {
        this.isActive = isActive;
        return this;
    }

    public CreateProductCommandRequestBuilder WithDrugSubstances(List<int> drugSubstanceIds)
    {
        this.drugSubstanceIds = drugSubstanceIds;
        return this;
    }

    public CreateProductCommandRequestBuilder WithProductTypes(List<int> productTypeIds)
    {
        this.productTypeIds = productTypeIds;
        return this;
    }

    public CreateProductCommandRequestBuilder WithProductExtensions(params ProductExtensionModel[] productExtensions)
    {
        this.productExtensions = productExtensions.ToList();
        return this;
    }
}
