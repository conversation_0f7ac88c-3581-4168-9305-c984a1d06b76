﻿using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Axon.HAComms.Api.JsonHandling
{
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType is JsonTokenType.String)
            {
                if (DateTime.TryParse(reader.GetString(), CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTime))
                {
                    return dateTime;
                }

                throw new JsonException("Invalid datetime format. Please provide datetime in valid format.");
            }

            throw new JsonException("Invalid JSON token type for datetime.");
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(CultureInfo.InvariantCulture));
        }
    }
}
