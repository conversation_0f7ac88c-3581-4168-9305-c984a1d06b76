﻿using System.Security.Claims;

namespace Axon.HAComms.Infrastructure.Persistance
{
    public static class ClaimsPrincipalExtensions
    {
        public static string? GetEmail(this ClaimsPrincipal user)
        {
            return (user.Identity as ClaimsIdentity)?.GetEmail();
        }

        public static string? GetEmail(this ClaimsIdentity user)
        {
            return user.GetClaimValue("emails")?.ToLower();
        }

        public static string? GetClaimValue(this ClaimsIdentity user, string claimType)
        {
            return user.Claims.FirstOrDefault((z) => z.Type == claimType)?.Value;
        }

        public static T GetClaimValue<T>(this ClaimsIdentity user, string claimType) where T : struct
        {
            var val = user.Claims.FirstOrDefault(z => z.Type == claimType)?.Value;

            if (string.IsNullOrEmpty(val))
                return default;

            return (T)Convert.ChangeType(val, typeof(T));
        }

        public static string? GetClaimValue(this ClaimsPrincipal user, string claimType)
        {
            return (user.Identity as ClaimsIdentity)?.GetClaimValue(claimType);
        }
    }
}
