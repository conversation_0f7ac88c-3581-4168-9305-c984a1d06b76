﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using FluentAssertions;
using NSubstitute;
using Xunit;
using Axon.HAComms.Tests.Common;
using MockQueryable.NSubstitute;
using Axon.HAComms.Application.Queries.RoutesOfAdministration.PagedListQuery;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;

namespace Axon.HAComms.Tests.Queries.RoutesOfAdministration.ListQuery;

public class GetRoutesOfAdministrationPagedListQueryHandlerTests
{
    private readonly GetRoutesOfAdministrationPagedListQueryHandler handler;
    private readonly IRouteOfAdministrationRepository routesRepo;

    public GetRoutesOfAdministrationPagedListQueryHandlerTests()
    {
        routesRepo = Substitute.For<IRouteOfAdministrationRepository>();
        handler = new GetRoutesOfAdministrationPagedListQueryHandler(routesRepo);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var routesOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(101);
        var mock = routesOfAdministration.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), 100, 10, null);

        // Act			
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var name = Fake.RouteOfAdministration.Name;
        var routeOfAdministration = new RouteOfAdministrationBuilder().WithName(name).Build();
        var routesOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(24);
        routesOfAdministration.Add(routeOfAdministration);
        var mock = routesOfAdministration.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(new[] { $"name=>{name}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Name.Equals(name));
    }

    [Fact]
    public async Task Handle_ValidDateFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var createdDate = new DateTime(2023, 04, 11);
        var createdBy = Fake.RouteOfAdministration.CreatedBy;
        var lastUpdatedDate = new DateTime(2022, 10, 25);
        var lastUpdatedBy = Fake.RouteOfAdministration.LastUpdatedBy;

        var substance = new RouteOfAdministrationBuilder()
            .WithCreatedDate(createdDate)
            .WithCreatedBy(createdBy)
            .WithLastUpdatedDate(lastUpdatedDate)
            .WithLastUpdatedBy(lastUpdatedBy).Build();
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(24);
        routes.Add(substance);
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(new[] { $"createdDate=>{createdDate.Year}", $"createdBy=>{createdBy.Substring(0, 5)}", $"lastUpdatedDate=>{lastUpdatedDate.Year}", $"lastUpdatedBy=>{lastUpdatedBy.Substring(0, 5)}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x =>
            x.CreatedDate.Equals(createdDate) &&
            (x.CreatedBy != null && x.CreatedBy.Equals(createdBy)) &&
            x.LastUpdatedDate.Equals(lastUpdatedDate) &&
            (x.LastUpdatedBy != null && x.LastUpdatedBy.Contains(lastUpdatedBy)));
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(15);
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), 0, 10, "name=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassNullOrderParam_ReturnsOrderedItemsByCode()
    {
        //Arrange
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(105);
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), 10, 50, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(105);
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), -10, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassDateOrderDesc_ReturnsOrderedItems()
    {
        //Arrange
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate();
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), 10, 10, "createdDate=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.CreatedDate);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var routes = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(50);
        var mock = routes.BuildMock();

        routesRepo.GetQueryableItems().Returns(mock);

        var request = new GetRoutesOfAdministrationPagedListQueryRequest(Array.Empty<string>(), 10, -10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(40);
    }
}
