﻿namespace Axon.HAComms.Tests.Builders
{
    public static class TestEntitiesGenerator<T1, T2> where T1 : class
                                                      where T2 : IBuilder<T1>, new()
    {
        public static List<T1> Generate(int entries = 100)
        {
            var list = new List<T1>();
            for (var i = 0; i < entries; i++)
            {
                list.Add(new T2().Build());
            }

            return list;
        }
    }
}
