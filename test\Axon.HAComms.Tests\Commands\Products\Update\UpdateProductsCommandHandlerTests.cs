﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Products.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Comments;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using Axon.HAComms.Tests.Builders.ProductExtensions;
using Axon.HAComms.Tests.Builders.Products;
using Axon.HAComms.Tests.Builders.ProductTypes;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Update;

public class UpdateProductsCommandHandlerTests
{
    private readonly UpdateProductCommandHandler handler;
    private readonly ICommentsRepository commentsRepo;
    private readonly IProductsRepository productsRepo;
    private readonly IDrugSubstancesRepository drugSubstancesRepo;
    private readonly IDosageFormsRepository dosageFormsRepo;
    private readonly IRouteOfAdministrationRepository routeOfAdministrationRepo;
    private readonly IProductTypesRepository productTypesRepo;
    private readonly IAuditService auditService;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;

    public UpdateProductsCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<UpdateProductCommandHandler>>();

        productsRepo = Substitute.For<IProductsRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        routeOfAdministrationRepo = Substitute.For<IRouteOfAdministrationRepository>();
        dosageFormsRepo = Substitute.For<IDosageFormsRepository>();
        var productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        productTypesRepo = Substitute.For<IProductTypesRepository>();
        auditService = Substitute.For<IAuditService>();
        handler = new UpdateProductCommandHandler(productsRepo, commentsRepo, drugSubstancesRepo, routeOfAdministrationRepo, dosageFormsRepo, productExtensionsRepo,
            productTypesRepo, mapper, logger, auditService, correlationIdProvider, clientDetailsProvider, userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequestWithDrugSubstances_ReturnsSuccessResult()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();

        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration],
            id: Fake.ProductExtension.Id);
        var product = new ProductsBuilder()
            .WithId(productId)
            .WithName(productName)
            .WithIsActive(isActive)
            .WithDrugSubstances([.. drugSubstances])
            .WithProductExtensions(productExtensionEntity)
            .WithProductTypes([.. productTypes]).Build();
        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(pcid)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel },
            [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(productId);
        result.UpdatedCommentsCount.Should().Be(0);
    }

    [Fact]
    public async Task Handle_ValidRequestUpdateWithAssociatedComments_ReturnsSuccessResult()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var newProductTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        var newProductTypeIds = newProductTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productTypesRepo.GetAllByIdsAsync(newProductTypeIds).Returns(newProductTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(newProductTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var comments = TestEntitiesGenerator<Comment, CommentsBuilder>.Generate(1);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();

        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration],
            id: Fake.ProductExtension.Id);
        var product = new ProductsBuilder()
            .WithId(productId)
            .WithName(productName)
            .WithIsActive(isActive)
            .WithDrugSubstances([.. drugSubstances])
            .WithProductExtensions(productExtensionEntity)
            .WithProductTypes([.. productTypes]).Build();
        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(true);
        commentsRepo.GetItemsWithProductExtensionIdsAsync(Arg.Any<int[]>()).Returns(comments.AsQueryable());

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(pcid)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel },
            [.. drugSubstanceIds], [.. newProductTypeIds]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(productId);
        result.UpdatedCommentsCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_NonExistingEntity_ThrowsException()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new UpdateProductCommandRequest(productId, productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ProductUpdateWithInvalidDosageForm_ThrowsException()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);

        var product = new ProductsBuilder().WithName(productName).WithIsActive(isActive).WithProductExtensions(productExtensionEntity).Build();

        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(Enumerable.Empty<DosageForm>().ToArray()));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(pcid)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel },
            [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ProductUpdateWithInvalidRouteOfAdministration_ThrowsException()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);

        var product = new ProductsBuilder().WithName(productName).WithIsActive(isActive).WithProductExtensions(productExtensionEntity).Build();

        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(Enumerable.Empty<RouteOfAdministration>().ToArray()));

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(pcid)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel }, [.. drugSubstanceIds],
            [.. productTypeIds]);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ProductUpdateWithEmptyProductTypeIds_ThrowsException()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        productTypesRepo.GetAllByIdsAsync(Arg.Any<int>()).ReturnsNull();

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);

        var product = new ProductsBuilder().WithName(productName).WithIsActive(isActive).WithProductExtensions(productExtensionEntity).Build();

        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(Enumerable.Empty<RouteOfAdministration>().ToArray()));

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel },
            [.. drugSubstanceIds], []);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var pcid = Fake.ProductExtension.PCID;

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        productsRepo.ExistsAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(false);

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();

        var productExtensionEntity = ProductExtension.Create(
            pcid,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration],
            id: Fake.ProductExtension.Id);
        var product = new ProductsBuilder()
            .WithName(productName)
            .WithIsActive(isActive)
            .WithDrugSubstances([.. drugSubstances])
            .WithProductExtensions(productExtensionEntity)
            .WithProductTypes([.. productTypes]).Build();
        productsRepo.GetItemAsync(productId).Returns(product);
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtensionModel = ProductExtensionModelBuilder.Default()
            .WithId(productExtensionEntity.Id)
            .WithPcid(pcid)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var request = new UpdateProductCommandRequest(productId, productName, isActive, new List<ProductExtensionModel> { productExtensionModel },
            [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DRUG_PRODUCT_UPDATED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_UPDATE,
                Arg.Any<Product>(), Arg.Any<Func<Task>>());
    }
}
