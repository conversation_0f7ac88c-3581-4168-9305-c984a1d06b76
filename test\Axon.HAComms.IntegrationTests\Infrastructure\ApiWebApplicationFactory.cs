﻿using Axon.HAComms.Application.Common;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Infrastructure.Auth;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Infrastructure
{
    [Collection("Database")]
    public class ApiWebApplicationFactory : WebApplicationFactory<Program>
    {
        public const string AUTH_SCHEME_NAME = "FakeAuthScheme";

        protected override IHost CreateHost(IHostBuilder builder)
        {
            builder.ConfigureAppConfiguration(config =>
            {
                var integrationConfig = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.Development.json")
                    .Build();
                config.AddConfiguration(integrationConfig);
            });

            builder.ConfigureServices(services =>
            {
                services.AddAuthentication(options =>
                    {
                        options.DefaultAuthenticateScheme = AUTH_SCHEME_NAME;
                        options.DefaultChallengeScheme = AUTH_SCHEME_NAME;
                    })
                    .AddScheme<AuthenticationSchemeOptions, TestAuthHandler>(AUTH_SCHEME_NAME, _ => { });
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var scopedServices = scope.ServiceProvider;
                var dbContext = scopedServices.GetRequiredService<HACommsContext>();
                dbContext.Database.Migrate();
                Seed(dbContext).Wait();
            });

            builder.UseEnvironment("IntegrationTest");
            return base.CreateHost(builder);
        }

        private static async Task Seed(HACommsContext dbContext)
        {
            //countries
            var country1 = new Country() { Name = Fake.Country.Name };
            var country2 = new Country() { Name = Fake.Country.Name };
            var country3 = new Country() { Name = Fake.Country.Name };
            var country4 = new Country() { Name = Fake.Country.Name };
            var country5 = new Country() { Name = Fake.Country.Name };
            await dbContext.Countries.AddRangeAsync(country1, country2, country3, country4, country5);

            //submission type
            var submissionType1 = new SubmissionType() { Name = Fake.SubmissionType.Name };
            var submissionType2 = new SubmissionType() { Name = Fake.SubmissionType.Name };
            var submissionType3 = new SubmissionType() { Name = Fake.SubmissionType.Name };
            var submissionType4 = new SubmissionType() { Name = Fake.SubmissionType.Name };
            var submissionType5 = new SubmissionType() { Name = Fake.SubmissionType.Name };
            await dbContext.SubmissionTypes.AddRangeAsync(submissionType1, submissionType2, submissionType3, submissionType4, submissionType5);

            //Product types
            var productType1 = new ProductType(0) { Name = Fake.ProductType.Name, Tenant = TenantConstants.DEFAULT_TENANT };
            var productType2 = new ProductType(0) { Name = Fake.ProductType.Name, Tenant = TenantConstants.DEFAULT_TENANT };
            var productType3 = new ProductType(0) { Name = Fake.ProductType.Name, Tenant = TenantConstants.DEFAULT_TENANT };
            var productType4 = new ProductType(0) { Name = Fake.ProductType.Name, Tenant = TenantConstants.DEFAULT_TENANT };
            var productType5 = new ProductType(0) { Name = Fake.ProductType.Name, Tenant = TenantConstants.DEFAULT_TENANT };
            var productType6 = new ProductType(0) { Name = Constants.NotCategorized, Tenant = TenantConstants.DEFAULT_TENANT };
            await dbContext.ProductTypes.AddRangeAsync(productType1, productType2, productType3, productType4, productType5, productType6);

            //save seed data
            await dbContext.SaveChangesAsync();
        }
    }
}
