﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IDosageFormsRepository : IRepository<DosageForm>
{
    Task<DosageForm> GetItemAsync(int id);

    Task<IEnumerable<DosageForm>> GetItemsAsync();

    IQueryable<DosageForm> GetQueryableItems();

    Task<DosageForm[]> GetAllByIdsAsync(params int[] ids);

    Task<bool> ExistsAsync(Expression<Func<DosageForm, bool>> filter);
}
