﻿using MediatR;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Application.Commands.Tags.Update
{
    public class UpdateTagCommandRequest : IRequest<UpdateTagCommandResponse>
    {
        public int Id { get; }
        public string Name { get; }

        public string? Description { get; }

        public UpdateTagCommandRequest(int id, string name, string? description)
        {
            Id = id;
            Name = name;
            Description = description;
        }
    }
}
