﻿using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Interfaces;

namespace Axon.HAComms.Domain.Entities;

public class Tag : MultiTenantEntity, IEntityWithName
{
    public Tag(int id, string name)
    {
        Id = id;
        Name = name;
    }

    public Tag()
    { }

    public string Name { get; set; } = string.Empty;

    public string? Description { get; set; }

    public int? ExternalId { get; set; }

    public ICollection<Comment>? Comments { get; set; }
}
