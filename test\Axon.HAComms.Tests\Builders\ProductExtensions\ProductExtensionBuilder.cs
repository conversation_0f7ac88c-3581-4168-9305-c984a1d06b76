﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.ProductExtensions
{
    public class ProductExtensionBuilder : IBuilder<ProductExtension>
    {
        private int id;
        private string pcid;
        private int productId;
        private bool isActive;
        private string createdBy;
        private DateTime createdDate;
        private DateTime lastUpdatedDate;
        private string lastUpdatedBy;
        private DosageFormBuilder dosageFormBuilder;

        public ProductExtensionBuilder()
        {
            this.id = Fake.ProductExtension.Id;
            this.pcid = Fake.ProductExtension.PCID;
            this.isActive = Fake.ProductExtension.IsActive;
            this.createdBy = Fake.ProductExtension.CreatedBy;
            this.createdDate = DateTime.Now;
            this.lastUpdatedDate = DateTime.Now;
            this.lastUpdatedBy = Fake.ProductExtension.LastUpdatedBy;
            this.productId = Fake.Product.Id;
            this.dosageFormBuilder = new DosageFormBuilder();
        }
        public ProductExtension Build()
        {
            return new()
            {
                PCID = this.pcid,
                CreatedDate = this.createdDate,
                CreatedBy = this.createdBy,
                ProductId = this.productId,
                IsActive = this.isActive,
                LastUpdatedBy = this.lastUpdatedBy,
                LastUpdatedDate = this.lastUpdatedDate,
                DosageForm = this.dosageFormBuilder.Build(),
                Product = new Product(this.productId)
                {
                    Name = Fake.Product.Name,
                    IsActive = true,
                }
            };
        }

        public ProductExtensionBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public ProductExtensionBuilder WithPCID(string pcid)
        {
            this.pcid = pcid;
            return this;
        }

        public ProductExtensionBuilder WithDosageForm(Action<DosageFormBuilder> action)
        {
            var builder = new DosageFormBuilder();
            action.Invoke(builder);
            dosageFormBuilder = builder;
            return this;
        }
    }
}
