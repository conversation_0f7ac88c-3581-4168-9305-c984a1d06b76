﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Auth;

public class TestAuthHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, UrlEncoder encoder)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var headers = Context.Request.Headers;

        var roleOverride = headers[HeaderOverrides.ROLE_OVER_HEADER_KEY];
        var emailOverride = headers[HeaderOverrides.EMAIL_OVERRIDE_HEADER_KEY];
        var objectIdOverride = headers[HeaderOverrides.OBJECT_ID_OVERRIDE_HEADER_KEY];

        var claims = new List<Claim>();

        if (!string.IsNullOrEmpty(roleOverride))
        {
            claims.RemoveAll(c => c.IsType(AxonClaimTypes.ROLE, "role"));
            claims.Add(new Claim(AxonClaimTypes.ROLE, "Test-" + roleOverride));
        }

        if (!string.IsNullOrEmpty(emailOverride))
        {
            claims.RemoveAll(c => c.IsType(AxonClaimTypes.EMAIL, "email"));
            claims.Add(new Claim(AxonClaimTypes.EMAIL, emailOverride.ToString()));
        }

        if (!string.IsNullOrEmpty(objectIdOverride))
        {
            claims.RemoveAll(c => c.IsType(AxonClaimTypes.OBJECT_ID, "objectid"));
            claims.Add(new Claim(AxonClaimTypes.OBJECT_ID, objectIdOverride.ToString()));
        }
        const string authenticationType = "FakeAuthScheme";
        var principle = new ClaimsPrincipal(new ClaimsIdentity(claims, authenticationType));

        if (claims.Count == 0)
        {
            return Task.FromResult(AuthenticateResult.Fail("User has no claims."));
        }

        return Task.FromResult(
            AuthenticateResult.Success(
                new AuthenticationTicket(principle, authenticationType)));
    }
}
