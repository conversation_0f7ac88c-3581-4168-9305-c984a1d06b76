﻿using Axon.HAComms.Application.Commands.Comments.Delete;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Comments.Delete;

public class DeleteCommentCommandValidatorTests
{
    private readonly DeleteCommentCommandValidator sut = new();

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        // Arrange
        var request = new DeleteCommentCommandRequest();

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Id);
        result.Errors.First().ErrorMessage.Should().Contain("'Id' must not be empty.");
    }

    [Fact]
    public void Validate_IdIsNoEmpty_DoesNotThrowException()
    {
        // Arrange
        var request = new DeleteCommentCommandRequest()
        {
            Id = Fake.Comment.Id,
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }
}
