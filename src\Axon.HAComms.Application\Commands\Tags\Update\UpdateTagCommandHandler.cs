using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Tags.Update;

internal class UpdateTagCommandHandler(
    ITagRepository repoTags,
    IMapper mapper,
    ILogger<UpdateTagCommandHandler> logger,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider) : IRequestHandler<UpdateTagCommandRequest, UpdateTagCommandResponse>
{
    public async Task<UpdateTagCommandResponse> Handle(UpdateTagCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoTags.GetItemAsync(request.Id);
        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.TAG_UPDATED, AuditEventCategory.TAGS, AuditEventDescription.TAG_UPDATE, entity,
            async () =>
            {
                entity.Name = string.IsNullOrEmpty(request.Name.Trim()) ? Constants.NotAssigned : request.Name;
                entity.Description = request.Description;
                repoTags.UpdateItem(entity);
                await repoTags.SaveChangesAsync(userProvider);
                logger.LogInformation("Tag {Tag} updated successfully.", entity.Name);
            });

        return mapper.Map<UpdateTagCommandResponse>(entity);
    }
}
