﻿using Axon.HAComms.Domain.Entities.Base;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Domain.Entities;

public class ProductExtension : MultiTenantEntity
{
    [MaxLength(15)]
    public string PCID { get; set; } = string.Empty;

    public DosageForm DosageForm { get; set; } = null!;

    public int DosageFormId { get; set; }

    public ICollection<RouteOfAdministration> RouteOfAdministrations { get; set; } = new HashSet<RouteOfAdministration>();

    public ICollection<ProductExtensionRouteOfAdministration>? ProductExtensionRouteOfAdministrations { get; set; }

    public bool IsActive { get; set; }

    public int ProductId { get; set; }

    public Product Product { get; set; } = null!;

    public ICollection<Comment> Comments { get; set; } = new HashSet<Comment>();

    public ProductExtension (string pcid, DosageForm dosageForm, bool isActive, List<RouteOfAdministration> routes = default!, Product? product = null)
    {
        PCID = pcid;
        DosageForm = dosageForm;
        RouteOfAdministrations = routes;
        IsActive = isActive;
        Product = product ?? null!;
        ProductId = product?.Id ?? 0;
    }

    public ProductExtension()
    {
    }

    public static ProductExtension Create(string pcid, DosageForm dosageForm, bool isActive, List<RouteOfAdministration> routes = default!, Product? product = null, int id = 0)
    {
        return new ProductExtension()
        {
            Id = id,
            PCID = pcid,
            DosageForm = dosageForm,
            RouteOfAdministrations = routes,
            IsActive = isActive,
            Product = product ?? null!,
            ProductId = product?.Id ?? 0
        };
    }

    public void SetRouteOfAdministrations(List<RouteOfAdministration> routesOfAdministration)
    {
        ProductExtensionRouteOfAdministrations = routesOfAdministration
            .Select(roa => new ProductExtensionRouteOfAdministration { ProductExtension = this, RouteOfAdministrationId = roa.Id }).ToList();
    }
}
