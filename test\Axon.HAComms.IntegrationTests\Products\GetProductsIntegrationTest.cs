﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.ProductExtensions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Products;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetProductsIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductsApi apiProducts = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());

    [Fact]
    public async Task GetPagedProducts_ValidRequest_ReturnsOk()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 56);

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT, null, 50, 10);

        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedProducts_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 77);
        var randomProduct = await dbContext.DrugProducts.GetRandomEntity();

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"isActive=>{randomProduct.IsActive}", $"name=>{randomProduct.Name}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.IsActive.Equals(randomProduct.IsActive) && x.Name.Equals(randomProduct.Name));
    }

    [Fact]
    public async Task GetPagedProducts_ValidSubstanceFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.DrugSubstances).OrderBy(r => Guid.NewGuid()).FirstAsync();

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"substances=>{product.DrugSubstances.First().Code}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Substances.Select(s => s.Name).Contains(product.DrugSubstances.First().Name));
        responseObj.Data.Should().Contain(x => x.Substances.Select(s => s.Code).Contains(product.DrugSubstances.First().Code));

    }

    [Fact]
    public async Task GetPagedProducts_ValidProductCodeFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var pcid = product.ProductExtensions.First().PCID;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"productcode=>{pcid}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.Select(e => e.Pcid).Contains(pcid));
    }

    [Fact]
    public async Task GetPagedProducts_ValidDosageFormFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).ThenInclude(pe => pe.DosageForm).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var dosageFormId = product.ProductExtensions.First().DosageForm.Id;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"dosageform=>{dosageFormId}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.Select(e => e.DosageFormId).Contains(dosageFormId));
    }

    [Fact]
    public async Task GetPagedProducts_ValidRoutesOfAdministrationFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).ThenInclude(pe => pe.RouteOfAdministrations).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var routeId = product.ProductExtensions.First().RouteOfAdministrations.First().Id;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"routeofadministration=>{routeId}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.SelectMany(e => e.RouteOfAdministrationIds).Contains(routeId));
    }

    [Fact]
    public async Task GetPagedProducts_ValidDosageFormRoutesOfAdministrationFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).ThenInclude(pe => pe.DosageForm)
            .Include(p => p.ProductExtensions).ThenInclude(pe => pe.RouteOfAdministrations)
            .OrderBy(r => Guid.NewGuid())
            .FirstAsync();
        var dosageFormId = product.ProductExtensions.First().DosageForm.Id;
        var routeId = product.ProductExtensions.First().RouteOfAdministrations.First().Id;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"routeofadministration=>{routeId}", $"dosageform=>{dosageFormId}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.SelectMany(e => e.RouteOfAdministrationIds).Contains(routeId)
                                               && x.ProductExtensions.Select(e => e.DosageFormId).Contains(dosageFormId));
    }

    [Fact]
    public async Task GetPagedProducts_ValidProductNameRoutesOfAdministrationFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).ThenInclude(pe => pe.RouteOfAdministrations).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var productName = product.Name;
        var routeId = product.ProductExtensions.First().RouteOfAdministrations.First().Id;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"routeofadministration=>{routeId}", $"name=>{productName}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.SelectMany(e => e.RouteOfAdministrationIds).Contains(routeId) && x.Name.Equals(productName));
    }

    [Fact]
    public async Task GetPagedProducts_ValidProductNameProductCodeFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var product = await dbContext.DrugProducts.Include(p => p.ProductExtensions).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var productName = product.Name;
        var productCode = product.ProductExtensions.First().PCID;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            new List<string> { $"productcode=>{productCode}", $"name=>{productName}" }, 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.ProductExtensions.Select(e => e.Pcid).Contains(productCode) && x.Name.Equals(productName));
    }

    [Fact]
    public async Task GetPagedProducts_ValidProductNameSubstanceFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 21);
        var randomProduct = await dbContext.DrugProducts.Include(p => p.DrugSubstances).OrderBy(r => Guid.NewGuid()).FirstAsync();
        var productName = randomProduct.Name;
        var substance = randomProduct.DrugSubstances.First().Code;

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT,
            [$"substances=>{substance}", $"name=>{productName}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Substances.Select(s => s.Code).Contains(substance) && x.Name.Equals(productName));
    }

    [Fact]
    public async Task GetPagedProducts_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await ProductsTestEntitiesBuilder.Build(dbContext, 55);

        //Act
        var responseObj = await apiProducts.GetPagedProductsListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15);

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    [Fact]
    public async Task GetProductsListQuery_GetRequest_ReturnsOk()
    {
        //Arrange
        var product1 = ProductBuilder.Default().WithName(Fake.Product.Name).WithIsActive(true).Build();
        var product2 = ProductBuilder.Default().WithName(Fake.Product.Name).WithIsActive(true).Build();
        var product3 = ProductBuilder.Default().WithName(Fake.Product.Name).WithIsActive(true).Build();
        await dbContext.DrugProducts.AddRangeAsync(product1, product2, product3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await apiProducts.GetProductsListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(3);
        var responsesIsActive = responseObj.Data.Select(x => x.IsActive).ToArray();
        responsesIsActive.Should().Contain(product1.IsActive);
        responsesIsActive.Should().Contain(product2.IsActive);
        responsesIsActive.Should().Contain(product3.IsActive);

        dbContext.DrugProducts.RemoveRange(product1, product2, product3);
        await dbContext.SaveChangesAsync();
    }

    [Fact]
    public async Task GetProductById_GetRequest_ReturnsOk()
    {
        //Arrange
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 10);

        //Act
        var productId = products[0].Id;
        var responseObj = await apiProducts.GetProductAsync(productId, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Id.Should().Be(productId);
    }

    [Fact]
    public async Task GetProductById_ProductDoesNotExits_ReturnsEntityNotFoundException()
    {
        //Arrange
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 1);
        // an ID that does not exist in the DB
        var searchProductId = products[0].Id + 1;

        //Act
        var responseObj = () => apiProducts.GetProductAsync(searchProductId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await responseObj.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("EntityNotFoundException");
    }

    [Fact]
    public async Task GetProductById_GetRequest_CorrectlySetsIsAssociatedToComment()
    {
        //Arrange

        // populate products
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtension1 = new ProductExtensionModel(
            pcid: Fake.ProductExtension.PCID,
            dosageFormId: dosageForm.Id, routeOfAdministrationIds: [routeOfAdministration.Id]);
        var productExtension2 = new ProductExtensionModel(
            pcid: Fake.ProductExtension.PCID,
            dosageFormId: dosageForm.Id, routeOfAdministrationIds: [routeOfAdministration.Id]);

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithProductExtensions(productExtension1, productExtension2)
            .WithDrugSubstances(drugSubstances.Select(d => d.Id).ToList())
            .WithProductTypes(productTypeIds)
            .Build();

        var createProductResponse = await apiProducts.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await apiProducts.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        // populate communications
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productResponse.ProductExtensions[0].Id,
                RouteOfAdministrationIds = productExtension1.RouteOfAdministrationIds.Select(x => x).ToList()
            })
            .WithDrugSubstanceIds(productResponse.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        // build request
        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Act
        var responseObj = await apiProducts.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Id.Should().Be(createProductResponse.Id);
        responseObj.ProductExtensions.Count.Should().Be(2);

        responseObj.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);

        responseObj.ProductExtensions[0].IsAssociatedToComment.Should().Be(true);
        responseObj.ProductExtensions[1].IsAssociatedToComment.Should().Be(false);
    }

    public async Task InitializeAsync()
    {
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        await RouteOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();

        await dbContext.SaveChangesAsync();
        dbContext.ChangeTracker.Clear();
    }
}
