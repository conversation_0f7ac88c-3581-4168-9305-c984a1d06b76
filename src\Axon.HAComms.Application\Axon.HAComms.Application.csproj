﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
   <InternalsVisibleTo Include="Axon.HAComms.Api" />
   <InternalsVisibleTo Include="Axon.HAComms.Infrastructure" />
   <InternalsVisibleTo Include="Axon.HAComms.Tests" />
	 <InternalsVisibleTo Include="DynamicProxyGenAssembly2, PublicKey=0024000004800000940000000602000000240000525341310004000001000100c547cac37abd99c8db225ef2f6c8a3602f3b3606cc9891605d02baa56104f4cfc0734aa39b93bf7852f7d9266654753cc297e7d2edfe0bac1cdcf9f717241550e0a7b191195b7667bb4f64bcb8e2121380fd1d9d46ad2d92d2d15605093924cceaf74c4861eff62abf69b9291ed0a340e113be11e6a7d3113e92484cf7045cc7" />
  </ItemGroup>
	
  <ItemGroup>
	 <PackageReference Include="AutoMapper" Version="13.0.1" />
	 <PackageReference Include="Axon.Core.Shared" Version="1.0.994" />
	 <PackageReference Include="Azure.Search.Documents" Version="11.5.1" />
	 <PackageReference Include="JetBrains.Annotations" Version="2024.3.0" />
	 <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
	 <PackageReference Include="Microsoft.AspNetCore.OData" Version="9.1.1" />
	 <PackageReference Include="Phlex.Core.Api.Abstractions" Version="23.0.0.2" />
	 <PackageReference Include="MediatR" Version="12.2.0" />
	 <PackageReference Include="SendGrid" Version="9.29.3" />
	 <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
  </ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\Axon.HAComms.Domain\Axon.HAComms.Domain.csproj" />
  </ItemGroup>

</Project>
