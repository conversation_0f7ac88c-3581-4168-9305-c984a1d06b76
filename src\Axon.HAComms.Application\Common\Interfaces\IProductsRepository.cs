﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IProductsRepository : IRepository<Product>
{
    Task<Product> GetItemAsync(int id);
    Task<IEnumerable<Product>> GetItemsAsync(Expression<Func<Product, object>> orderByExpression);

    IQueryable<Product> GetQueryableItems();

    Task<bool> ExistsAsync(Expression<Func<Product, bool>> filter);

    Task DeleteItemAsync(int id);
}
