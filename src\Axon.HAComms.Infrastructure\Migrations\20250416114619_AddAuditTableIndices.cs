﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Axon.HAComms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAuditTableIndices : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Audit_EventCategory",
                table: "Audit",
                column: "EventCategory");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_EventOutcome",
                table: "Audit",
                column: "EventOutcome");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_EventType",
                table: "Audit",
                column: "EventType");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_IpAddress",
                table: "Audit",
                column: "IpAddress");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_StartDate",
                table: "Audit",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_Audit_UserEmail",
                table: "Audit",
                column: "UserEmail");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Audit_EventCategory",
                table: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_Audit_EventOutcome",
                table: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_Audit_EventType",
                table: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_Audit_IpAddress",
                table: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_Audit_StartDate",
                table: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_Audit_UserEmail",
                table: "Audit");
        }
    }
}
