name: "$(Date:yyyyMMdd).$(Rev:r)"

pool:
  vmImage: ubuntu-latest

trigger:
  - develop
  - release/*
  - hotfix/*

variables:
  - name: Source_Branch
    value: $[replace(variables['Build.SourceBranch'],'refs/heads/','')]
  - name: Version_Number
    value: "1.0"
  - name: Build_Number
    value: $[counter(variables['Version_Number'], 0)]
  - name: Build_Configuration
    value: "Release"

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

stages:
  - stage: Prerequisites
    jobs:
      - job: CalculateVersion
        displayName: "Calculate Version"
        steps:
          - template: General/calculate-version.yml@templates
            parameters:
              VersionNumber: "$(Version_Number)"
              BuildNumber: "$(Build_Number)"
              BranchName: "$(Source_Branch)"
  - stage: BuildSDKs
    jobs:
      - job: BuildTypescriptSdk
        displayName: "Build and publish typescript SDK"
        steps:
          - template: Build/npm/npm-build-publish.yml@templates
            parameters:
              VersionNumber: "$(Build.BuildNumber)"
              SonarProjectKey: ""
              SonarProjectName: ""
              AppPath: "src/Axon.HAComms.Api.Sdk.Typescript"
              NpmrcFile: ""
              SkipTests: true
              PublishPackage: true
      - job: BuildCSharpSDK
        displayName: "Build and publish C# SDK"
        steps:
          - template: Nuget/nuget-publish.yml@templates
            parameters:
              BuildConfiguration: "$(Build_Configuration)"
              PackagesToPack: "src/Axon.HAComms.Api.Sdk.Net/Axon.HAComms.Api.Sdk.Net.csproj"
              PublishNuget: true
              NoBuild: false
