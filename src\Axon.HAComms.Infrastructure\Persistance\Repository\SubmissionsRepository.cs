﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class SubmissionsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<SubmissionsRepository> logger)
    : SqlServerRepository<Submission>(context, tenant, logger), ISubmissionsRepository
{
    public async Task<IEnumerable<string>> GetNumbersAsync()
    {
        return await context
            .Set<Submission>()
            .OrderBy(x => x.Number)
            .Select(x => x.Number)
            .Distinct()
            .AsNoTracking()
            .ToListAsync();
    }
}
