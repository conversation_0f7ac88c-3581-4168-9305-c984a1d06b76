﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.DrugSubstances.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.DrugSubstances.ListQuery;

public class GetDrugSubstancesListQueryHandlerTests
{
    private readonly GetDrugSubstancesListQueryHandler handler;
    private readonly IDrugSubstancesRepository drugSubstancesRepo;

    public GetDrugSubstancesListQueryHandlerTests()
    {
        drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DrugSubstancesMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetDrugSubstancesListQueryHandler(drugSubstancesRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = new List<DrugSubstance>
        {
            new DrugSubstancesBuilder().Build(),
            new DrugSubstancesBuilder().Build(),
            new DrugSubstancesBuilder().Build()
        };

        drugSubstancesRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetDrugSubstancesListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Code).Should().Contain(queryableItems[0].Code);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Code).Should().Contain(queryableItems[1].Code);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Code).Should().Contain(queryableItems[2].Code);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        var drugSubstance1 = "test1";
        var drugSubstance2 = "test2";
        var drugSubstance3 = "test3";

        //Arrange
        var queryableItems = new List<DrugSubstance>
        {
            new DrugSubstancesBuilder().WithCode(drugSubstance3).Build(),
            new DrugSubstancesBuilder().WithCode(drugSubstance2).Build(),
            new DrugSubstancesBuilder().WithCode(drugSubstance1).Build()
        };

        drugSubstancesRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetDrugSubstancesListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInAscendingOrder(x => x.Code);
    }
}
