﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Applications;
using Axon.HAComms.Tests.Builders.Comments;
using Axon.HAComms.Tests.Builders.Country;
using Axon.HAComms.Tests.Builders.SubmissionType;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Communications
{
    public class CommunicationsBuilder : IBuilder<Communication>
    {
        private int id;
        private string subject;
        private readonly string lastUpdatedBy;
        private readonly ICollection<ApplicationBuilder> applicationsBuilder;
        private string createdBy;
        private DateTime createdDate;
        private readonly DateTime lastUpdatedDate;
        private DateTime dateOfCommunication;
        private bool isCompleted;
        private SubmissionTypeBuilder submissionTypeBuilder;
        private CountryBuilder countryBuilder;
        private readonly List<CommentsBuilder> commentsBuilder;

        public CommunicationsBuilder()
        {
            id = Fake.Communication.Id;
            subject = Fake.Communication.Subject;
            isCompleted = false;
            createdBy = Fake.Communication.CreatedBy;
            createdDate = DateTime.Now;
            lastUpdatedDate = DateTime.Now;
            dateOfCommunication = DateTime.Now;
            lastUpdatedBy = Fake.Communication.LastUpdatedBy;
            applicationsBuilder = [];
            commentsBuilder = [];
            countryBuilder = new CountryBuilder();
            submissionTypeBuilder = new SubmissionTypeBuilder();
        }

        public Communication Build()
        {
            return new Communication(id)
            {
                Subject = subject,
                Applications = applicationsBuilder.Select(x => x.Build()).ToList(),
                DateOfCommunication = dateOfCommunication,
                IsCompleted = isCompleted,
                Comments = commentsBuilder.Select(x => x.Build()).ToList(),
                CreatedBy = createdBy,
                CreatedDate = createdDate,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy,
                SubmissionType = submissionTypeBuilder.Build(),
                Country = countryBuilder.Build()
            };
        }

        public CommunicationsBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public CommunicationsBuilder WithSubject(string subject)
        {
            this.subject = subject;
            return this;
        }

        public CommunicationsBuilder WithIsCompleted(bool isCompleted)
        {
            this.isCompleted = isCompleted;
            return this;
        }

        public CommunicationsBuilder WithDateCreated(DateTime createdDate)
        {
            this.createdDate = createdDate;
            return this;
        }

        public CommunicationsBuilder WithDateOfCommunication(DateTime dateOfCommunication)
        {
            this.dateOfCommunication = dateOfCommunication;
            return this;
        }

        public CommunicationsBuilder WithCreatedBy(string createdBy)
        {
            this.createdBy = createdBy;
            return this;
        }

        public CommunicationsBuilder WithCountry(Action<CountryBuilder> action)
        {
            var builder = new CountryBuilder();
            action.Invoke(builder);
            countryBuilder = builder;
            return this;
        }

        public CommunicationsBuilder WithSubmissionType(Action<SubmissionTypeBuilder> action)
        {
            var builder = new SubmissionTypeBuilder();
            action.Invoke(builder);
            submissionTypeBuilder = builder;
            return this;
        }

        public CommunicationsBuilder WithComment(Action<CommentsBuilder> action)
        {
            var builder = new CommentsBuilder();
            action.Invoke(builder);
            commentsBuilder.Add(builder);
            return this;
        }
    }
}
