﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductProductTypesConfiguration : IEntityTypeConfiguration<ProductProductTypes>
{
    public void Configure(EntityTypeBuilder<ProductProductTypes> builder)
    {
        builder.ToTable("ProductProductTypes");

        builder.HasKey(x => x.Id);
    }
}
