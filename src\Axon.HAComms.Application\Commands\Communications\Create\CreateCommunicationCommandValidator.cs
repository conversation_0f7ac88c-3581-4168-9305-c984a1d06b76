using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Communications.Create;

[UsedImplicitly]
public class CreateCommunicationCommandValidator : AbstractValidator<CreateCommunicationCommandRequest>
{
    public CreateCommunicationCommandValidator(ISubmissionTypesRepository subTypeRepo, ICountriesRepository countryRepo)
    {
        this.AddCommunicationRequestValidation(subTypeRepo, countryRepo);

        this.RuleFor(x => x.Comment)
            .Cascade(CascadeMode.Stop)
            .NotNull()
            .SetValidator(new CommentValidator());
    }
}
