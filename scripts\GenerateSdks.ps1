Param(
	[String]$JDKdownloadUrl = "https://download.oracle.com/java/17/latest/jdk-17_windows-x64_bin.exe",  
	[String]$OpenApiGeneratorDownloadUrl = "https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.6.0/openapi-generator-cli-7.6.0.jar"
)

function SetupPrerequisites()
{
	if (Get-Command java | Select-Object Version) 
	{
		Write-Host 'Java already installed.'  -ForegroundColor black -BackgroundColor green
	}
	else 
	{
		Write-Host "Java not found." -ForegroundColor red -BackgroundColor black
		DownloadAndInstallJava	
	}

	$openapiCheckResult = Test-Path ".\openapi-generator-cli.jar"
	if ($openapiCheckResult)
	{
		Write-Host "openapi-generator-cli.jar found." -ForegroundColor black -BackgroundColor green
	}
	else
	{
		Write-Host "Openapi-generator-cli not found. Downloading." -ForegroundColor red -BackgroundColor black
		Invoke-WebRequest -OutFile openapi-generator-cli.jar $OpenApiGeneratorDownloadUrl
	}
	
	if (dotnet tool list | Select-String "swagger") 
	{
		Write-Host 'Swagger already installed.' -ForegroundColor black -BackgroundColor green
	}
	else 
	{
		Write-Host "Swagger not found. Installing." -ForegroundColor red -BackgroundColor black
		dotnet new tool-manifest --force
		dotnet tool install --version 6.9.0 Swashbuckle.AspNetCore.Cli --ignore-failed-sources
	}
}

#returns X:\my\path\Phlex.Name.Api\src\Phlex.Name.Api.Sdk.Net
#projectsuffix is ".Sdk.Net" or ".Sdk.Typescript"
function GetPathForSdk()
{
	Param($projectsuffix)
	$path = $PSScriptRoot.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"

	$projectname = GetPackageNameFromPath $PSScriptRoot 
	$projectname = $projectname + $projectsuffix

	$projectpath = $path + "\src\" + $projectname

	return $projectpath
}

#returns Phlex.Name.Api from X:\my\path\Phlex.Name.Api\scripts
function GetPackageNameFromPath()
{
	Param($path)
	$path = $path.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"

	$packagepath = (Get-ChildItem -Path $path -Filter "*.Api*" -Recurse -Directory).Fullname | Select-Object -First 1

	$packagename = $packagepath.Split("\") | Select-Object -Last 1
	$packagename = $packagename -join "\"

	return $packagename
}

function GenerateSwagger()
{
	$path = $PSScriptRoot.Split("\") | Select-Object -SkipLast 1
	$path = $path -join "\"
	
	$packagename = GetPackageNameFromPath $PSScriptRoot

	$path = $path + "\src\" + $packagename + "\bin\Debug"
	$depsjson = $packagename + ".deps.json"
	
	if (-Not (Test-Path -Path $path))
	{
		Write-Host "$path does not exist. Please build the API project." -ForegroundColor red -BackgroundColor black
		Exit
	}

	$depsjsonpath = Get-ChildItem -Path $path -Recurse $depsjson | Sort CreationTime -Descending | Select-Object -Property FullName -First 1
	$depsjsonfullname = $depsjsonpath.FullName
	
	if (($depsjsonfullname -eq $null) -Or (-Not (Test-Path -Path $depsjsonfullname)))
	{
		Write-Host "$depsjson does not exist in $path. Please build the API project." -ForegroundColor red -BackgroundColor black
		Exit
	}
	
	$parts = $depsjsonfullname.Split(".") | Select-Object -SkipLast 2
	$parts = $parts -join "."
	$dllpath = $parts + ".dll"
	
	if (-Not (Test-Path -Path $dllpath))
	{
		Write-Host "$packagename.dll does not exist in $dllpath." -ForegroundColor red -BackgroundColor black
		Exit
	}

	#solution for cosmodb config issue
	#copy appsettings.json to script location
	$appsettingsjson = "appsettings.json"
	$appsettingsjsonpath = Get-ChildItem -Path $path -Recurse $appsettingsjson | Sort CreationTime -Descending | Select-Object -Property FullName -First 1
	$appsettingsjsonfullname = $appsettingsjsonpath.FullName

	if (($appsettingsjsonfullname -eq $null) -Or (-Not (Test-Path -Path $appsettingsjsonfullname)))
	{
		Write-Host "$appsettingsjson does not exist in $appsettingsjsonfullname." -ForegroundColor red -BackgroundColor black
		Exit
	}

	if (-Not (Test-Path -Path $appsettingsjsonfullname))
	{
		Write-Host "$appsettingsjson does not exist in $appsettingsjsonfullname." -ForegroundColor red -BackgroundColor black
		Exit
	}
	
	Copy-Item -Path $appsettingsjsonfullname -Destination ".\appsettings.json"
	dotnet swagger tofile --output swagger.json $dllpath v1
	Remove-Item -Force ".\appsettings.json"
}

function GenerateCsharpWithOpenApi()
{
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$packagename = $packagename + ".Sdk.Net"
	$path = GetPathForSdk ".Sdk.Net"
	$path = $path + "\temp"
	
	Write-Host "Generating csharp files with OpenApi" -ForegroundColor black -BackgroundColor green
	
	java -jar openapi-generator-cli.jar generate -i swagger.json -g csharp -o $path --additional-properties=netCoreProjectFile=true,packageName=$packagename,targetFramework=net8.0 --library httpclient
	
	Write-Host "Generating csharp finished" -ForegroundColor black -BackgroundColor green
}

function GenerateTypeScriptWithOpenApi()
{
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$path = GetPathForSdk ".Sdk.Typescript"
	$path = $path + "\temp"
	
	Write-Host "Generating TypeScript files with OpenApi" -ForegroundColor black -BackgroundColor green
	
	java -jar openapi-generator-cli.jar generate -i swagger.json -g typescript-axios -o $path --skip-validate-spec --additional-properties=packageName=$packagename --global-property apis,models,supportingFiles,apiTests=false,apiDocs=false,modelTests=false,modelDocs=false
	
	Write-Host "Generating TypeScript finished" -ForegroundColor black -BackgroundColor green
}

function RemoveFiles()
{
	Write-Host 'Removing redundant files'
	
	#remove from sdk netcore
	$packagename = GetPackageNameFromPath $PSScriptRoot
	$packagename = $packagename + ".Sdk.Net"
	$targetDir = GetPathForSdk ".Sdk.Net"
	$path = $targetDir + "\temp\src\" + $packagename + "\*"
	Copy-Item -Path $path -Destination $targetDir -Force -Recurse
	$removesrc = $targetDir + "\temp"
	Remove-Item -Recurse -Force $removesrc
	
	#remove from sdk typescript
	$targetDir = GetPathForSdk ".Sdk.Typescript"
	
	$path = $targetDir + "\temp\*.ts"
	Copy-Item -Path $path -Destination $targetDir -Force -Recurse
	$removesrc = $targetDir + "\temp"
	Remove-Item -Recurse -Force $removesrc
	
	Write-Host 'Removing files finished'
}

function DownloadAndInstallJava()
{
	#download java
	Write-Host "Downloading Java. Please wait." -ForegroundColor black -BackgroundColor green

	Invoke-WebRequest -UseBasicParsing $JDKdownloadUrl -OutFile jdk.exe
	
	$path = $PSScriptRoot + "\jdk.exe"
	Write-Host "Installing Java. Please wait." -ForegroundColor black -BackgroundColor green
	
	try
	{
		Start-Process -FilePath $path -passthru -wait -argumentlist /s
		Write-Host "Java installed successfully" -ForegroundColor black -BackgroundColor green
	}
	catch
	{
		Write-Host "Error occured while installing Java." -ForegroundColor red -BackgroundColor black
		Exit
	}
	finally
	{
		Write-Host "Removing instalation file." -ForegroundColor black -BackgroundColor green
		Remove-Item -Path $path -Force
	}	
}

SetupPrerequisites
GenerateSwagger
GenerateCsharpWithOpenApi
GenerateTypeScriptWithOpenApi
RemoveFiles
