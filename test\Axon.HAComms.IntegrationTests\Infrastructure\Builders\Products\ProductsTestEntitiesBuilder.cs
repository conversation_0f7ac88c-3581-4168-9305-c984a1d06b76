using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;

public static class ProductsTestEntitiesBuilder
{
    public static async Task<List<Product>> Build(HACommsContext dbContext, int entries = 100)
    {
        var list = new List<Product>();
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();

        for (var i = 0; i < entries; i++)
        {
            var drugSubstance = await dbContext.DrugSubstances.GetRandomEntity();
            var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();

            var productExtension = new ProductExtension()
            {
                PCID = Fake.ProductExtension.PCID,
                DosageForm = dosageForm,
                RouteOfAdministrations = [routeOfAdministration],
                IsActive = true,
                Tenant = TenantConstants.DEFAULT_TENANT
            };
            var productType = ApiTestHelper.NotCategorizedProductType(dbContext);

            var product = ProductBuilder.Default().WithDrugSubstances([drugSubstance]).WithProductExtensions(productExtension).WithProductTypes(productType).Build();

            list.Add(product);
        }

        dbContext.DrugProducts.AddRange(list);
        await dbContext.SaveChangesAsync();
        return list;
    }
}
