﻿using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Queries.DosageForms.PagedListQuery;

internal class GetDosageFormPagedListQueryHandler(IDosageFormsRepository dosageFormRepo) :
    IRequestHandler<GetDosageFormPagedListQueryRequest, ApiPagedListResult<DosageFormPagedListModel>>
{
    private readonly IDosageFormsRepository dosageFormRepo = dosageFormRepo ?? throw new ArgumentNullException(nameof(dosageFormRepo));
    private readonly Dictionary<string, Expression<Func<DosageForm, object>>> sortExpressions =
        new()
        {
            { TableFilterConstants.Name, x => x.Name },
            { TableFilterConstants.CreatedDate, x => x.CreatedDate },
            { TableFilterConstants.CreatedBy, x => x.CreatedBy },
            { TableFilterConstants.LastUpdatedDate, x => x.LastUpdatedDate },
            { TableFilterConstants.LastUpdatedBy, x => x.LastUpdatedBy }
        };

    public async Task<ApiPagedListResult<DosageFormPagedListModel>> Handle(GetDosageFormPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildDosageForm(request.Filters);
        var predicate = BuildSortExpression(request);
        var query = dosageFormRepo.GetQueryableItems();

        var entities = await query
            .FilterItems(expression, predicate, request.Skip, request.Take)
            .Select(x => new DosageFormPagedListModel(x.Id, x.Name, x.ProductExtensions.Count != 0, x.CreatedDate, x.CreatedBy, x.LastUpdatedDate, x.LastUpdatedBy))
            .ToListAsync(cancellationToken: cancellationToken);

        return new ApiPagedListResult<DosageFormPagedListModel>(
            entities,
            new()
            {
                TotalItemCount = expression == null ? await query.CountAsync(cancellationToken) : await query.CountAsync(expression, cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }

    private Func<IQueryable<DosageForm>, IOrderedQueryable<DosageForm>> BuildSortExpression(GetDosageFormPagedListQueryRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Order))
        {
            return sub => sub.OrderBy(x => x.Name);
        }

        var orderSegments = request.Order.Split("=>");
        var propName = orderSegments[0];
        var order = orderSegments[1];
        var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

        if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
        {
            return CompareAndOrderBy(func);
        }

        return sub => sub.OrderBy(x => x.Name);

        Func<IQueryable<DosageForm>, IOrderedQueryable<DosageForm>> CompareAndOrderBy<TKey>(
            Expression<Func<DosageForm, TKey>> expression)
        {
            return isAsc ?
                sub => sub.OrderBy(expression) :
                sub => sub.OrderByDescending(expression);
        }
    }
}
