﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Country;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Countries.ListQuery;

internal class GetCountryListQueryHandler(ICountriesRepository repo, IMapper mapper) : IRequestHandler<GetCountryListQueryRequest, ApiListResult<CountryModel>>
{
    public async Task<ApiListResult<CountryModel>> Handle(GetCountryListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync();
        return new ApiListResult<CountryModel>(mapper.Map<List<CountryModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
