﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class TagRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<TagRepository> logger)
    : SqlServerRepository<Tag>(context, tenant, logger), ITagRepository
{
    public async Task<List<Tag>> GetAllByIdsAsync(params int[] ids)
    {
        var items = await context.Set<Tag>()
            .AsQueryable()
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();
        return items;
    }

    public async Task<IEnumerable<Tag>> GetItemsAsync()
    {
        return await context.Set<Tag>()
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<Tag> GetItemAsync(int id)
    {
        var entity = await context.Set<Tag>().SingleOrDefaultAsync(d => d.Id == id);
        return entity ?? throw new EntityNotFoundException(nameof(Tag), id);
    }

    public IQueryable<Tag> GetQueryableItems()
    {
        return context.Set<Tag>().AsQueryable();
    }

    public async Task<bool> ExistsAsync(Expression<Func<Tag, bool>> filter)
    {
        return await context.Set<Tag>().AsNoTracking().AnyAsync(filter);
    }
}
