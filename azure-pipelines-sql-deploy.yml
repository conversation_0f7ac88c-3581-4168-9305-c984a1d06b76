parameters:
  - name: environments
    type: object
    default:
      - name: 'axon_hac_dev_helm'
        prefix: 'dev'
        azureSubscription: 'Axon_Development'
      - name: 'axon_hac_int_helm'
        prefix: 'int'
        azureSubscription: 'Axon_Development'
        dependsOn: 'axon_hac_dev_helm'
      - name: 'axon_hac_stg_helm'
        prefix: 'stg'
        azureSubscription: 'Axon_Development'
        dependsOn: 'axon_hac_int_helm'
      - name: 'axon_hac_uat_helm'
        prefix: 'uat'
        azureSubscription: 'Axon_Production'
        dependsOn: 'axon_hac_stg_helm'
      - name: 'axon_hac_prodeu_helm'
        prefix: 'prod'
        azureSubscription: 'Axon_Production'
        dependsOn: 'axon_hac_stg_helm'

name: '$(Date:yyyyMMdd).$(Rev:r)'

pool: pv-windows-pool

trigger: none
pr: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: Axon.HAComms.Api.Build
      trigger:
        branches:
          include:
          - develop
  repositories:
    - repository: axon-templates
      name: Axon/Axon.Pipelines.Templates
      type: git

variables:
  - template: Variables/environments.yml@axon-templates

stages:
  - ${{ each env in parameters.Environments }}:
      - stage: '${{ env.name }}_sql_deploy'
        displayName: '${{ env.name }} Deploy SQL'
        variables:
          platform: 'hac'
          region: 'eun'
        jobs:
          - deployment: DeploySQL
            displayName: 'Deploy SQL dacpac ${{ env.name }}'
            environment: ${{ env.name }}
            strategy:
              runOnce:
                deploy:
                  steps:
                    - task: DownloadPipelineArtifact@2
                      inputs:
                        source: 'specific'
                        project: $(resources.pipeline.build-pipeline.projectID)
                        pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                        runVersion: 'specific'
                        runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                        runId: $(resources.pipeline.build-pipeline.runID)
                        path: '$(System.DefaultWorkingDirectory)'
                    - task: SqlAzureDacpacDeployment@1
                      displayName: 'Azure SQL SqlTask'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        AuthenticationType: servicePrincipal
                        ServerName: '${{ variables.platform }}-${{ env.prefix }}-sqlserver-${{ variables.region }}.database.windows.net'
                        DatabaseName: '${{ variables.platform }}-${{ env.prefix }}-default-${{ variables.region }}'
                        deployType: SqlTask
                        SqlFile: '$(System.DefaultWorkingDirectory)/drop/Migrations/migration.sql'
