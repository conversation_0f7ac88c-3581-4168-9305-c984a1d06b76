/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CommunicationPagedListModel
    /// </summary>
    [DataContract(Name = "CommunicationPagedListModel")]
    public partial class CommunicationPagedListModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationPagedListModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected CommunicationPagedListModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationPagedListModel" /> class.
        /// </summary>
        /// <param name="id">id (required).</param>
        /// <param name="subject">subject.</param>
        /// <param name="productNames">productNames.</param>
        /// <param name="countryId">countryId.</param>
        /// <param name="countryName">countryName.</param>
        /// <param name="dateOfCommunication">dateOfCommunication.</param>
        /// <param name="createdDate">createdDate.</param>
        /// <param name="createdBy">createdBy.</param>
        /// <param name="isCompleted">isCompleted.</param>
        public CommunicationPagedListModel(int id = default(int), string subject = default(string), string productNames = default(string), int countryId = default(int), string countryName = default(string), DateTime dateOfCommunication = default(DateTime), DateTime? createdDate = default(DateTime?), string createdBy = default(string), bool isCompleted = default(bool))
        {
            this.Id = id;
            this.Subject = subject;
            this.ProductNames = productNames;
            this.CountryId = countryId;
            this.CountryName = countryName;
            this.DateOfCommunication = dateOfCommunication;
            this.CreatedDate = createdDate;
            this.CreatedBy = createdBy;
            this.IsCompleted = isCompleted;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", IsRequired = true, EmitDefaultValue = true)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Subject
        /// </summary>
        [DataMember(Name = "subject", EmitDefaultValue = true)]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or Sets ProductNames
        /// </summary>
        [DataMember(Name = "productNames", EmitDefaultValue = true)]
        public string ProductNames { get; set; }

        /// <summary>
        /// Gets or Sets CountryId
        /// </summary>
        [DataMember(Name = "countryId", EmitDefaultValue = false)]
        public int CountryId { get; set; }

        /// <summary>
        /// Gets or Sets CountryName
        /// </summary>
        [DataMember(Name = "countryName", EmitDefaultValue = true)]
        public string CountryName { get; set; }

        /// <summary>
        /// Gets or Sets DateOfCommunication
        /// </summary>
        [DataMember(Name = "dateOfCommunication", EmitDefaultValue = false)]
        public DateTime DateOfCommunication { get; set; }

        /// <summary>
        /// Gets or Sets CreatedDate
        /// </summary>
        [DataMember(Name = "createdDate", EmitDefaultValue = true)]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// Gets or Sets CreatedBy
        /// </summary>
        [DataMember(Name = "createdBy", EmitDefaultValue = true)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or Sets IsCompleted
        /// </summary>
        [DataMember(Name = "isCompleted", EmitDefaultValue = true)]
        public bool IsCompleted { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CommunicationPagedListModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Subject: ").Append(Subject).Append("\n");
            sb.Append("  ProductNames: ").Append(ProductNames).Append("\n");
            sb.Append("  CountryId: ").Append(CountryId).Append("\n");
            sb.Append("  CountryName: ").Append(CountryName).Append("\n");
            sb.Append("  DateOfCommunication: ").Append(DateOfCommunication).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  CreatedBy: ").Append(CreatedBy).Append("\n");
            sb.Append("  IsCompleted: ").Append(IsCompleted).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
