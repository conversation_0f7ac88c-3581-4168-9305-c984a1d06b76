#nullable enable

namespace Axon.HAComms.Application.Models.DrugSubstances
{
	public class DrugSubstancePagedListModel
	{
        public int Id { get; set; }
        public string? Name { get; set; }
		public string? Code { get; set; }
        public string? Description { get; set; }
        public bool IsAssociatedToComment { get; set; }
        public DateTime? CreatedDate { get; set; }
		public DateTime? LastUpdatedDate { get; set; }
		public string? LastUpdatedBy { get; set; }

        public DrugSubstancePagedListModel()
		{

		}

		public DrugSubstancePagedListModel(
			int id, 
			string? name, 
			string? code,
			string? description,
			bool isAssociatedToComment,
            DateTime? createdDate,
            DateTime? lastUpdatedDate, 
			string? lastUpdatedBy)
		{
            Id = id;
			Name = name;
			Code = code;
            Description = description;
            IsAssociatedToComment = isAssociatedToComment;
            CreatedDate = createdDate;
			LastUpdatedDate = lastUpdatedDate;
			LastUpdatedBy = lastUpdatedBy;
		}
    }
}
