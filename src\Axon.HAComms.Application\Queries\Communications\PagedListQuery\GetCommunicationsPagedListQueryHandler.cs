﻿using AutoMapper;
using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Queries.Communications.PagedListQuery;

internal class GetCommunicationsPagedListQueryHandler(
    ICommunicationsRepository communicationRepo,
    IMapper mapper) : IRequestHandler<GetCommunicationsPagedListQueryRequest, ApiPagedListResult<CommunicationPagedListModel>>
{
    private readonly Dictionary<string, Expression<Func<CommunicationsView, object>>> sortExpressions =
        new()
        {
            { TableFilterConstants.Subject, x => x.Subject! },
            { TableFilterConstants.CountryName, x => x.CountryName },
            { TableFilterConstants.ProductNames, x => x.ProductNames ?? string.Empty },
            { TableFilterConstants.DateOfCommunication, x => x.DateOfCommunication },
            { TableFilterConstants.CreatedDate, x => x.CreatedDate },
            { TableFilterConstants.CreatedBy, x => x.CreatedBy }
        };

    public async Task<ApiPagedListResult<CommunicationPagedListModel>> Handle(GetCommunicationsPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildCommunications(request.Filters);
        var predicate = BuildSortExpression(request);
        var query = communicationRepo.GetCommunicationsView();

        var entities = await query
            .FilterItems(expression, predicate, request.Skip, request.Take)
            .ToListAsync(cancellationToken: cancellationToken);

        return new ApiPagedListResult<CommunicationPagedListModel>(
            entities.Select(mapper.Map<CommunicationPagedListModel>).ToList(),
            new ApiPagedListResult<CommunicationPagedListModel>.PagingInfo
            {
                TotalItemCount = expression == null ? await query.CountAsync(cancellationToken) : await query.CountAsync(expression, cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }

    private Func<IQueryable<CommunicationsView>, IOrderedQueryable<CommunicationsView>> BuildSortExpression(GetCommunicationsPagedListQueryRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Order))
        {
            return sub => sub.OrderBy(x => x.Subject);
        }

        var orderSegments = request.Order.Split("=>");
        var propName = orderSegments[0];
        var order = orderSegments[1];
        var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

        if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out Expression<Func<CommunicationsView, object>>? func))
        {
            return CompareAndOrderBy(func);
        }

        return sub => sub.OrderBy(x => x.Subject);

        Func<IQueryable<CommunicationsView>, IOrderedQueryable<CommunicationsView>> CompareAndOrderBy<TKey>(
            Expression<Func<CommunicationsView, TKey>> expression)
        {
            return isAsc ?
                sub => sub.OrderBy(expression) :
                sub => sub.OrderByDescending(expression);
        }
    }
}
