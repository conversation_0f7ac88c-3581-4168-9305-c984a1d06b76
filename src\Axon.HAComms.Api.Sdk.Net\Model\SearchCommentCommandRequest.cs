/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// SearchCommentCommandRequest
    /// </summary>
    [DataContract(Name = "SearchCommentCommandRequest")]
    public partial class SearchCommentCommandRequest : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchCommentCommandRequest" /> class.
        /// </summary>
        /// <param name="startDate">startDate.</param>
        /// <param name="endDate">endDate.</param>
        /// <param name="products">products.</param>
        /// <param name="dosageForms">dosageForms.</param>
        /// <param name="routesOfAdministration">routesOfAdministration.</param>
        /// <param name="drugSubstances">drugSubstances.</param>
        /// <param name="countries">countries.</param>
        /// <param name="submissionTypes">submissionTypes.</param>
        /// <param name="applicationNumbers">applicationNumbers.</param>
        /// <param name="submissionNumbers">submissionNumbers.</param>
        /// <param name="tags">tags.</param>
        /// <param name="productTypes">productTypes.</param>
        /// <param name="productCodes">productCodes.</param>
        /// <param name="isGeneralGuidance">isGeneralGuidance.</param>
        /// <param name="isQuestionIncluded">isQuestionIncluded.</param>
        /// <param name="searchText">searchText.</param>
        /// <param name="skip">skip.</param>
        /// <param name="take">take.</param>
        /// <param name="sort">sort.</param>
        /// <param name="fuzzy">fuzzy.</param>
        /// <param name="requestType">requestType.</param>
        public SearchCommentCommandRequest(DateTime? startDate = default(DateTime?), DateTime? endDate = default(DateTime?), List<ProductDtoModel> products = default(List<ProductDtoModel>), List<DosageFormModel> dosageForms = default(List<DosageFormModel>), List<RouteOfAdministrationModel> routesOfAdministration = default(List<RouteOfAdministrationModel>), List<DrugSubstanceDtoModel> drugSubstances = default(List<DrugSubstanceDtoModel>), List<CountryModel> countries = default(List<CountryModel>), List<SubmissionTypeModel> submissionTypes = default(List<SubmissionTypeModel>), List<string> applicationNumbers = default(List<string>), List<string> submissionNumbers = default(List<string>), List<TagModel> tags = default(List<TagModel>), List<ProductTypeModel> productTypes = default(List<ProductTypeModel>), List<string> productCodes = default(List<string>), bool? isGeneralGuidance = default(bool?), bool? isQuestionIncluded = default(bool?), string searchText = default(string), int? skip = default(int?), int? take = default(int?), string sort = default(string), bool? fuzzy = default(bool?), string requestType = default(string))
        {
            this.StartDate = startDate;
            this.EndDate = endDate;
            this.Products = products;
            this.DosageForms = dosageForms;
            this.RoutesOfAdministration = routesOfAdministration;
            this.DrugSubstances = drugSubstances;
            this.Countries = countries;
            this.SubmissionTypes = submissionTypes;
            this.ApplicationNumbers = applicationNumbers;
            this.SubmissionNumbers = submissionNumbers;
            this.Tags = tags;
            this.ProductTypes = productTypes;
            this.ProductCodes = productCodes;
            this.IsGeneralGuidance = isGeneralGuidance;
            this.IsQuestionIncluded = isQuestionIncluded;
            this.SearchText = searchText;
            this.Skip = skip;
            this.Take = take;
            this.Sort = sort;
            this.Fuzzy = fuzzy;
            this.RequestType = requestType;
        }

        /// <summary>
        /// Gets or Sets StartDate
        /// </summary>
        [DataMember(Name = "startDate", EmitDefaultValue = true)]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or Sets EndDate
        /// </summary>
        [DataMember(Name = "endDate", EmitDefaultValue = true)]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Gets or Sets Products
        /// </summary>
        [DataMember(Name = "products", EmitDefaultValue = true)]
        public List<ProductDtoModel> Products { get; set; }

        /// <summary>
        /// Gets or Sets DosageForms
        /// </summary>
        [DataMember(Name = "dosageForms", EmitDefaultValue = true)]
        public List<DosageFormModel> DosageForms { get; set; }

        /// <summary>
        /// Gets or Sets RoutesOfAdministration
        /// </summary>
        [DataMember(Name = "routesOfAdministration", EmitDefaultValue = true)]
        public List<RouteOfAdministrationModel> RoutesOfAdministration { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstances
        /// </summary>
        [DataMember(Name = "drugSubstances", EmitDefaultValue = true)]
        public List<DrugSubstanceDtoModel> DrugSubstances { get; set; }

        /// <summary>
        /// Gets or Sets Countries
        /// </summary>
        [DataMember(Name = "countries", EmitDefaultValue = true)]
        public List<CountryModel> Countries { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionTypes
        /// </summary>
        [DataMember(Name = "submissionTypes", EmitDefaultValue = true)]
        public List<SubmissionTypeModel> SubmissionTypes { get; set; }

        /// <summary>
        /// Gets or Sets ApplicationNumbers
        /// </summary>
        [DataMember(Name = "applicationNumbers", EmitDefaultValue = true)]
        public List<string> ApplicationNumbers { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionNumbers
        /// </summary>
        [DataMember(Name = "submissionNumbers", EmitDefaultValue = true)]
        public List<string> SubmissionNumbers { get; set; }

        /// <summary>
        /// Gets or Sets Tags
        /// </summary>
        [DataMember(Name = "tags", EmitDefaultValue = true)]
        public List<TagModel> Tags { get; set; }

        /// <summary>
        /// Gets or Sets ProductTypes
        /// </summary>
        [DataMember(Name = "productTypes", EmitDefaultValue = true)]
        public List<ProductTypeModel> ProductTypes { get; set; }

        /// <summary>
        /// Gets or Sets ProductCodes
        /// </summary>
        [DataMember(Name = "productCodes", EmitDefaultValue = true)]
        public List<string> ProductCodes { get; set; }

        /// <summary>
        /// Gets or Sets IsGeneralGuidance
        /// </summary>
        [DataMember(Name = "isGeneralGuidance", EmitDefaultValue = true)]
        public bool? IsGeneralGuidance { get; set; }

        /// <summary>
        /// Gets or Sets IsQuestionIncluded
        /// </summary>
        [DataMember(Name = "isQuestionIncluded", EmitDefaultValue = true)]
        public bool? IsQuestionIncluded { get; set; }

        /// <summary>
        /// Gets or Sets SearchText
        /// </summary>
        [DataMember(Name = "searchText", EmitDefaultValue = true)]
        public string SearchText { get; set; }

        /// <summary>
        /// Gets or Sets Skip
        /// </summary>
        [DataMember(Name = "skip", EmitDefaultValue = true)]
        public int? Skip { get; set; }

        /// <summary>
        /// Gets or Sets Take
        /// </summary>
        [DataMember(Name = "take", EmitDefaultValue = true)]
        public int? Take { get; set; }

        /// <summary>
        /// Gets or Sets Sort
        /// </summary>
        [DataMember(Name = "sort", EmitDefaultValue = true)]
        public string Sort { get; set; }

        /// <summary>
        /// Gets or Sets Fuzzy
        /// </summary>
        [DataMember(Name = "fuzzy", EmitDefaultValue = true)]
        public bool? Fuzzy { get; set; }

        /// <summary>
        /// Gets or Sets RequestType
        /// </summary>
        [DataMember(Name = "requestType", EmitDefaultValue = true)]
        public string RequestType { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SearchCommentCommandRequest {\n");
            sb.Append("  StartDate: ").Append(StartDate).Append("\n");
            sb.Append("  EndDate: ").Append(EndDate).Append("\n");
            sb.Append("  Products: ").Append(Products).Append("\n");
            sb.Append("  DosageForms: ").Append(DosageForms).Append("\n");
            sb.Append("  RoutesOfAdministration: ").Append(RoutesOfAdministration).Append("\n");
            sb.Append("  DrugSubstances: ").Append(DrugSubstances).Append("\n");
            sb.Append("  Countries: ").Append(Countries).Append("\n");
            sb.Append("  SubmissionTypes: ").Append(SubmissionTypes).Append("\n");
            sb.Append("  ApplicationNumbers: ").Append(ApplicationNumbers).Append("\n");
            sb.Append("  SubmissionNumbers: ").Append(SubmissionNumbers).Append("\n");
            sb.Append("  Tags: ").Append(Tags).Append("\n");
            sb.Append("  ProductTypes: ").Append(ProductTypes).Append("\n");
            sb.Append("  ProductCodes: ").Append(ProductCodes).Append("\n");
            sb.Append("  IsGeneralGuidance: ").Append(IsGeneralGuidance).Append("\n");
            sb.Append("  IsQuestionIncluded: ").Append(IsQuestionIncluded).Append("\n");
            sb.Append("  SearchText: ").Append(SearchText).Append("\n");
            sb.Append("  Skip: ").Append(Skip).Append("\n");
            sb.Append("  Take: ").Append(Take).Append("\n");
            sb.Append("  Sort: ").Append(Sort).Append("\n");
            sb.Append("  Fuzzy: ").Append(Fuzzy).Append("\n");
            sb.Append("  RequestType: ").Append(RequestType).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
