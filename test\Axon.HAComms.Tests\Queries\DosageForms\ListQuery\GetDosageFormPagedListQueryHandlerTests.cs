﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using FluentAssertions;
using NSubstitute;
using Xunit;
using Axon.HAComms.Tests.Common;
using MockQueryable.NSubstitute;
using Axon.HAComms.Application.Queries.DosageForms.PagedListQuery;
using Axon.HAComms.Tests.Builders.DosageForms;

namespace Axon.HAComms.Tests.Queries.DosageForms.ListQuery;

public class GetDosageFormPagedListQueryHandlerTests
{
    private readonly GetDosageFormPagedListQueryHandler handler;
    private readonly IDosageFormsRepository dosageFormsRepo;

    public GetDosageFormPagedListQueryHandlerTests()
    {
        dosageFormsRepo = Substitute.For<IDosageFormsRepository>();
        handler = new GetDosageFormPagedListQueryHandler(dosageFormsRepo);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(101);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), 100, 10, null);

        // Act			
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var name = Fake.DosageForm.Name;
        var dosageForm = new DosageFormBuilder().WithName(name).Build();
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(24);
        dosageForms.Add(dosageForm);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(new[] { $"name=>{name}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Name.Equals(name));
    }

    [Fact]
    public async Task Handle_ValidDateFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var createdDate = new DateTime(2023, 04, 11);
        var createdBy = Fake.DosageForm.CreatedBy;
        var lastUpdatedDate = new DateTime(2022, 10, 25);
        var lastUpdatedBy = Fake.DosageForm.LastUpdatedBy;

        var substance = new DosageFormBuilder()
            .WithCreatedDate(createdDate)
            .WithCreatedBy(createdBy)
            .WithLastUpdatedDate(lastUpdatedDate)
            .WithLastUpdatedBy(lastUpdatedBy).Build();
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(24);
        dosageForms.Add(substance);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(new[] { $"createdDate=>{createdDate.Year}", $"createdBy=>{createdBy.Substring(0, 5)}", $"lastUpdatedDate=>{lastUpdatedDate.Year}", $"lastUpdatedBy=>{lastUpdatedBy.Substring(0, 5)}" }, 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x =>
            x.CreatedDate.Equals(createdDate) &&
            (x.CreatedBy != null && x.CreatedBy.Equals(createdBy)) &&
            x.LastUpdatedDate.Equals(lastUpdatedDate) &&
            (x.LastUpdatedBy != null && x.LastUpdatedBy.Contains(lastUpdatedBy)));
    }

    [Fact]
    public async Task Handle_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(15);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), 0, 10, "name=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassNullOrderParam_ReturnsOrderedItemsByCode()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(105);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), 10, 50, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }

    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(105);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), -10, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassDateOrderDesc_ReturnsOrderedItems()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate();
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), 10, 10, "createdDate=>desc");

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().BeInDescendingOrder(x => x.CreatedDate);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var dosageForms = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(50);
        var mock = dosageForms.BuildMock();

        dosageFormsRepo.GetQueryableItems().Returns(mock);

        var request = new GetDosageFormPagedListQueryRequest(Array.Empty<string>(), 10, -10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(40);
    }
}
