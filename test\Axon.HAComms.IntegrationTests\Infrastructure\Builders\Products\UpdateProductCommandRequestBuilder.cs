﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;

public class UpdateProductCommandRequestBuilder
{
    private int id;
    private string? name = Fake.Product.Name;
    private bool isActive = Fake.Product.IsActive;
    private List<int> drugSubstanceIds = Fake.Product.DrugSubstanceIds;
    private List<int> productTypeIds = Fake.Product.ProductTypeIds;
    private List<ProductExtensionModel>? productExtensions = [];

    public UpdateProductCommandRequestBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public UpdateProductCommandRequestBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public UpdateProductCommandRequestBuilder WithIsActive(bool isActive)
    {
        this.isActive = isActive;
        return this;
    }

    public UpdateProductCommandRequestBuilder WithDrugSubstances(List<int> drugSubstanceIds)
    {
        this.drugSubstanceIds = drugSubstanceIds;
        return this;
    }

    public UpdateProductCommandRequestBuilder WithProductTypes(List<int> productTypeIds)
    {
        this.productTypeIds = productTypeIds;
        return this;
    }

    public UpdateProductCommandRequestBuilder WithProductExtensions(params ProductExtensionModel[] productExtensions)
    {
        this.productExtensions = productExtensions.ToList();
        return this;
    }

    public UpdateProductCommandRequest Build()
    {
        return new UpdateProductCommandRequest(
            name ?? string.Empty, 
            isActive,
            productTypeIds,
            drugSubstanceIds,
            productExtensions ?? [],
            Constants.NotAssigned,
            id);
    }
}
