﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DosageForms;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateDosageFormIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DosageFormsApi dosageFormsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateDosageForm_ValidRequest_ReturnsOk()
    {
        //Arrange
        var requestObj1 = new CreateDosageFormCommandRequest(Fake.DosageForm.Name);

        //Act
        var responseObj1 = await dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();
        var requestObj2 = new UpdateDosageFormCommandRequest(responseObj1.Id, Fake.DosageForm.Name);

        await dosageFormsApi.UpdateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj2);
        var responseObj = await dosageFormsApi.GetDosageFormListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        var sut = responseObj.Data.SingleOrDefault(s => s.Id == responseObj1.Id);
        sut.Should().NotBeNull();
        sut?.Name.Should().Be(requestObj2.Name);
    }

    [Fact]
    public async Task UpdateDosageForm_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var dosageFormName1 = Fake.DosageForm.Name;
        var dosageFormName2 = Fake.DosageForm.Name;

        var requestObj1 = new CreateDosageFormCommandRequest(dosageFormName1);
        var requestObj2 = new CreateDosageFormCommandRequest(dosageFormName2);

        //Act
        var responseObj1 = await dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj1);
        var responseObj2 = await dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        //Act
        var requestObj3 = new UpdateDosageFormCommandRequest(responseObj1.Id, dosageFormName2);

        var dosageFormResponse = () => dosageFormsApi.UpdateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj3);

        //Assert
        var exception = await dosageFormResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Dosage form with name '{dosageFormName2}' already exists.");
    }

    [Fact]
    public async Task UpdateDosageForm_InvalidDosageFormId_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dosageFormId = Fake.DosageForm.Id;
        var dosageFormName = Fake.DosageForm.Name;
        var request = new UpdateDosageFormCommandRequest(dosageFormId, dosageFormName);

        //Act
        var response = () => dosageFormsApi.UpdateDosageFormAsync(TenantConstants.DEFAULT_TENANT, request);

        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"DosageForm\\\" ({dosageFormId}) was not found.", exception.And.Message);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.DosageForms.Clear();
        await dbContext.SaveChangesAsync();
    }
}
