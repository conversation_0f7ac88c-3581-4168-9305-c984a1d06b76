﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Axon.HAComms.Infrastructure.Persistance;

public class HaCommsDbContextFactory : IDesignTimeDbContextFactory<HACommsContext>
{
    public HACommsContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<HACommsContext>();
        optionsBuilder.UseSqlServer(configuration.GetConnectionString("default"));

        return new HACommsContext(optionsBuilder.Options);
    }
}
