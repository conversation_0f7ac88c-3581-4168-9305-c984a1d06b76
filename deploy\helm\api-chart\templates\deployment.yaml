apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "axon-hacomms-api.fullname" . }}
  labels:
    {{- include "axon-hacomms-api.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_14=Image tag should be set
    checkov.io/skip2: CKV_K8S_21=Default namespace should not be used
    checkov.io/skip3: CKV_K8S_35=Prefer using secrets as files over secrets as environment variables
    checkov.io/skip4: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
    checkov.io/skip5: CKV_K8S_43=Image should use digest
    checkov.io/skip6: CKV_K8S_8=Liveness Probe Should be Configured
    checkov.io/skip7: CKV_K8S_9=Readiness Probe Should be Configured
spec:
  selector:
    matchLabels:
      {{- include "axon-hacomms-api.selectorLabels" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "axon-hacomms-api.selectorLabels" . | nindent 8 }}
        azure.workload.identity/use: "true"
    spec:
      automountServiceAccountToken: false
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      serviceAccountName: {{ .Values.azureWorkload.appName }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          livenessProbe:
            httpGet:
              path: /health/liveness
              port: http
              scheme: HTTP
            failureThreshold: 5
            periodSeconds: 30
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: /health/readiness
              port: http
              scheme: HTTP
            failureThreshold: 5
            periodSeconds: 30
            timeoutSeconds: 3
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          env:
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: newreliclicensekey
                  key: KEY
            - name: NEWRELIC_PROFILER_LOG_DIRECTORY
              value: "\\app"
            - name: NEWRELIC_LOG_LEVEL
              value: "off"
            - name: NEW_RELIC_APP_NAME
              value: axon-hacomms-api
            - name: ASPNETCORE_ENVIRONMENT
              value: {{ .Values.aspNetCoreEnvironment }}
            - name: ASPNETCORE_URLS
              value: "http://+:8080"
            - name: KeyVaultName
              value: {{ .Values.keyVaultName }}
            - name: AzureSearch__IsEnabled
              value: "{{ .Values.azureSearch.isEnabled }}"
            - name: AzureSearch__ServiceName
              value: {{ .Values.azureSearch.serviceName }}
            - name: AzureSearch__IndexName
              value: {{ .Values.azureSearch.IndexName }}
            - name: AzureSearch__IndexerName
              value: {{ .Values.azureSearch.IndexerName }}
            - name: AzureSearch__DataSourceName
              value: {{ .Values.azureSearch.DataSourceName }}
            - name: AzureSearch__Interval
              value: "{{ .Values.azureSearch.Interval }}"
            - name: AzureAd__ClientId
              value: {{ .Values.clientId }}
            - name: AzureIdentity__ManageIdentityClientId
              value: {{ .Values.azureWorkload.clientId }}
            - name: AxonCoreShared__TenantId
              value: {{ .Values.TenantId }}
            - name: AxonCoreShared__ApiHost
              value: {{ .Values.ApiHost }}
            - name: AxonCoreShared__ClientId
              value: {{ .Values.azureWorkload.clientId }}
            - name: AxonCoreShared__AppScope
              value: {{ .Values.AppScope }}
            - name: AxonCoreShared__GrpcHost
              value: {{ .Values.GrpcHost }}
            - name: AxonCoreShared__AppName
              value: {{ .Values.AppName }}
            - name: cors__origins__0__uri
              value: {{ .Values.corsOriginUrl0 }}
            - name: cors__origins__1__uri
              value: {{ .Values.corsOriginUrl1 }}
            - name: AxonCoreShared__DataProtection__StorageUri
              value: {{ .Values.DataProtectionBlobStorageUri }}        
            - name: AxonCoreShared__DataProtection__KeyVaultKey
              value: {{ .Values.DataProtectionKeyVaultKey }}                 
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
