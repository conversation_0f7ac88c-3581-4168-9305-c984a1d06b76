﻿using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface ICommunicationsRepository : IRepository<Domain.Entities.Communication>
{
    Task<Domain.Entities.Communication> GetItemAsync(int id);

    IQueryable<CommunicationsView> GetCommunicationsView();

    Task<Domain.Entities.Communication> GetOpenCommunicationByIdAsync(int id, CancellationToken cancellationToken);

    Task<Domain.Entities.Communication> GetCompletedCommunicationByIdAsync(int id, CancellationToken cancellationToken);

    Task<Product?[]> GetCommunicationProducts(int id);

    Task<Domain.Entities.Communication> GetCommunicationAsync(int id);
}
