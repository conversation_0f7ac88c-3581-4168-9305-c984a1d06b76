﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class Comment : MultiTenantEntity
{
    public Comment()
    {
    }

    public Comment(int id)
    {
        this.Id = id;
    }

    /// <summary>
    /// Description.
    /// </summary>
    public string? Description { get; set; }

    public string? Question { get; set; }

    public string? Response { get; set; }

    public ICollection<ProductExtension>? ProductExtensions { get; set; }

    public ICollection<CommentProductExtension>? CommentProductExtensions { get; set; }

    public bool IsGeneralGuidance { get; set; }

    public bool IsQuestionIncluded { get; set; }

    public int? ExternalId { get; set; }

    public ICollection<DrugSubstance> DrugSubstances { get; set; } = new HashSet<DrugSubstance>();

    public List<CommentDrugSubstances> CommentDrugSubstances { get; set; } = [];

    /// <summary>
    /// Used to tag(group) comments by GMD number or other company specific data
    /// </summary>
    public ICollection<Tag> Tags { get; set; } = new List<Tag>();

    public List<CommentTags> CommentTags { get; set; } = [];

    /// <summary>
    /// BIRDS links to BI response
    /// </summary>
    public string? BIRDSLinkToBIResponse { get; set; }

    /// <summary>
    /// BIRDS links to BI response
    /// </summary>
    public string? BIRDSLinkToBISAMP { get; set; }

    public Communication Communication { get; set; } = null!;
    public int CommunicationId { get; set; }

    public void SetCommentDrugSubstances(List<DrugSubstance> drugSubstanceEntities)
    {
        CommentDrugSubstances = drugSubstanceEntities.Select(ds => new CommentDrugSubstances { Comment = this, DrugSubstanceId = ds.Id }).ToList();
    }

    public void SetCommentTags(List<Tag> tags)
    {
        CommentTags = tags.Select(tag => new CommentTags { Comment = this, TagId = tag.Id }).ToList();
    }
}
