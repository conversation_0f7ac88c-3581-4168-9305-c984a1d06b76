﻿using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Axon.HAComms.Domain.Entities;

[ExcludeFromCodeCoverage]
public class Audit
{
    public Guid Id { get; set; }

    [MaxLength(50)]
    public string? EventType { get; set; }

    [MaxLength(50)]
    public string? EventOutcome { get; set; }

    [MaxLength(50)]
    public string? EventCategory { get; set; }

    public string? EventDescription { get; set; }

    public Guid? CorrelationId { get; set; }

    [MaxLength(255)]
    public string? UserId { get; set; }

    [MaxLength(200)]
    public string? UserEmail { get; set; }

    [MaxLength(200)]
    public string? IpAddress { get; set; }

    public DateTimeOffset? StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }

    public int? Duration { get; set; }

    public string? Environment { get; set; }

    public string? Target { get; set; }

    public DateTimeOffset? LastUpdatedDate { get; set; }

    public string? JsonData { get; set; }

    [MaxLength(200)]
    public string? Discriminator { get; set; }

    [MaxLength(200)]
    public string? Tenant { get; set; }
}
