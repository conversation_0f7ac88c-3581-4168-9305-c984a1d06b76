using Axon.HAComms.Domain.Entities.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations.Base;

public abstract class BaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : BaseEntity
{
    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        ConfigureBaseProperties(builder);
        ConfigureEntity(builder);
    }

    protected virtual void ConfigureBaseProperties(EntityTypeBuilder<T> builder)
    {
        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }

    protected abstract void ConfigureEntity(EntityTypeBuilder<T> builder);
}
