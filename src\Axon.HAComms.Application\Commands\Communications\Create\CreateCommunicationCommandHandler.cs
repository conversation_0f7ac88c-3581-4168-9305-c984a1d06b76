using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Communications.Create;

internal class CreateCommunicationCommandHandler(
    ICommunicationService communicationService,
    ICommunicationsRepository communicationRepo,
    ICommentsRepository commentsRepo,
    IMapper mapper,
    ILogger<CreateCommunicationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService)
    : IRequestHandler<CreateCommunicationCommandRequest, CreateCommunicationCommandResponse>
{
    public async Task<CreateCommunicationCommandResponse> Handle(CreateCommunicationCommandRequest request, CancellationToken cancellationToken)
    {
        var correlationId = correlationIdProvider.Provide();
        var communication = new Domain.Entities.Communication();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.COMMUNICATION_CREATED, AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_CREATE, communication,
            async () =>
            {
                communication = mapper.Map(request, communication);
                communicationRepo.AddItem(communication);
                await communicationRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Communication {Subject} added successfully.", communication.Subject);
            });

        var comment = new Comment();
        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.COMMENT_CREATED, AuditEventCategory.COMMENTS, AuditEventDescription.COMMENT_CREATE, comment,
            async () =>
            {
                await communicationService.ValidateAndCreateCommentAsync(request.Comment, comment);
                comment.CommunicationId = communication.Id;
                commentsRepo.AddItem(comment);
                await commentsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Comment {Id} added successfully.", comment.Id);
            });

        var response = mapper.Map<CreateCommunicationCommandResponse>(communication);
        return response;
    }
}
