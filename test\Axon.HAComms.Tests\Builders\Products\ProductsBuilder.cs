﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Products
{
    public class ProductsBuilder : IBuilder<Product>
    {
        private int id;
        private string name;
        private bool isActive;
        private ICollection<DrugSubstance>? drugSubstances;
        private ICollection<ProductType>? productTypes;
        private ICollection<ProductExtension>? productExtensions;
        private string createdBy;
        private DateTime createdDate;
        private DateTime lastUpdatedDate;
        private string lastUpdatedBy;

        public ProductsBuilder()
        {
            this.id = Fake.Product.Id;
            this.name = Fake.Product.Name;
            this.isActive = Fake.Product.IsActive;
            this.createdBy = Fake.Product.CreatedBy;
            this.createdDate = DateTime.Now;
            this.lastUpdatedDate = DateTime.Now;
            this.lastUpdatedBy = Fake.Product.LastUpdatedBy;
            
        }
        public Product Build()
        {
            return new(this.id)
            {
                Name = name,
                IsActive = isActive,
                DrugSubstances = drugSubstances!,
                ProductTypes = productTypes!,
                ProductExtensions = productExtensions!,
                CreatedBy = createdBy,
                CreatedDate = createdDate,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy
            };
        }

        public ProductsBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public ProductsBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public ProductsBuilder WithIsActive(bool isActive)
        {
            this.isActive = isActive;
            return this;
        }

        public ProductsBuilder WithDrugSubstances(params DrugSubstance[] items)
        {
            this.drugSubstances = items;
            return this;
        }

        public ProductsBuilder WithProductExtensions(params ProductExtension[] items)
        {
            this.productExtensions = items;
            return this;
        }

        public ProductsBuilder WithProductTypes(params ProductType[] items)
        {
            this.productTypes = items;
            return this;
        }
    }
}
