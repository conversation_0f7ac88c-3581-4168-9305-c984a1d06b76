﻿using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Extensions;
using FluentAssertions;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Comments.Create;

public class CreateCommentCommandValidatorTests
{
    private readonly CreateCommentCommandValidator sut = new();

    [Fact]
    public void Validate_CommunicationIdIsEmpty_ThrowsException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.CommunicationId).WithErrorMessage("'Communication Id' must not be empty.");
    }

    [Fact]
    public void Validate_CommunicationIdIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_DescriptionIsEmpty_ThrowsException_WhenQuestionIncludedIsFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Description).WithErrorMessage("'Description' must not be empty.");
    }

    [Fact]
    public void Validate_DescriptionIsNotEmpty_DoesNotThrowException_WhenQuestionIncludedIsFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_QuestionIsEmpty_ThrowsException_WhenQuestionIncludedIsTrue()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Response = Fake.Comment.Response,
            IsQuestionIncluded = true,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Question).WithErrorMessage("'Question' must not be empty.");
    }

    [Fact]
    public void Validate_ResponseIsEmpty_ThrowsException_WhenQuestionIncludedIsTrue()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Question = Fake.Comment.Question,
            IsQuestionIncluded = true,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Response).WithErrorMessage("'Response' must not be empty.");
    }

    [Fact]
    public void Validate_QuestionAndResponseNotEmpty_DoesNotThrowException_WhenQuestionIncludedIsTrue()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Question = Fake.Comment.Question,
            Response = Fake.Comment.Response,
            IsQuestionIncluded = true,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductExtensionsAreEmpty_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = null,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductExtensions)
            .WithErrorMessage("'Product Extensions' must not be empty.");
    }

    [Fact]
    public void Validate_ProductExtensionIdIsEmpty_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].ProductExtensionId");
        result.Errors.First().ErrorMessage.Should().Contain("'Product Extension Id' must not be empty.");
    }



    [Fact]
    public void Validate_ProductExtensionIdsUniqueValues_DoesNotThrowException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } },
                new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } }
            },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductExtensionIdsDuplicateValues_ThrowsException()
    {
        var productExtensionId = Fake.ProductExtension.Id;
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new ProductExtensionCommentModel() { ProductExtensionId = productExtensionId, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } },
                new ProductExtensionCommentModel() { ProductExtensionId = productExtensionId, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } }
            },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductExtensions).WithErrorMessage("Product Extensions must be unique.");
    }

    [Fact]
    public void Validate_RouteOfAdministrationIdsIsEmpty_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id } },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].RouteOfAdministrationIds")
            .WithErrorMessage("'Route Of Administration Ids' must not be empty.");
    }

    [Fact]
    public void Validate_RouteOfAdministrationIdsEqualZero_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = new List<int> { 0 } } },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].RouteOfAdministrationIds[0]")
            .WithErrorMessage("Route Of Administration Id must not be 0.");
    }

    [Fact]
    public void Validate_DrugSubstanceIdsIsEmpty_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id }, ProductExtensionId = Fake.ProductExtension.Id } },
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.DrugSubstanceIds).WithErrorMessage("'Drug Substance Ids' must not be empty.");
    }

    [Fact]
    public void Validate_ProductFieldsNotEmpty_DoesNotThrowException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id }, ProductExtensionId = Fake.ProductExtension.Id } },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductFieldsEmpty_DoesNotThrowException_WhenIsGeneralGuidance()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_TagIdsEmpty_ThrowsException_WhenIsGeneralGuidance()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.TagIds).WithErrorMessage("'Tag Ids' must not be empty.");
    }

    [Fact]
    public void Validate_TagIdsEmpty_ThrowsException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id }, ProductExtensionId = Fake.ProductExtension.Id } },
            DrugSubstanceIds = [Fake.DrugSubstance.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.TagIds).WithErrorMessage("'Tag Ids' must not be empty.");
    }

    [Fact]
    public void Validate_TagIdsNotEmpty_DoesNotThrowException_WhenIsGeneralGuidance()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_TagIdsNotEmpty_DoesNotThrowException_WhenIsGeneralGuidanceFalse()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id }, ProductExtensionId = Fake.ProductExtension.Id } },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_BIRDSLinkToBIResponseFieldIsValidUrl_DoesNotThrowException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_BIRDSLinkToBIResponseFieldIsNotValidUrl_ThrowsException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            BIRDSLinkToBIResponse = Fake.GetRandomString(50)
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.BIRDSLinkToBIResponse).WithErrorMessage("Must be a valid url.");
    }

    [Fact]
    public void Validate_BIRDSLinkToBISAMPFieldIsValidUrl_DoesNotThrowException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_BIRDSLinkToBISAMPFieldIsNotValidUrl_ThrowsException()
    {
        // Arrange
        var request = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            TagIds = [Fake.Tag.Id],
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            BIRDSLinkToBISAMP = Fake.GetRandomString(50)
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.BIRDSLinkToBISAMP).WithErrorMessage("Must be a valid url.");
    }
}
