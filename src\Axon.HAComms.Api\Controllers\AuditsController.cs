﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Queries.Audit.GetAuditsQuery;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[Authorize]
[HasPermissions(nameof(HacommsPermissions.ViewAudit))]
[Route("{tenant}/v{version:apiVersion}/odata")]
[ApiExplorerSettings(IgnoreApi = true)]
public class AuditsController(IMediator mediator) : ODataControllerBase(mediator)
{
    [HttpGet("Audits")]
    [ProducesResponseType(200, Type = typeof(List<ApplicationAudit>))]
    [ProducesResponseType(400, Type = typeof(ValidationResult))]
    [ProducesResponseType(401, Type = typeof(ErrorResult))]
    [ProducesResponseType(403, Type = typeof(ErrorResult))]
    [ProducesResponseType(500, Type = typeof(ErrorResult))]
    public async Task<IActionResult> GetAudits([FromRoute] string tenant, ODataQueryOptions<ApplicationAudit> odataOptions)
    {
        var result = await Mediator.Send(new GetAuditsQueryRequest(tenant, odataOptions));
        return Ok(result.ApplicationAudits);
    }
}
