﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductExtensionConfiguration : BaseEntityConfiguration<ProductExtension>
{
    protected override void ConfigureEntity(EntityTypeBuilder<ProductExtension> builder)
    {
        builder.ToTable("ProductExtensions");
        builder.HasMany(p => p.RouteOfAdministrations)
            .WithMany(r => r.ProductExtensions)
            .UsingEntity<ProductExtensionRouteOfAdministration>(
                l => l.<PERSON>ne(roa => roa.RouteOfAdministration).WithMany(roa => roa.ProductExtensionRouteOfAdministrations).HasForeignKey(roa => roa.RouteOfAdministrationId),
                r => r.<PERSON>One(p => p.ProductExtension).WithMany(x => x.ProductExtensionRouteOfAdministrations).HasForeign<PERSON>ey(p => p.ProductExtensionId),
                b => b.ToTable("ProductExtensionRouteOfAdministration"));
    }
}
