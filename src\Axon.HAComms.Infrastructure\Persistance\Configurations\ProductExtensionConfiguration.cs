﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductExtensionConfiguration : IEntityTypeConfiguration<ProductExtension>
{
    public void Configure(EntityTypeBuilder<ProductExtension> builder)
    {
        builder.ToTable("ProductExtensions");
        builder.HasMany(p => p.RouteOfAdministrations)
            .WithMany(r => r.ProductExtensions)
            .UsingEntity<ProductExtensionRouteOfAdministration>(
                l => l.<PERSON>ne(roa => roa.RouteOfAdministration).WithMany(roa => roa.ProductExtensionRouteOfAdministrations).HasForeignKey(roa => roa.RouteOfAdministrationId),
                r => r.<PERSON>ne(p => p.ProductExtension).WithMany(x => x.ProductExtensionRouteOfAdministrations).HasForeign<PERSON><PERSON>(p => p.ProductExtensionId),
                b => b.ToTable("ProductExtensionRouteOfAdministration"));

        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
