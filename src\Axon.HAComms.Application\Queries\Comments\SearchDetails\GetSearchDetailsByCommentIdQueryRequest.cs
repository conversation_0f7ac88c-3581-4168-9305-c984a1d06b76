using Axon.HAComms.Application.Models.Search;
using MediatR;

namespace Axon.HAComms.Application.Queries.Comments.SearchDetails
{
    public class GetSearchDetailsByCommentIdQueryRequest : IRequest<SearchDetailsModel>
    {
        public int CommunicationId { get; set; }
        public int CommentId { get; }

        public GetSearchDetailsByCommentIdQueryRequest(int communicationId, int commentId)
        {
            CommunicationId = communicationId;
            CommentId = commentId;
        }
    }
}
