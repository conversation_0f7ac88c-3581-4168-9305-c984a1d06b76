﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]

public class GetCommunicationByIdIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetCommunicationById_WithOneProduct_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createResponse.Should().NotBeNull();
        createResponse.Id.Should().NotBe(0);
        var communicationId = createResponse.Id;

        //Act
        var getCommunicationResponse = await communicationApi.GetCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommunicationResponse.Should().NotBeNull();
        getCommunicationResponse.Id.Should().Be(communicationId);
        getCommunicationResponse.Applications.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Number.Should().Be(application.Number);
        getCommunicationResponse.Applications[0].Submissions.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Submissions[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Submissions[0].Number.Should().Be(submission.Number);
        getCommunicationResponse.Country.Id.Should().Be(country.Id);
        getCommunicationResponse.Country.Name.Should().Be(country.Name);
        getCommunicationResponse.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        getCommunicationResponse.IsCompleted.Should().BeFalse();
        getCommunicationResponse.Subject.Should().Be(request.Subject);
        getCommunicationResponse.SubmissionType.Id.Should().Be(request.SubmissionTypeId);
        getCommunicationResponse.SubmissionType.Name.Should().Be(submissionType.Name);
        getCommunicationResponse.Products.Should().HaveCount(1);
        getCommunicationResponse.Products[0].Should().NotBeNull();
        getCommunicationResponse.Products[0].Id.Should().Be(product1.Id);
        getCommunicationResponse.Products[0].Name.Should().Be(product1.Name);
        getCommunicationResponse.GeneralGuidanceCommentsCount.Should().Be(0);
    }

    [Fact]
    public async Task GetCommunicationById_WithMultipleProducts_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct1 = product1.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createResponse.Should().NotBeNull();
        createResponse.Id.Should().NotBe(0);
        var communicationId = createResponse.Id;

        var createCommentRequest1 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();
        var commentResponse1 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest1);
        commentResponse1.Should().NotBeNull();
        commentResponse1.Id.Should().NotBe(0);

        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct2 = product2.ProductExtensions.First();
        var createCommentRequest2 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();
        var commentResponse2 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest2);
        commentResponse2.Should().NotBeNull();
        commentResponse2.Id.Should().NotBe(0);

        var createCommentRequest3 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();
        var commentResponse3 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest3);
        commentResponse3.Should().NotBeNull();
        commentResponse3.Id.Should().NotBe(0);

        var createCommentRequest4 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions([])
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();
        var commentResponse4 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest4);
        commentResponse4.Should().NotBeNull();
        commentResponse4.Id.Should().NotBe(0);

        //Act
        var getCommunicationResponse = await communicationApi.GetCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommunicationResponse.Should().NotBeNull();
        getCommunicationResponse.Id.Should().Be(communicationId);
        getCommunicationResponse.Applications.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Number.Should().Be(application.Number);
        getCommunicationResponse.Applications[0].Submissions.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Submissions[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Submissions[0].Number.Should().Be(submission.Number);
        getCommunicationResponse.Country.Id.Should().Be(country.Id);
        getCommunicationResponse.Country.Name.Should().Be(country.Name);
        getCommunicationResponse.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        getCommunicationResponse.IsCompleted.Should().BeFalse();
        getCommunicationResponse.Subject.Should().Be(request.Subject);
        getCommunicationResponse.SubmissionType.Id.Should().Be(request.SubmissionTypeId);
        getCommunicationResponse.SubmissionType.Name.Should().Be(submissionType.Name);
        getCommunicationResponse.GeneralGuidanceCommentsCount.Should().Be(1);
        getCommunicationResponse.Products.Should().HaveCount(2);
        getCommunicationResponse.Products[0].Should().NotBeNull();
        getCommunicationResponse.Products[0].Id.Should().Be(product1.Id);
        getCommunicationResponse.Products[0].Name.Should().Be(product1.Name);

        getCommunicationResponse.Products[1].Should().NotBeNull();
        getCommunicationResponse.Products[1].Id.Should().Be(product2.Id);
        getCommunicationResponse.Products[1].Name.Should().Be(product2.Name);
    }

    [Fact]
    public async Task GetCommunicationById_WithGeneralGuidanceCommentsOnly_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsGeneralGuidance()
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createResponse.Should().NotBeNull();
        createResponse.Id.Should().NotBe(0);
        var communicationId = createResponse.Id;

        //Act
        var getCommunicationResponse = await communicationApi.GetCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommunicationResponse.Should().NotBeNull();
        getCommunicationResponse.Id.Should().Be(communicationId);
        getCommunicationResponse.Applications.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Number.Should().Be(application.Number);
        getCommunicationResponse.Applications[0].Submissions.Should().HaveCount(1);
        getCommunicationResponse.Applications[0].Submissions[0].Should().NotBeNull();
        getCommunicationResponse.Applications[0].Submissions[0].Number.Should().Be(submission.Number);
        getCommunicationResponse.Country.Id.Should().Be(country.Id);
        getCommunicationResponse.Country.Name.Should().Be(country.Name);
        getCommunicationResponse.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        getCommunicationResponse.IsCompleted.Should().BeFalse();
        getCommunicationResponse.Subject.Should().Be(request.Subject);
        getCommunicationResponse.SubmissionType.Id.Should().Be(request.SubmissionTypeId);
        getCommunicationResponse.SubmissionType.Name.Should().Be(submissionType.Name);
        getCommunicationResponse.GeneralGuidanceCommentsCount.Should().Be(1);
    }


    [Fact]
    public async Task GetCommunicationById_InvalidCommunicationId_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        createResponse.Should().NotBeNull();
        createResponse.Id.Should().NotBe(0);

        //Act
        var invalidCommunicationId = Fake.Communication.Id;
        var getCommunicationResponse = () => communicationApi.GetCommunicationAsync(invalidCommunicationId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await getCommunicationResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Communication\\\" ({invalidCommunicationId}) was not found.");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.Communications.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();

        await dbContext.SaveChangesAsync();
    }
}
