/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Api.Sdk.Net.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ISubmissionTypesApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>SubmissionTypeModelApiListResult</returns>
        SubmissionTypeModelApiListResult GetSubmissionTypeList(string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of SubmissionTypeModelApiListResult</returns>
        ApiResponse<SubmissionTypeModelApiListResult> GetSubmissionTypeListWithHttpInfo(string tenant);
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ISubmissionTypesApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SubmissionTypeModelApiListResult</returns>
        System.Threading.Tasks.Task<SubmissionTypeModelApiListResult> GetSubmissionTypeListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SubmissionTypeModelApiListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<SubmissionTypeModelApiListResult>> GetSubmissionTypeListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ISubmissionTypesApi : ISubmissionTypesApiSync, ISubmissionTypesApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class SubmissionTypesApi : IDisposable, ISubmissionTypesApi
    {
        private Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public SubmissionTypesApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public SubmissionTypesApi(string basePath)
        {
            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public SubmissionTypesApi(Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public SubmissionTypesApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public SubmissionTypesApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public SubmissionTypesApi(HttpClient client, Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionTypesApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public SubmissionTypesApi(Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient client, Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient asyncClient, Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>SubmissionTypeModelApiListResult</returns>
        public SubmissionTypeModelApiListResult GetSubmissionTypeList(string tenant)
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<SubmissionTypeModelApiListResult> localVarResponse = GetSubmissionTypeListWithHttpInfo(tenant);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of SubmissionTypeModelApiListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<SubmissionTypeModelApiListResult> GetSubmissionTypeListWithHttpInfo(string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling SubmissionTypesApi->GetSubmissionTypeList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<SubmissionTypeModelApiListResult>("/{tenant}/v1/SubmissionTypes", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetSubmissionTypeList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SubmissionTypeModelApiListResult</returns>
        public async System.Threading.Tasks.Task<SubmissionTypeModelApiListResult> GetSubmissionTypeListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<SubmissionTypeModelApiListResult> localVarResponse = await GetSubmissionTypeListWithHttpInfoAsync(tenant, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SubmissionTypeModelApiListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<SubmissionTypeModelApiListResult>> GetSubmissionTypeListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling SubmissionTypesApi->GetSubmissionTypeList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<SubmissionTypeModelApiListResult>("/{tenant}/v1/SubmissionTypes", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetSubmissionTypeList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
