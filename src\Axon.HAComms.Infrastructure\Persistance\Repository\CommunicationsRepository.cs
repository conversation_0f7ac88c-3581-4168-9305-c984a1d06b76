﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class CommunicationsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<CommunicationsRepository> logger)
    : SqlServerRepository<Communication>(context, tenant, logger), ICommunicationsRepository
{
    public async Task<Communication> GetItemAsync(int id)
    {
        var entity = await context.Set<Communication>()
            .Include(x => x.Country)
            .Include(x => x.SubmissionType)
            .Include(x => x.Applications)
            .ThenInclude(x => x.Submissions)
            .AsSplitQuery()
            .SingleOrDefaultAsync(c => c.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(Communication), id);
    }

    public async Task<Product?[]> GetCommunicationProducts(int id)
    {
        var entities = (await context.Set<Communication>()
                                .Include(c => c.Comments)
                                    .ThenInclude(com => com.ProductExtensions!)
                                    .ThenInclude(pe => pe.Product)
                               .Where(c => c.Id == id)
                               .ToArrayAsync())
            .SelectMany(c => c.Comments.SelectMany(comment => comment.ProductExtensions != null ? comment.ProductExtensions.GroupBy(pe => pe.ProductId).SelectMany(group => group.Select(p => p.Product)) : null!));

        return entities.ToArray();
    }

    public async Task<Communication> GetOpenCommunicationByIdAsync(int id, CancellationToken cancellationToken)
    {
        var entity = await context.Set<Communication>().SingleOrDefaultAsync(c => !c.IsCompleted && c.Id == id, cancellationToken: cancellationToken);

        return entity ?? throw new EntityNotFoundException(nameof(Communication), id);
    }

    public async Task<Communication> GetCompletedCommunicationByIdAsync(int id, CancellationToken cancellationToken)
    {
        var entity = await context.Set<Communication>().SingleOrDefaultAsync(c => c.IsCompleted && c.Id == id, cancellationToken: cancellationToken);

        return entity ?? throw new EntityNotFoundException(nameof(Communication), id);
    }

    public IQueryable<CommunicationsView> GetCommunicationsView()
    {
        return context.Set<CommunicationsView>().AsQueryable();
    }

    public async Task<Communication> GetCommunicationAsync(int id)
    {
        var entity = await context.Set<Communication>().SingleOrDefaultAsync(c => c.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(Communication), id);
    }
}
