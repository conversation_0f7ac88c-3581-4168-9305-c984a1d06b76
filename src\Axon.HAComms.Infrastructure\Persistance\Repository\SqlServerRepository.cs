﻿using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class SqlServerRepository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly MultitenantHacommsDbContext context;
    protected readonly ITenant tenant;
    private readonly ILogger<SqlServerRepository<T>> logger;

    protected SqlServerRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<SqlServerRepository<T>> logger)
    {
        this.context = context;
        this.tenant = tenant;
        this.logger = logger;
    }

    public virtual void AddItem(T item)
        => context.Set<T>().Add(item);

    public virtual void DeleteItem(T item)
    {
        logger.LogInformation("Deleting {Type}, ", typeof(T));
        context.Set<T>().Remove(item);
    }

    public virtual void UpdateItem(T item)
        => context.Entry(item).State = EntityState.Modified;

    public async Task<int> SaveChangesAsync(IUserProvider userProvider)
    {
        SetBaseFields(userProvider);
        logger.LogInformation("Saving changes to the database.");

        var result = await context.SaveChangesAsync();
        logger.LogInformation("{EntitiesAffected} entities were affected by the save operation.", result);

        return result;
    }

    private void SetBaseFields(IUserProvider userProvider)
    {
        var userId = userProvider.ProvideEmail();
        logger.LogInformation("Setting base fields for entities modified by user {UserId}.", userId);

        var entityEntries = context.ChangeTracker.Entries()
            .Where(entityEntry => entityEntry.State is EntityState.Added or EntityState.Modified && entityEntry.Entity is T)
            .ToList();

        foreach (var entityEntry in entityEntries)
        {
            var entity = (T)entityEntry.Entity;

            entity.LastUpdatedDate = DateTime.UtcNow;
            entity.LastUpdatedBy = userId;

            if (entityEntry.State != EntityState.Added)
                continue;
            
            entity.CreatedDate = DateTime.UtcNow;
            entity.CreatedBy = userId;

            var entityState = entityEntry.State;
            var entityProperties = entityEntry.CurrentValues.Properties
                .ToDictionary(p => p.Name, p => entityEntry.CurrentValues[p.Name]);

            logger.LogInformation("Entity {EntityType} with state {EntityState}: {EntityProperties}.",
                entity.GetType().Name, entityState, entityProperties);
        }

        logger.LogInformation("Base fields set for {EntityCount} entities modified by user {UserId}.", entityEntries.Count, userId);
    }
}
