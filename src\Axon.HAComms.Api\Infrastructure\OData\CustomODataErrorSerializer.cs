﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Extensions;
using Microsoft.AspNetCore.OData.Formatter.Serialization;
using Microsoft.OData;

namespace Axon.HAComms.Api.Infrastructure.OData;

public class CustomODataErrorSerializer : ODataErrorSerializer
{
    public override Task WriteObjectAsync(object graph, Type type, ODataMessageWriter messageWriter, ODataSerializerContext writeContext)
    {
        if (graph is SerializableError error)
        {
            var oDataError = error.CreateODataError();
            oDataError.InnerError = null;

            return base.WriteObjectAsync(oDataError, typeof(ODataError), messageWriter, writeContext);
        }
        else if (graph is ODataError oDataError)
        {
            oDataError.InnerError = null;
            return base.WriteObjectAsync(oDataError, typeof(ODataError), messageWriter, writeContext);
        }

        return base.WriteObjectAsync(graph, type, messageWriter, writeContext);
    }
}
