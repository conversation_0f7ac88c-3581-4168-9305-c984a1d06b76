﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class ProductExtensionsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<ProductExtensionsRepository> logger)
    : SqlServerRepository<ProductExtension>(context, tenant, logger), IProductExtensionsRepository
{
    public void DeleteItems(params ProductExtension[] productExtensions)
    {
        context.Set<ProductExtension>().RemoveRange(productExtensions);
    }

    public IQueryable<ProductExtension> GetQueryableItems()
    {
        return context.Set<ProductExtension>();
    }

    public IQueryable<ProductExtension> GetQueryableItemsWithIncludes()
    {
        return context.Set<ProductExtension>()
            .Include(x => x.Product)
                .ThenInclude(s => s.DrugSubstances)
            .Include(x => x.RouteOfAdministrations)
            .Include(x => x.DosageForm);
    }

    public async Task<List<string>> GetFilteredListAsync(
        Expression<Func<ProductExtension, bool>> filter,
        Expression<Func<ProductExtension, string>> selector)
    {
        return await context.Set<ProductExtension>()
            .Where(filter)
            .Select(selector)
            .Distinct()
            .ToListAsync();
    }

    public async Task<bool> ExistsAsync(Expression<Func<ProductExtension, bool>> filter)
    {
        return await context.Set<ProductExtension>().Include(x => x.DosageForm).AsNoTracking().AnyAsync(filter);
    }
}
