﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Comments.CommentsPagedList;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Comments;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore.Query;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Comments.ProductCommentsPagedList;

public class GetCommentsPagedListQueryHandlerTests
{
    private readonly GetCommentsPagedListQueryHandler handler;
    private readonly ICommentsRepository commentsRepo;
    public GetCommentsPagedListQueryHandlerTests()
    {
        commentsRepo = Substitute.For<ICommentsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommentsMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetCommentsPagedListQueryHandler(commentsRepo, mapper);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var comments = TestEntitiesGenerator<Comment, CommentsBuilder>.Generate(54);

        var mock = comments.BuildMock();
        commentsRepo.GetFilteredComments(include: Arg.Any<Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>>(),filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(mock);

        var request = new GetCommentsPagedListQueryRequest(Fake.Communication.Id, Fake.Product.Id, null, 50, 10);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(4);
    }

    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var comments = TestEntitiesGenerator<Comment, CommentsBuilder>.Generate(54);

        var mock = comments.BuildMock();
        commentsRepo.GetFilteredComments(include: Arg.Any<Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>>(), filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(mock);

        var request = new GetCommentsPagedListQueryRequest(Fake.Communication.Id, Fake.Product.Id, null, -50, 10);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var comments = TestEntitiesGenerator<Comment, CommentsBuilder>.Generate(54);

        var mock = comments.BuildMock();
        commentsRepo.GetFilteredComments(include: Arg.Any<Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>>(), filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(mock);

        var request = new GetCommentsPagedListQueryRequest(Fake.Communication.Id, Fake.Product.Id, null,10, -20);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(44);
    }
}
