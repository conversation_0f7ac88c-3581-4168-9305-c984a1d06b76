﻿DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

--UNCOMMENT NEXT LINE AFTER PROVIDING A VALID TENANT
--DECLARE @default_tenant varchar(20) = '';

INSERT INTO [dbo].[Tags] (Name, Description, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant)
VALUES 
(N'A201',N'Adventitious Agents Safety Evaluation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S110',N'Nomenclature for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S120',N'Structure of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S130',N'General Properties', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S210',N'Drug Substance Manufacturer', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S220',N'Manufacturing Procedure of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S231',N'Justification for Starting Material Designation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S232',N'Specifications for Starting Material', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S233',N'Analytical Procedure for Starting Material', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S234',N'Specifications for Reagents, Solvents and Auxiliary Materials', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S241',N'Justification of Controls at Critical Steps', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S242',N'Specifications for Intermediate', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S243',N'Analytical Procedures for Intermediate', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S250',N'Process Evaluation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S251',N'Sterilization Process Validation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S260',N'Manufacturing Process Development', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S311',N'Elucidation of Structure', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S312',N'Physicochemical Characteristics of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S313',N'Solid-State Forms of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S320',N'Impurities in the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S400',N'Control Strategy Summary – Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S410',N'Specifications for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S420',N'Analytical Procedure for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S430',N'Validation of Analytical Procedure for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S440',N'Batch Analyses for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S460',N'Certificate of Analysis for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S450',N'Justification of the Specifications for the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S500',N'Reference Standard(s) for Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S610',N'Description of the Container/Closure System', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S620',N'Container/Closure Suitability Information', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S630',N'Specifications for Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S640',N'Analytical Procedure for Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S650',N'Validation of Analytical Procedure for the Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S710',N'Stability Summary and Conclusions', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S720',N'Post-Approval Stability Protocol and Stability Commitment', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S731',N'Stability Data - Stress Stability Studies of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'S732',N'Stability Data - Accelerated and Long-Term Stability Studies of the Drug Substance', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P100',N'Description and Composition of the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P200',N'Pharmaceutical Development', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P210',N'Clinical Trial Formulations and Batches', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P310',N'Drug Product Manufacturer', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P320',N'Batch Formula', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P330',N'Description of Manufacturing Process and Process Controls', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P331',N'Packaging Procedure', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P332',N'Master Batch Record', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P340',N'Justification of Controls at Critical Steps', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P341',N'Specifications for Intermediate Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P342',N'Analytical Procedure for Intermediate Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P343',N'Validation of Analytical Procedure for Intermediate Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P344',N'Justification of Specifications for Intermediate Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P350',N'Process Evaluation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P351',N'Process Validation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P352',N'Sterilization Process Validation', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P400',N'Reference to Compendia', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P410',N'Specifications for Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P420',N'Analytical Procedure for Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P430',N'Validation of Analytical Procedure for Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P440',N'Justification of the Specifications for Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P470',N'Certificate of Analysis for Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P450',N'Excipients of Human or Animal Origin', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P460',N'Novel Excipient', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P500',N'Control Strategy Summary – Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P510',N'Specifications for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P520',N'Analytical Procedure for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P530',N'Validation of Analytical Procedure for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P540',N'Batch Analyses for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P541',N'Certificate of Analysis for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P550',N'Impurities in the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P560',N'Justification of the Specifications for the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P561',N'Justification of the Dissolution Specification', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P600',N'Reference Standard(s) for Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P710',N'Description of the Container/Closure System', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P720',N'Specifications for the Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P730',N'Analytical Procedure for the Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P740',N'Validation of Analytical Procedure for the Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P750',N'Justification of the Specifications for Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P760',N'Certificate of Analysis for the Container/Closure Component', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P810',N'Stability Summary and Conclusions', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P820',N'Post-Approval Stability Protocol and Stability Commitment', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P831',N'Stability Data - Stress Stability Studies of the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P832',N'Stability Data - Accelerated and Long-Term Stability Studies of the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P833',N'Stability Data - In-Use Stability Studies of the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'P834',N'Stability Data - Bulk Storage Stability Studies of the Drug Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'ATMP',N'Advanced Therapy Medicinal Product', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Biosimilar',N'Highly similar biological product to an approved reference product, with no meaningful differences in safety, purity, and potency.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Biotech',N'Use of biological processes, organisms, or systems in medicine and agriculture.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Biowaiver',N'Exemption from in vivo bioequivalence studies for certain generic drugs, based on in vitro similarity.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Cgmp',N'Current Good Manufacturing Practice', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Comparator',N'Reference product in clinical trials for comparing safety and effectiveness.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Container label',N'Label on packaging or container providing drug information and warnings.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Cross-reference to other applications',N'Referring to related applications or submissions for the same or similar products.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'CTD 2.3',N'Common Technical Document Version 2.3', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Device',N'Non-drug medical device for diagnosis, prevention, or treatment.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'DMF change index',N'Document informing regulatory authorities about changes to confidential Drug Master File.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'E100',N'Colorant (curcumin) used in food, pharmaceuticals, and cosmetics.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Establishment information',N'Info about manufacturing facilities, locations, and compliance.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Field copy certification',N'Certification of accuracy and completeness of official document copies.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Form',N'Physical presentation of a pharmaceutical product (tablets, capsules, injections, creams).', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Format',N'Structure and layout of a document or file.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'GMP certificate',N'Certificate for compliance with Good Manufacturing Practice regulations.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Oligonucleotide',N'Short DNA/RNA sequences used in research, diagnostics, and therapeutics.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Package insert',N'Printed information leaflet/brochure in drug packaging.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Patent',N'Exclusive legal right granted to an inventor for limited period.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Peptide',N'Short chain of amino acids used in drug development and research.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'QP statement',N'Declaration by Qualified Person confirming batch compliance with standards.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Samples',N'Product samples for testing, evaluation, or promotion.', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant),
(N'Secondary packaging',N'Outer packaging for multiple product units (boxes, cartons).', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted, @default_tenant)


