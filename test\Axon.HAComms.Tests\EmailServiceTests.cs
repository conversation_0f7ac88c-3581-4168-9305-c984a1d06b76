using Axon.HAComms.Application.Services;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net;
using Xunit;


namespace Axon.HAComms.Tests;
public class EmailServiceTests
{
    private readonly IConfiguration mockConfiguration;
    private readonly ISendGridClient mockSendGridClient;
    private readonly EmailService emailService;

    public EmailServiceTests()
    {
        mockConfiguration = Substitute.For<IConfiguration>();
        mockConfiguration.GetConnectionString("SendGrid").Returns("dummyApiKey");
        mockConfiguration.GetValue<string>("senderEmail").Returns("<EMAIL>");
        mockSendGridClient = Substitute.For<ISendGridClient>();
        emailService = new EmailService(mockConfiguration, mockSendGridClient);
    }

    [Fact]
    public async Task Send_EmailSuccessfullySent_ReturnsTrueAndStatusCode()
    {
        // Arrange
        string[] receivers = { "<EMAIL>", "<EMAIL>" };
        string subject = Fake.GetRandomString(15);
        string messageContent = Fake.GetRandomString(30);

        var response = new Response(HttpStatusCode.OK, null, null);

        mockSendGridClient
            .SendEmailAsync(Arg.Any<SendGridMessage>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await emailService.Send(receivers, subject, messageContent);

        // Assert
        result.Item1.Should().BeTrue();
        result.Item2.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task Send_EmailFailed_ReturnsFalseAndStatusCode()
    {
        // Arrange
        string[] receivers = { "<EMAIL>", "<EMAIL>" };
        string subject = Fake.GetRandomString(15);
        string messageContent = Fake.GetRandomString(30);

        var response = new Response(HttpStatusCode.BadRequest, null, null);

        mockSendGridClient
            .SendEmailAsync(Arg.Any<SendGridMessage>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await emailService.Send(receivers, subject, messageContent);

        // Assert
        result.Item1.Should().BeFalse();
        result.Item2.Should().Be(HttpStatusCode.BadRequest);
    }
}
