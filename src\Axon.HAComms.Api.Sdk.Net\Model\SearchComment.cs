/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// SearchComment
    /// </summary>
    [DataContract(Name = "SearchComment")]
    public partial class SearchComment : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchComment" /> class.
        /// </summary>
        /// <param name="commentId">commentId.</param>
        /// <param name="communicationId">communicationId.</param>
        /// <param name="description">description.</param>
        /// <param name="dateOfCommunication">dateOfCommunication.</param>
        /// <param name="productCodes">productCodes.</param>
        /// <param name="productName">productName.</param>
        /// <param name="countryName">countryName.</param>
        /// <param name="drugSubstanceNames">drugSubstanceNames.</param>
        /// <param name="dosageFormNames">dosageFormNames.</param>
        /// <param name="routeOfAdministrationNames">routeOfAdministrationNames.</param>
        /// <param name="submissionNumberNames">submissionNumberNames.</param>
        /// <param name="applicationNumberNames">applicationNumberNames.</param>
        /// <param name="submissionTypeName">submissionTypeName.</param>
        /// <param name="tagNames">tagNames.</param>
        /// <param name="productTypeNames">productTypeNames.</param>
        /// <param name="isQuestionIncluded">isQuestionIncluded.</param>
        /// <param name="isGeneralGuidance">isGeneralGuidance.</param>
        /// <param name="birdsLinkToBIResponse">birdsLinkToBIResponse.</param>
        /// <param name="birdsLinkToBISAMP">birdsLinkToBISAMP.</param>
        /// <param name="question">question.</param>
        /// <param name="response">response.</param>
        public SearchComment(int commentId = default(int), int communicationId = default(int), string description = default(string), DateTime? dateOfCommunication = default(DateTime?), string productCodes = default(string), string productName = default(string), string countryName = default(string), string drugSubstanceNames = default(string), string dosageFormNames = default(string), string routeOfAdministrationNames = default(string), string submissionNumberNames = default(string), string applicationNumberNames = default(string), string submissionTypeName = default(string), string tagNames = default(string), string productTypeNames = default(string), bool? isQuestionIncluded = default(bool?), bool? isGeneralGuidance = default(bool?), string birdsLinkToBIResponse = default(string), string birdsLinkToBISAMP = default(string), string question = default(string), string response = default(string))
        {
            this.CommentId = commentId;
            this.CommunicationId = communicationId;
            this.Description = description;
            this.DateOfCommunication = dateOfCommunication;
            this.ProductCodes = productCodes;
            this.ProductName = productName;
            this.CountryName = countryName;
            this.DrugSubstanceNames = drugSubstanceNames;
            this.DosageFormNames = dosageFormNames;
            this.RouteOfAdministrationNames = routeOfAdministrationNames;
            this.SubmissionNumberNames = submissionNumberNames;
            this.ApplicationNumberNames = applicationNumberNames;
            this.SubmissionTypeName = submissionTypeName;
            this.TagNames = tagNames;
            this.ProductTypeNames = productTypeNames;
            this.IsQuestionIncluded = isQuestionIncluded;
            this.IsGeneralGuidance = isGeneralGuidance;
            this.BirdsLinkToBIResponse = birdsLinkToBIResponse;
            this.BirdsLinkToBISAMP = birdsLinkToBISAMP;
            this.Question = question;
            this.Response = response;
        }

        /// <summary>
        /// Gets or Sets CommentId
        /// </summary>
        [DataMember(Name = "commentId", EmitDefaultValue = false)]
        public int CommentId { get; set; }

        /// <summary>
        /// Gets or Sets CommunicationId
        /// </summary>
        [DataMember(Name = "communicationId", EmitDefaultValue = false)]
        public int CommunicationId { get; set; }

        /// <summary>
        /// Gets or Sets Description
        /// </summary>
        [DataMember(Name = "description", EmitDefaultValue = true)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets DateOfCommunication
        /// </summary>
        [DataMember(Name = "dateOfCommunication", EmitDefaultValue = true)]
        public DateTime? DateOfCommunication { get; set; }

        /// <summary>
        /// Gets or Sets ProductCodes
        /// </summary>
        [DataMember(Name = "productCodes", EmitDefaultValue = true)]
        public string ProductCodes { get; set; }

        /// <summary>
        /// Gets or Sets ProductName
        /// </summary>
        [DataMember(Name = "productName", EmitDefaultValue = true)]
        public string ProductName { get; set; }

        /// <summary>
        /// Gets or Sets CountryName
        /// </summary>
        [DataMember(Name = "countryName", EmitDefaultValue = true)]
        public string CountryName { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstanceNames
        /// </summary>
        [DataMember(Name = "drugSubstanceNames", EmitDefaultValue = true)]
        public string DrugSubstanceNames { get; set; }

        /// <summary>
        /// Gets or Sets DosageFormNames
        /// </summary>
        [DataMember(Name = "dosageFormNames", EmitDefaultValue = true)]
        public string DosageFormNames { get; set; }

        /// <summary>
        /// Gets or Sets RouteOfAdministrationNames
        /// </summary>
        [DataMember(Name = "routeOfAdministrationNames", EmitDefaultValue = true)]
        public string RouteOfAdministrationNames { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionNumberNames
        /// </summary>
        [DataMember(Name = "submissionNumberNames", EmitDefaultValue = true)]
        public string SubmissionNumberNames { get; set; }

        /// <summary>
        /// Gets or Sets ApplicationNumberNames
        /// </summary>
        [DataMember(Name = "applicationNumberNames", EmitDefaultValue = true)]
        public string ApplicationNumberNames { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionTypeName
        /// </summary>
        [DataMember(Name = "submissionTypeName", EmitDefaultValue = true)]
        public string SubmissionTypeName { get; set; }

        /// <summary>
        /// Gets or Sets TagNames
        /// </summary>
        [DataMember(Name = "tagNames", EmitDefaultValue = true)]
        public string TagNames { get; set; }

        /// <summary>
        /// Gets or Sets ProductTypeNames
        /// </summary>
        [DataMember(Name = "productTypeNames", EmitDefaultValue = true)]
        public string ProductTypeNames { get; set; }

        /// <summary>
        /// Gets or Sets IsQuestionIncluded
        /// </summary>
        [DataMember(Name = "isQuestionIncluded", EmitDefaultValue = true)]
        public bool? IsQuestionIncluded { get; set; }

        /// <summary>
        /// Gets or Sets IsGeneralGuidance
        /// </summary>
        [DataMember(Name = "isGeneralGuidance", EmitDefaultValue = true)]
        public bool? IsGeneralGuidance { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBIResponse
        /// </summary>
        [DataMember(Name = "birdsLinkToBIResponse", EmitDefaultValue = true)]
        public string BirdsLinkToBIResponse { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBISAMP
        /// </summary>
        [DataMember(Name = "birdsLinkToBISAMP", EmitDefaultValue = true)]
        public string BirdsLinkToBISAMP { get; set; }

        /// <summary>
        /// Gets or Sets Question
        /// </summary>
        [DataMember(Name = "question", EmitDefaultValue = true)]
        public string Question { get; set; }

        /// <summary>
        /// Gets or Sets Response
        /// </summary>
        [DataMember(Name = "response", EmitDefaultValue = true)]
        public string Response { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SearchComment {\n");
            sb.Append("  CommentId: ").Append(CommentId).Append("\n");
            sb.Append("  CommunicationId: ").Append(CommunicationId).Append("\n");
            sb.Append("  Description: ").Append(Description).Append("\n");
            sb.Append("  DateOfCommunication: ").Append(DateOfCommunication).Append("\n");
            sb.Append("  ProductCodes: ").Append(ProductCodes).Append("\n");
            sb.Append("  ProductName: ").Append(ProductName).Append("\n");
            sb.Append("  CountryName: ").Append(CountryName).Append("\n");
            sb.Append("  DrugSubstanceNames: ").Append(DrugSubstanceNames).Append("\n");
            sb.Append("  DosageFormNames: ").Append(DosageFormNames).Append("\n");
            sb.Append("  RouteOfAdministrationNames: ").Append(RouteOfAdministrationNames).Append("\n");
            sb.Append("  SubmissionNumberNames: ").Append(SubmissionNumberNames).Append("\n");
            sb.Append("  ApplicationNumberNames: ").Append(ApplicationNumberNames).Append("\n");
            sb.Append("  SubmissionTypeName: ").Append(SubmissionTypeName).Append("\n");
            sb.Append("  TagNames: ").Append(TagNames).Append("\n");
            sb.Append("  ProductTypeNames: ").Append(ProductTypeNames).Append("\n");
            sb.Append("  IsQuestionIncluded: ").Append(IsQuestionIncluded).Append("\n");
            sb.Append("  IsGeneralGuidance: ").Append(IsGeneralGuidance).Append("\n");
            sb.Append("  BirdsLinkToBIResponse: ").Append(BirdsLinkToBIResponse).Append("\n");
            sb.Append("  BirdsLinkToBISAMP: ").Append(BirdsLinkToBISAMP).Append("\n");
            sb.Append("  Question: ").Append(Question).Append("\n");
            sb.Append("  Response: ").Append(Response).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
