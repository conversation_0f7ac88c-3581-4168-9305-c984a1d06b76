﻿using Axon.HAComms.Application.Models.Comments;
using FluentValidation;

namespace Axon.HAComms.Application.Common;

public class CommentValidator : AbstractValidator<CommentRequestModel>
{
    public CommentValidator()
    {
        RuleFor(c => c.Description).NotEmpty().When(c => !c.IsQuestionIncluded);
        RuleFor(c => c.Question).NotEmpty().When(c => c.IsQuestionIncluded);
        RuleFor(c => c.Response).NotEmpty().When(c => c.IsQuestionIncluded);
        RuleFor(c => c.ProductExtensions).NotEmpty().When(c => !c.IsGeneralGuidance);
        RuleFor(c => c.ProductExtensions).Must(c => c?.Select(pe => pe.ProductExtensionId).Distinct().Count() == c?.Select(pe => pe.ProductExtensionId).Count())
                                         .When(c => !c.IsGeneralGuidance)
                                         .WithMessage("Product Extensions must be unique.");
        RuleForEach(c => c.ProductExtensions).ChildRules(pe =>
        {
            pe.RuleFor(a => a.RouteOfAdministrationIds).NotEmpty();
            pe.RuleForEach(a => a.RouteOfAdministrationIds).NotEqual(0).WithMessage(_ => "Route Of Administration Id must not be 0.");
            pe.RuleFor(a => a.ProductExtensionId).NotEmpty();

        }).NotEmpty().When(c => !c.IsGeneralGuidance);

        RuleFor(c => c.DrugSubstanceIds).NotEmpty().When(c => !c.IsGeneralGuidance);
        RuleFor(x => x.BIRDSLinkToBIResponse).Must(BeValidUrl).WithMessage("Must be a valid url.").When(x => !string.IsNullOrEmpty(x.BIRDSLinkToBIResponse));
        RuleFor(x => x.BIRDSLinkToBISAMP).Must(BeValidUrl).WithMessage("Must be a valid url.").When(x => !string.IsNullOrEmpty(x.BIRDSLinkToBISAMP));
        RuleFor(c => c.TagIds).NotEmpty();
    }

    private static bool BeValidUrl(string? url)
    {
        if (string.IsNullOrEmpty(url))
        {
            return false;
        }
        if (!Uri.TryCreate(url, UriKind.Absolute, out Uri? uriResult))
        {
            return false;
        }

        if ((uriResult.Scheme != Uri.UriSchemeHttp && uriResult.Scheme != Uri.UriSchemeHttps) || string.IsNullOrEmpty(uriResult.Host))
        {
            return false;
        }

        return true;
    }
}
