﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Commands.Comments.Delete;
using Axon.HAComms.Application.Commands.Comments.DeleteByProduct;
using Axon.HAComms.Application.Commands.Comments.Search;
using Axon.HAComms.Application.Commands.Comments.Update;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Application.Models.Search;
using Axon.HAComms.Application.Queries.Comments.CommentsPagedList;
using Axon.HAComms.Application.Queries.Comments.SearchDetails;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Comments")]
public class CommentsController(IMediator mediator) : ApiController<PERSON>ase(mediator)
{
    /// <summary>
    /// Search comments
    /// </summary>
    /// <response code="200">Comments</response>
    /// <response code="401">The user not authenticated.</response>
    /// <response code="403">The user not authorised.</response>
    /// <response code="500">System error.</response>
    [HttpPost("search", Name = "SearchComments")]
    //[HasPermissions(nameof(HacommsPermissions.CanSearchComments))]
    [ProducesResponseType(200, Type = typeof(SearchCommentCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    public async Task<IActionResult> SearchAsync([FromBody] SearchCommentCommandRequest command) =>
        await Send(command);

    /// <summary>
    /// Get search details for comment by id
    /// </summary>
    /// <response code="200">Single comment returned.</response>
    [HttpGet("search-details/{communicationId}/{commentId}", Name = "SearchDetails")]
    //[HasPermissions(nameof(HacommsPermissions.CanSearchComments))]
    [ProducesResponseType(200, Type = typeof(SearchDetailsModel))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetSearchDetailsByCommunicationIdAndCommentIdAsync(int communicationId, int commentId) =>
        await Send(new GetSearchDetailsByCommentIdQueryRequest(communicationId, commentId));

    /// <summary>
    /// Get comments by product in communication or general guidance comments
    /// If productId is null - general comments for the specified communicationId will be returned
    /// If productId is not null - all comments for the specified communicationId and productId will be returned
    /// If excludedCommentId is null - all comments for the specified communicationId and productId will be returned
    /// If excludedCommentId is not null - the comment with excludedCommentId will not be returned
    /// </summary>
    /// <response code="200">A collection of comments returned.</response>
    [HttpGet("comments/{communicationId}", Name = "GetCommentsByCommunicationId")]
    //[HasPermissions(nameof(HacommsPermissions.CanSearchComments))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<CommentDtoModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetCommentsByCommunicationIdAsync(int communicationId, [FromQuery] int? productId, [FromQuery] int? excludedCommentId, [FromQuery] int skip, [FromQuery] int take) =>
        await Send(new GetCommentsPagedListQueryRequest(communicationId, productId, excludedCommentId, skip, take));

    /// <summary>
    /// Create comment
    /// </summary>
    /// <returns></returns>
    [HttpPost(Name = "CreateComment")]
    //[HasPermissions(nameof(HacommsPermissions.CreateComment))]
    [ProducesResponseType(200, Type = typeof(CreateCommentCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateCommentAsync([FromBody] CreateCommentCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update comment
    /// </summary>
    [HttpPut(Name = "UpdateComment")]
    [HasPermissions(nameof(HacommsPermissions.EditComment))]
    [ProducesResponseType(200, Type = typeof(UpdateCommentCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateCommentAsync([FromBody] UpdateCommentCommandRequest command) =>
        await Send(command);

    /// <summary>
    /// Delete comment 
    /// </summary>
    [HttpDelete("{id}", Name = "DeleteComment")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteComment))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteCommentAsync(int id) =>
        await Send(new DeleteCommentCommandRequest { Id = id});

    /// <summary>
    /// Delete comments by communication and product
    /// </summary>
    [HttpDelete(Name = "DeleteCommentsByCommunicationAndProduct")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteComment))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteCommentsByCommunicationAndProductAsync([FromBody] DeleteCommentsByProductCommandRequest command) =>
        await Send(command);
}
