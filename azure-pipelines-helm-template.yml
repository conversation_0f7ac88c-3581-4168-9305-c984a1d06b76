parameters:
  - name: environments
    type: object
    default:
      - name: axon_hac_dev_helm
        environment: axon_hacomms_dev
        cluster: axn-nonprod-aks-eun
        rg: rg-axn-nonprod-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-dev
        valuesOverrideFiles: ["./deploy/helm/api-chart/environments/development.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: axn-dev-kv-eun
           secretsFilter: 'newreliclicensekey'
        dependsOn: []

      - name: axon_hac_int_helm
        environment: axon_hacomms_int
        cluster: axn-nonprod-aks-eun
        rg: rg-axn-nonprod-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-int
        valuesOverrideFiles: ["./deploy/helm/api-chart/environments/integration.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: axn-int-kv-eun
           secretsFilter: 'newreliclicensekey'
        dependsOn: ["axon_hac_dev_helm"]

      - name: axon_hac_stg_helm
        environment: axon_hacomms_stg
        cluster: axn-stg-aks-eun
        rg: rg-axn-stg-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-stg
        valuesOverrideFiles: ["./deploy/helm/api-chart/environments/staging.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: axn-stg-kv-eun
           secretsFilter: 'newreliclicensekey'
        dependsOn: ["axon_hac_int_helm"]

      - name: axon_hac_prodeu_helm
        environment: axon_hacomms_prodeu
        cluster: axn-prod-aks-eun
        rg: rg-axn-prod-eun
        subscription: "Axon Production"
        namespace: axon-hacomms-prod
        valuesOverrideFiles: ["./deploy/helm/api-chart/environments/production-eu.values.yaml"]
        keyVaults:
         - subscription: "Axon_Production"
           name: axn-prod-kv-eun
           secretsFilter: 'newreliclicensekey'
        dependsOn: ["axon_hac_stg_helm"]

      - name: axon_hac_uat_helm
        environment: axon_hacomms_uat
        cluster: axn-prod-aks-eun
        rg: rg-axn-prod-eun
        subscription: "Axon Production"
        namespace: axon-hacomms-uat
        valuesOverrideFiles: ["./deploy/helm/api-chart/environments/uat.values.yaml"]
        keyVaults:
         - subscription: "Axon_Production"
           name: axn-prod-kv-eun
           secretsFilter: 'newreliclicensekey'
        dependsOn: ["axon_hac_stg_helm"]

stages:
  - stage: Deploy    
    jobs:
    - job: Initialize
      steps:
      - bash: |          
            echo "Updating build number with pipeline run name"
            echo "##vso[build.updatebuildnumber]$(Build.BuildNumber) Build #$(resources.pipeline.build-pipeline.runName)"         
      displayName: 'Update Pipeline Name'
  - ${{ each env in parameters.environments }}:
    - template: Helm/deploy-single-oidc.yaml@devops-templates
      parameters:
        Environment: ${{ env.name }}
        cluster: ${{ env.cluster }}
        resourceGroup: ${{ env.rg }}
        subscription: ${{ env.subscription }}
        ChartName: deploy/helm/api-chart
        ChartVersion: $(resources.pipeline.build-pipeline.runName)
        ReleaseName: axon-hacomms-api
        Namespace: ${{ env.namespace }}
        keyVaults: ${{ env.keyVaults }}
        usehelmrepo: false
        valuesOverrideFiles: ${{ env.valuesOverrideFiles }}
        additionalHelmArgs: "--set image.tag=$(resources.pipeline.build-pipeline.runName)"
        replaceSecrets: true
        helmTest: true
        pool: "pv-pool"