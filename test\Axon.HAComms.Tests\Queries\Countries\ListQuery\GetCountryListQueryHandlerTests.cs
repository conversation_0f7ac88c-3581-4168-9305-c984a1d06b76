﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Countries.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Country;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Countries.ListQuery;

public class GetCountryListQueryHandlerTests
{
    private readonly GetCountryListQueryHandler handler;
    private readonly ICountriesRepository countryRepo;

    public GetCountryListQueryHandlerTests()
    {
        countryRepo = Substitute.For<ICountriesRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CountryMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetCountryListQueryHandler(countryRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<Country, CountryBuilder>.Generate(3);

        countryRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetCountryListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<Country, CountryBuilder>.Generate(3);

        countryRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetCountryListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }
}
