using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Delete;

internal class DeleteRouteOfAdministrationCommandHandler(
    IRouteOfAdministrationRepository repoRoutesOfAdministration,
    IProductExtensionsRepository productExtensionsRepo,
    ILogger<DeleteRouteOfAdministrationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteRouteOfAdministrationCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteRouteOfAdministrationCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoRoutesOfAdministration.GetItemAsync(request.Id);
        if (await productExtensionsRepo.ExistsAsync(x =>
                x.ProductExtensionRouteOfAdministrations != null && x.ProductExtensionRouteOfAdministrations.Any(s => s.RouteOfAdministrationId == entity.Id)))
        {
            logger.LogWarning("Route Of Administration {Route} has associated products and cannot be deleted.", entity.Id);
            throw new AssociationExistsException("Route Of Administration", entity.Id);
        }

        repoRoutesOfAdministration.DeleteItem(entity);
        await repoRoutesOfAdministration.SaveChangesAsync(userProvider);

        auditService.Log(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.ROUTE_OF_ADMINISTRATION_DELETED, AuditEventCategory.ROUTES_OF_ADMINISTRATION, AuditEventDescription.ROUTE_OF_ADMINISTRATION_DELETE, entity);

        return Result.Success();
    }
}
