﻿using Axon.HAComms.Application.Commands.Communications.Complete;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Complete
{
    public class CompleteCommunicationCommandValidatorTests
    {
        private readonly CompleteCommunicationCommandValidator sut;

        public CompleteCommunicationCommandValidatorTests()
        {
            sut = new CompleteCommunicationCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var request = new CompleteCommunicationCommandRequest(0);

            var result = sut.TestValidate(request);
            result.ShouldHaveValidationErrorFor(x => x.Id);
            Assert.Contains("'Id' must not be empty", result.Errors.First().ErrorMessage);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var request = new CompleteCommunicationCommandRequest(Fake.Communication.Id);

            // Act
            var result = sut.TestValidate(request);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
