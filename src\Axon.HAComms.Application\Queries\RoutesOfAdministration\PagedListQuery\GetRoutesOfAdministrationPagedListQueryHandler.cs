﻿using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Queries.RoutesOfAdministration.PagedListQuery;

internal class GetRoutesOfAdministrationPagedListQueryHandler(IRouteOfAdministrationRepository routesRepo) :
    IRequestHandler<GetRoutesOfAdministrationPagedListQueryRequest, ApiPagedListResult<RouteOfAdministrationPagedListModel>>
{
    private readonly IRouteOfAdministrationRepository routesRepo = routesRepo ?? throw new ArgumentNullException(nameof(routesRepo));
    private readonly Dictionary<string, Expression<Func<RouteOfAdministration, object>>> sortExpressions =
        new()
        {
            { TableFilterConstants.Name, x => x.Name },
            { TableFilterConstants.CreatedDate, x => x.CreatedDate },
            { TableFilterConstants.CreatedBy, x => x.CreatedBy },
            { TableFilterConstants.LastUpdatedDate, x => x.LastUpdatedDate },
            { TableFilterConstants.LastUpdatedBy, x => x.LastUpdatedBy }
        };

    public async Task<ApiPagedListResult<RouteOfAdministrationPagedListModel>> Handle(GetRoutesOfAdministrationPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildRouteOfAdministration(request.Filters);
        var predicate = BuildSortExpression(request);
        var query = routesRepo.GetQueryableItems();

        var entities = await query
            .FilterItems(expression, predicate, request.Skip, request.Take)
            .Select(x => new RouteOfAdministrationPagedListModel(x.Id, x.Name, x.ProductExtensions.Count != 0, x.CreatedDate, x.CreatedBy, x.LastUpdatedDate, x.LastUpdatedBy))
            .ToListAsync(cancellationToken: cancellationToken);

        return new ApiPagedListResult<RouteOfAdministrationPagedListModel>(
            entities,
            new()
            {
                TotalItemCount = expression == null ? await query.CountAsync(cancellationToken) : await query.CountAsync(expression, cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }

    private Func<IQueryable<RouteOfAdministration>, IOrderedQueryable<RouteOfAdministration>> BuildSortExpression(GetRoutesOfAdministrationPagedListQueryRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Order))
        {
            return sub => sub.OrderBy(x => x.Name);
        }

        var orderSegments = request.Order.Split("=>");
        var propName = orderSegments[0];
        var order = orderSegments[1];
        var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

        if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
        {
            return CompareAndOrderBy(func);
        }

        return sub => sub.OrderBy(x => x.Name);

        Func<IQueryable<RouteOfAdministration>, IOrderedQueryable<RouteOfAdministration>> CompareAndOrderBy<TKey>(
            Expression<Func<RouteOfAdministration, TKey>> expression)
        {
            return isAsc ?
                sub => sub.OrderBy(expression) :
                sub => sub.OrderByDescending(expression);
        }
    }
}
