﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetCommentsIntegrationTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetCommentsByProductId_PagedList_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateCommentsForProduct(dbContext, product1, 26);
        comments.AddRange(await ApiTestHelper.GenerateCommentsForProduct(dbContext, product2, 3));

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, product1.Id, null, 20, 10);
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().NotBeNull();
        responseObj.Data.Count.Should().Be(6);
        responseObj.Paging.TotalItemCount.Should().Be(26);
        responseObj.Paging.Offset.Should().Be(20);
        responseObj.Paging.Limit.Should().Be(10);

        var excludedCommentId = responseObj.Data.First(c => c.ProductName == product1.Name).Id;
        var responseObjWithExcludedComment =
            await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, product1.Id, excludedCommentId, 20, 10);

        //Assert
        responseObjWithExcludedComment.Should().NotBeNull();
        responseObjWithExcludedComment.Data.Should().NotBeNull();
        responseObjWithExcludedComment.Data.Count.Should().Be(5);
        responseObjWithExcludedComment.Data.Select(x => x.ProductName).Distinct().Should().Equal(product1.Name);
        responseObjWithExcludedComment.Data.Select(x => x.ProductName).Should().NotContain(product2.Name);
        responseObjWithExcludedComment.Data.Select(x => x.Id).Should().NotContain(excludedCommentId);
        responseObjWithExcludedComment.Paging.TotalItemCount.Should().Be(25);
        responseObjWithExcludedComment.Paging.Offset.Should().Be(20);
        responseObjWithExcludedComment.Paging.Limit.Should().Be(10);
    }

    [Fact]
    public async Task GetGeneralGuidanceComments_PagedList_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateGeneralGuidanceComments(dbContext, 26);

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, null, null, 20, 10);
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().NotBeNull();
        responseObj.Data.Count.Should().Be(6);
        responseObj.Paging.TotalItemCount.Should().Be(26);
        responseObj.Paging.Offset.Should().Be(20);
        responseObj.Paging.Limit.Should().Be(10);

        var excludedCommentId = responseObj.Data.First().Id;
        var responseObjWithExcludedComment = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, null, excludedCommentId, 20, 10);

        //Assert
        responseObjWithExcludedComment.Should().NotBeNull();
        responseObjWithExcludedComment.Data.Should().NotBeNull();
        responseObjWithExcludedComment.Data.Count.Should().Be(5);
        responseObjWithExcludedComment.Data.Select(x => x.Id).Should().NotContain(excludedCommentId);
        responseObjWithExcludedComment.Data.Select(x => x.IsGeneralGuidance).Distinct().Should().Equal(true);
        responseObjWithExcludedComment.Paging.TotalItemCount.Should().Be(25);
        responseObjWithExcludedComment.Paging.Offset.Should().Be(20);
        responseObjWithExcludedComment.Paging.Limit.Should().Be(10);
    }

    [Fact]
    public async Task GetCommentsPagedList_InvalidTakeRequest_ReturnsAllItems()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateCommentsForProduct(dbContext, product1, 24);
        comments.AddRange(await ApiTestHelper.GenerateCommentsForProduct(dbContext, product2, 3));

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();
        
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, product1.Id, null, 10, -5);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().NotBeNull();
        responseObj.Data.Count.Should().Be(14);
        responseObj.Data.Select(x => x.ProductName).Distinct().Should().Equal(product1.Name);
        responseObj.Data.Select(x => x.ProductName).Should().NotContain(product2.Name);
        responseObj.Paging.TotalItemCount.Should().Be(24);
        responseObj.Paging.Offset.Should().Be(10);
    }

    [Fact]
    public async Task GetCommentsPagedList_InvalidSkipRequest_ReturnsNotSkippedItems()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateCommentsForProduct(dbContext, product1, 28);
        comments.AddRange(await ApiTestHelper.GenerateCommentsForProduct(dbContext, product2, 3));

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();
        
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, product1.Id, null, -10, 15);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().NotBeNull();
        responseObj.Data.Count.Should().Be(15);
        responseObj.Data.Select(x => x.ProductName).Distinct().Should().Equal(product1.Name);
        responseObj.Data.Select(x => x.ProductName).Should().NotContain(product2.Name);
        responseObj.Paging.TotalItemCount.Should().Be(28);
        responseObj.Paging.Limit.Should().Be(15);
    }

    [Fact]
    public async Task GetCommentsPagedList_InvalidProductIdRequest_ReturnsNoItems()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateCommentsForProduct(dbContext, product1, 24);
        comments.AddRange(await ApiTestHelper.GenerateCommentsForProduct(dbContext, product2, 3));

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(response.Id, TenantConstants.DEFAULT_TENANT, Fake.Product.Id, null, 0, 10);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task GetCommentsByPagedList_InvalidCommunicationIdRequest_ReturnsNoItems()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comments = await ApiTestHelper.GenerateCommentsForProduct(dbContext, product1, 24);
        comments.AddRange(await ApiTestHelper.GenerateCommentsForProduct(dbContext, product2, 3));

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comments.First()).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        foreach (var comment in comments.Skip(1).ToArray())
        {
            comment.CommunicationId = response.Id;
            await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment);
        }

        //Act
        var responseObj = await commentsApi.GetCommentsByCommunicationIdAsync(Fake.Communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id, null, 0, 10);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Data.Should().BeNullOrEmpty();
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        await TagsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.Comments.Clear();
        dbContext.Communications.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Tags.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
