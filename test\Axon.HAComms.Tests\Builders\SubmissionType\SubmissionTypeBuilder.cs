﻿using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.SubmissionType;

public class SubmissionTypeBuilder : IBuilder<Domain.Entities.SubmissionType>
{
		private string name;
        private DateTime createdDate;
		private DateTime lastUpdatedDate;
		private string lastUpdatedBy;
		private string createdBy;

    public SubmissionTypeBuilder()
		{
			this.name = Fake.SubmissionType.Name;
			this.createdDate = DateTime.Now;
			this.lastUpdatedDate = DateTime.Now;
			this.lastUpdatedBy = Fake.SubmissionType.LastUpdatedBy;
			this.createdBy = Fake.SubmissionType.CreatedBy;
		}

		public Domain.Entities.SubmissionType Build()
		{
			return new()
			{
				Name = name,
				CreatedDate = createdDate,
				LastUpdatedDate = lastUpdatedDate,
				LastUpdatedBy = lastUpdatedBy,
			    CreatedBy = createdBy
			};
		}

    public SubmissionTypeBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }
}
