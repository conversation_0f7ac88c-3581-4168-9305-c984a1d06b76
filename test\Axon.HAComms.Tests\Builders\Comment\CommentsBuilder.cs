﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Builders.ProductExtensions;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Comments
{
    public class CommentsBuilder : IBuilder<Comment>
    {
        private string description;
        private string lastUpdatedBy;
        private string createdBy;
        private DateTime createdDate;
        private DateTime lastUpdatedDate;
        private ProductExtension productExtension;
        private CommunicationsBuilder communicationBuilder;
        private bool isQuestionIncluded;
        private string birdsLinkToBIResponse;
        private string birdsLinkToBISAMP;

        public CommentsBuilder()
        {
            createdBy = Fake.Comment.CreatedBy;
            createdDate = DateTime.Now;
            lastUpdatedDate = DateTime.Now;
            lastUpdatedBy = Fake.Comment.LastUpdatedBy;
            description = Fake.Comment.Description;
            communicationBuilder = new CommunicationsBuilder();
            productExtension = (new ProductExtensionBuilder()).Build();
            birdsLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse;
            birdsLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP;
        }

        public Comment Build()
        {
            return new()
            {
                Description = description,
                CreatedBy = createdBy,
                CreatedDate = createdDate,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy,
                Communication = communicationBuilder.Build(),
                ProductExtensions = new List<ProductExtension> { productExtension },
                IsQuestionIncluded = isQuestionIncluded,
                BIRDSLinkToBIResponse = birdsLinkToBIResponse,
                BIRDSLinkToBISAMP = birdsLinkToBISAMP
            };
        }

        public CommentsBuilder WithQuestionIncluded(bool isQuestionIncluded)
        {
            this.isQuestionIncluded = isQuestionIncluded;
            return this;
        }

        public CommentsBuilder WithDescription(string description)
        {
            this.description = description;
            return this;
        }

        public CommentsBuilder WithProductExtension(ProductExtension productExtension)
        {
            this.productExtension = productExtension;
            return this;
        }

        public CommentsBuilder ToCommunication(Action<CommunicationsBuilder> action)
        {
            var builder = new CommunicationsBuilder();
            action.Invoke(builder);
            communicationBuilder = builder;
            return this;
        }
    }
}
