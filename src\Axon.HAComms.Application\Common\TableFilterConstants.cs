﻿namespace Axon.HAComms.Application.Common
{
    public static class TableFilterConstants
    {
        public const string Name = "name";
        public const string Code = "code";
        public const string IsActive = "isactive";
        public const string Substances = "substances";
        public const string ProductCode = "productcode";
        public const string RouteOfAdministration = "routeofadministration";
        public const string DosageForm = "dosageform";
        public const string CreatedDate = "createddate";
        public const string CreatedBy = "createdby";
        public const string LastUpdatedDate = "lastupdateddate";
        public const string LastUpdatedBy = "lastupdatedby";
        public const string Subject = "subject";
        public const string CountryId = "countryid";
        public const string CountryName = "countryname";
        public const string DateOfCommunication = "dateofcommunication";
        public const string ProductNames = "productnames";
        public const string IsCompleted = "iscompleted";
        public const string Description = "description";
    }
}
