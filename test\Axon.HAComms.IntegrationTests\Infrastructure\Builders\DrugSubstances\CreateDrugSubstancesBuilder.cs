using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;

public class CreateDrugSubstancesBuilder
{
    private string code = Fake.DrugSubstance.Code;
    private string name = Fake.DrugSubstance.Name;
    private string description = Fake.DrugSubstance.Description;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static CreateDrugSubstancesBuilder Default() => new();

    public DrugSubstance Build()
    {
        return new DrugSubstance()
        {
            Code = code,
            Name = name,
            Description = description,
            Tenant = tenant
        };
    }

    public CreateDrugSubstancesBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CreateDrugSubstancesBuilder WithCode(string code)
    {
        this.code = code;
        return this;
    }

    public CreateDrugSubstancesBuilder WithDescription(string description)
    {
        this.description = description;
        return this;
    }

    public CreateDrugSubstancesBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
