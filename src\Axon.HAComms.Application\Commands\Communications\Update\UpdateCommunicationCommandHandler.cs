using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Communications.Update;

internal class UpdateCommunicationCommandHandler(
    ICommunicationsRepository communicationRepo,
    IMapper mapper,
    ILogger<UpdateCommunicationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<UpdateCommunicationCommandRequest, UpdateCommunicationCommandResponse>
{
    public async Task<UpdateCommunicationCommandResponse> Handle(UpdateCommunicationCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await communicationRepo.GetItemAsync(request.Id);

        if (entity.IsCompleted)
        {
            throw new InvalidOperationException("Cannot update completed communication!");
        }

        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.COMMUNICATION_UPDATED, AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_UPDATE, entity,
            async () =>
            {
                entity.Subject = request.Subject;
                entity.SubmissionTypeId = request.SubmissionTypeId;
                entity.CountryId = request.CountryId;
                entity.DateOfCommunication = request.DateOfCommunication;
                entity.Applications = mapper.Map<ICollection<Domain.Entities.Application>>(request.Applications);
                
                communicationRepo.UpdateItem(entity);
                await communicationRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Communication {Subject} updated successfully.", entity.Subject);
            });

        var response = mapper.Map<UpdateCommunicationCommandResponse>(entity);
        return response;
    }
}
