﻿using Azure.Search.Documents.Models;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IAzureSearchHelper
{
    Task<SearchResults<T>> TextSearchAsync<T>(
        string searchText,
        int? skip,
        int? take,
        string? sort,
        string[]? selectFields = null,
        string? filter = null,
        bool fuzzy = false);

    Task CreateIndexAsync<T>();

    Task CreateIndexer();

    Task CreateDataSource();

    Task DeleteIndexer();

    Task DeleteIndex();

    Task DeleteDataSource();
}
