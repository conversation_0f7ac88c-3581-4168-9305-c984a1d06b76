﻿IF EXISTS(SELECT 1 FROM sys.views WHERE NAME='SearchInComments' and TYPE='v')
DROP VIEW SearchInComments;
GO
CREATE VIEW [dbo].[SearchInComments] AS
       SELECT cpdd.[Id],
            cpdd.[Description],
            cpdd.[Question],
            cpdd.[Response],
            cpdd.[IsGeneralGuidance],
            cpdd.[IsQuestionIncluded],
            cpdd.[BIRDSLinkToBIResponse],
            cpdd.[BIRDSLinkToBISAMP],
            cpdd.[Tenant],
            cpdd.ProductId,
            LOWER(cpdd.ProductCodes) AS ProductCodes,
            cpdd.ProductName,
            dsra.[DrugSubstanceIds],
            dsra.[RouteOfAdministrationIds],
            dsra.[DrugSubstanceNames],
            dsra.[TagIds],
            dsra.[TagNames],
            dsra.[ProductTypeIds],
            dsra.[ProductTypeNames],
            cpdd.DosageFormIds,
            CommAppSub.[SubmissionTypeId],
            CommAppSub.[CommunicationId],
            CommAppSub.[DateOfCommunication],
            CommAppSub.[IsCompleted],
            CommAppSub.[CountryId] AS CountryId,
            CommAppSub.[Subject],
            country.[Name] AS CountryName,
            cpdd.DosageFormNames,
            dsra.[RouteOfAdministrationNames],
            st.[Name] AS SubmissionTypeName,
            CommAppSub.[ApplicationIds],
            CommAppSub.[SubmissionIds],
            LOWER(CommAppSub.[ApplicationNumbers]) AS ApplicationNumbers,
            LOWER(CommAppSub.[SubmissionNumbers]) AS SubmissionNumbers,
    		GREATEST(dsra.CommAdminLastUpdateDate, dsra.CommSubLastUpdatedDate, dsra.CommTagLastUpdatedDate, cpdd.cLastUpdatedDate) as LastUpdatedDate
        FROM (
                SELECT ct.CommentId,
                    gcds.DrugSubstanceIds,
                    gra.RouteOfAdministrationIds,
                    gpt.ProductTypeIds,
                    gcds.DrugSubstanceNames,
                    gra.RouteOfAdministrationNames,
                    ct.TagIds,
                    ct.TagNames,
                    gpt.ProductTypeNames,
    				gcds.CommSubLastUpdatedDate,
    				gra.LastUpdateDate AS CommAdminLastUpdateDate, 
    				ct.LastUpdatedDate AS CommTagLastUpdatedDate
                FROM (
                        SELECT cds.CommentId,
                            '[' + STRING_AGG(ds.Id, ',') + ']' AS DrugSubstanceIds,
                            '[' + STRING_AGG('"' + ds.Name + '|' + ds.Code + '"', ', ') + ']' AS DrugSubstanceNames,
    						MAX(cds.LastUpdatedDate) AS CommSubLastUpdatedDate
                        FROM [dbo].[DrugSubstances] ds
                            JOIN [dbo].[CommentDrugSubstances] cds ON cds.[DrugSubstanceId] = ds.[Id]
                        GROUP BY cds.[CommentId]
                    ) gcds
                    JOIN 
                        (SELECT cpe.CommentId as CommentId,
                                '[' + STRING_AGG(cpera.RouteOfAdministrationId, ',') + ']' AS RouteOfAdministrationIds,
                                STRING_AGG(ra.Name, ', ') AS RouteOfAdministrationNames,
                                MAX(cpera.LastUpdatedDate)  AS LastUpdateDate
                        FROM  dbo.CommentProductExtension AS cpe
                            JOIN  dbo.CommentProductExtensionRoutesOfAdministration AS cpera ON cpera.CommentProductExtensionId = cpe.Id
                            JOIN  dbo.RouteOfAdministrations AS ra ON ra.Id=cpera.RouteOfAdministrationId
                        GROUP BY cpe.CommentId) AS gra ON gra.CommentId = gcds.CommentId
                    JOIN (
                        SELECT cpe.CommentId,
                            '[' + STRING_AGG(pt.Id, ',') + ']' AS ProductTypeIds,
                            '["' +  STRING_AGG(pt.Name, '", "') + '"]' AS ProductTypeNames
                        FROM [dbo].[ProductTypes] pt
                            JOIN dbo.ProductProductTypes AS ppt ON pt.Id = ppt.ProductTypeId
                            JOIN dbo.ProductExtensions AS pe ON pe.ProductId = ppt.ProductId
                            JOIN dbo.CommentProductExtension AS cpe ON cpe.ProductExtensionId = pe.Id
                        GROUP BY cpe.CommentId
                    ) AS gpt ON gpt.CommentId = gcds.[CommentId]
                    RIGHT JOIN (
                        SELECT ct.CommentId,
                            '[' + STRING_AGG(ct.TagId, ',') + ']' AS TagIds,
                            STRING_AGG(t.Name, ', ') AS TagNames,
    						MAX(ct.LastUpdatedDate) AS LastUpdatedDate
                        FROM [dbo].[Tags] as t
                            JOIN [dbo].[CommentTags] AS ct ON t.Id = ct.TagId
                        GROUP BY ct.CommentId
                    ) AS ct ON ct.[CommentId] = gcds.[CommentId]
            ) AS dsra
			RIGHT JOIN (SELECT c.[Id],
						 c.[Description],
						 c.[Question],
						 c.[Response],
						 c.[IsGeneralGuidance],
						 c.[IsQuestionIncluded],
						 c.[BIRDSLinkToBIResponse],
						 c.[BIRDSLinkToBISAMP],
						 c.[Tenant],
						 c.CommunicationId,
						 GREATEST(c.LastUpdatedDate, dp.LastUpdatedDate, pe.LastUpdatedDate) AS cLastUpdatedDate,
						 dp.[Name] AS ProductName,
						 pe.[ProductId],
						 '["' +  STRING_AGG(pe.[PCID], '", "') + '"]' AS ProductCodes,
						 STRING_AGG(df.[Name], '| ') AS DosageFormNames,
						 '[' + STRING_AGG(pe.[DosageFormId], ',') + ']' AS DosageFormIds
				FROM dbo.Comments AS c
				LEFT OUTER JOIN dbo.CommentProductExtension AS cpe ON cpe.CommentId = c.Id
				LEFT OUTER JOIN dbo.ProductExtensions AS pe ON pe.Id = cpe.ProductExtensionId
				LEFT OUTER JOIN dbo.DosageForms AS df ON pe.DosageFormId = df.Id
				LEFT OUTER JOIN dbo.DrugProducts AS dp ON pe.ProductId = dp.Id
				GROUP BY c.[Id],
							c.[Description],
							c.[Question],
							c.[Response],
							c.[IsGeneralGuidance],
							c.[IsQuestionIncluded],
							c.[BIRDSLinkToBIResponse],
							c.[BIRDSLinkToBISAMP],
							c.[Tenant],
							GREATEST(c.LastUpdatedDate, dp.LastUpdatedDate, pe.LastUpdatedDate),
							c.CommunicationId,
							pe.ProductId,
							dp.[Name]) cpdd ON cpdd.Id = dsra.CommentId
            JOIN (
                SELECT comm.SubmissionTypeId,
                    comm.Id AS CommunicationId,
                    comm.DateOfCommunication,
                    comm.IsCompleted,
                    comm.CountryId AS CountryId,
                    comm.Subject,
    				MAX(comm.LastUpdatedDate) AS CommunLastUpdateDate,
                    '[' + STRING_AGG(a.Id, ',') + ']' AS ApplicationIds,
                    '[' + STRING_AGG(sub.Id, ',') + ']' AS SubmissionIds,
                    '["' + STRING_AGG(a.Number, '", "') + '"]' AS ApplicationNumbers,
                    '["' + STRING_AGG(sub.Number, '", "') + '"]' AS SubmissionNumbers
                FROM [dbo].[Communications] comm
                    LEFT JOIN [dbo].[Applications] AS a ON a.CommunicationId = comm.Id
                    LEFT JOIN [dbo].[Submissions] AS sub ON sub.ApplicationId = a.Id
                GROUP BY comm.SubmissionTypeId,
                    comm.Id,
                    comm.DateOfCommunication,
                    comm.IsCompleted,
                    comm.CountryId,
                    comm.Subject
            ) AS CommAppSub ON CommAppSub.CommunicationId = cpdd.CommunicationId
            JOIN [dbo].[Countries] AS country ON CommAppSub.CountryId = country.Id
            JOIN [dbo].[SubmissionTypes] AS st ON st.Id = CommAppSub.SubmissionTypeId
