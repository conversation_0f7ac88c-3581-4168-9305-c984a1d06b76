﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.DosageForms.Create;
using Axon.HAComms.Application.Commands.DosageForms.Delete;
using Axon.HAComms.Application.Commands.DosageForms.Update;
using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Application.Queries.DosageForms.ListQuery;
using Axon.HAComms.Application.Queries.DosageForms.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/DosageForms")]
public class DosageFormsController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all dosage forms
    /// </summary>
    /// <returns>All dosage forms</returns>
    [HttpGet("all", Name = "GetDosageFormList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewDosageFormList))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<DosageFormModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetDosageFormListAsync()
        => await Send(new GetDosageFormListQueryRequest());

    /// <summary>
    /// Get dosage forms paged list
    /// </summary>
    /// <returns>Dosage forms paged list</returns>
    [HttpGet(Name = "GetPagedDosageFormsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewDosageForm))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<DosageFormPagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
        => await Send(new GetDosageFormPagedListQueryRequest(filters, skip, take, order));

    /// <summary>
    /// Create dosage form
    /// </summary>
    /// <returns>Dosage Form</returns>
    [HttpPost(Name = "CreateDosageForm")]
    //[HasPermissions(nameof(HacommsPermissions.CreateDosageForm))]
    [ProducesResponseType(200, Type = typeof(CreateDosageFormCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateDosageFormAsync([FromBody] CreateDosageFormCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update dosage form
    /// </summary>
    /// <returns>Updated dosage form reference</returns>
    [HttpPut(Name = "UpdateDosageForm")]
    //[HasPermissions(nameof(HacommsPermissions.EditDosageForm))]
    [ProducesResponseType(200, Type = typeof(UpdateDosageFormCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateDosageFormAsync([FromBody] UpdateDosageFormCommandRequest command) => await Send(command);


    /// <summary>
    /// Delete dosage form
    /// </summary>
    /// <returns>Deleted dosage form reference</returns>
    [HttpDelete("{id}", Name = "DeleteDosageForm")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteDosageForm))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteDosageFormAsync(int id) => await Send(new DeleteDosageFormCommandRequest(id));
}
