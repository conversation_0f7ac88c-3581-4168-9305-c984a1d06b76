﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Communications.Complete;

internal class CompleteCommunicationCommandHandler(
    ICommunicationsRepository communicationRepo,
    IUserProvider userProvider,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    ILogger<CompleteCommunicationCommandHandler> logger) : IRequestHandler<CompleteCommunicationCommandRequest, Result>
{
    public async Task<Result> Handle(CompleteCommunicationCommandRequest request, CancellationToken cancellationToken)
    {
        var correlationId = correlationIdProvider.Provide();
        var communication = await communicationRepo.GetOpenCommunicationByIdAsync(request.Id, cancellationToken);

        await auditService.LogAsync(
           correlationId, clientDetailsProvider.Provide(),
           AuditEventType.COMMUNICATION_COMPLETED, AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_COMPLETED, communication,
           async () =>
           {
               communication.Complete();
               communicationRepo.UpdateItem(communication);
               await communicationRepo.SaveChangesAsync(userProvider);
               logger.LogInformation("Communication {Subject} completed successfully.", communication.Subject);
           });

        return Result.Success();
    }
}
