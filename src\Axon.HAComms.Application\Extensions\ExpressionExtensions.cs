﻿using System.Linq.Expressions;
using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Interfaces;

namespace Axon.HAComms.Application.Extensions;

public static class ExpressionExtensions
{
    public static Expression<Func<T, bool>> BuildWithName<T>(this Expression<Func<T, bool>>? expression, string filterValue) where T : IEntityWithName
    {
        return expression.AndAlso(x => x.Name.Contains(filterValue));
    }

    public static Expression<Func<T, bool>> BuildWithCreatedDate<T>(this Expression<Func<T, bool>>? expression, string filterValue) where T : BaseEntity
    {
        return expression.AndAlso(x => x.CreatedDate.Date.ToString().Contains(filterValue));
    }

    public static Expression<Func<T, bool>> BuildWithCreatedBy<T>(this Expression<Func<T, bool>>? expression, string filterValue) where T : BaseEntity
    {
        return expression.AndAlso(x => x.CreatedBy.Contains(filterValue));
    }

    public static Expression<Func<T, bool>> BuildWithLastUpdatedDate<T>(this Expression<Func<T, bool>>? expression, string filterValue) where T : BaseEntity
    {
        return expression.AndAlso(x => x.LastUpdatedDate.Date.ToString().Contains(filterValue));
    }

    public static Expression<Func<T, bool>> BuildWithLastUpdatedBy<T>(this Expression<Func<T, bool>>? expression, string filterValue) where T : BaseEntity
    {
        return expression.AndAlso(x => x.LastUpdatedBy.Contains(filterValue));
    }
}
