﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IDrugSubstancesRepository : IRepository<DrugSubstance>
{
    Task<DrugSubstance> GetItemAsync(int id);
    Task<IEnumerable<DrugSubstance>> GetItemsAsync();
    Task<List<DrugSubstance>> GetAllByIdsAsync(params int[] ids);
    Task<bool> ExistsAsync(Expression<Func<DrugSubstance, bool>> filter);
    IQueryable<DrugSubstance> GetQueryableItems();
}
