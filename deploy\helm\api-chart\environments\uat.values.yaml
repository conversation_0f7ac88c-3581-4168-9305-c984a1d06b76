image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: latest

ingress:
  tls:
    - tlsSecretName: tls-app-eu-smartphlex-com
      hosts:
        - app-eu.smartphlex.com
        - app.smartphlex.com
  hosts:
    - host: app-eu.smartphlex.com
      paths:
        - path: /axon-hacommsuat-api/(.*)
    - host: app.smartphlex.com
      paths:
        - path: /axon-hacommsuat-api/(.*)

replicas: 2
minAvailability: 1

keyVaultName: hac-uat-kv-eun
clientId: b07bf619-7ce5-4dd5-8fa6-14fb944f8433 #Smartphlex prodeu
azureSearch:
  isEnabled: true
  serviceName: ss-prod-ss-eun
  IndexName: hacomms-index-uat
  IndexerName: hacomms-indexer-uat
  DataSourceName: hacomms-db-uat

azureWorkload:
  appName: axon-hacomms-uat
  clientId: d95f8f43-d4c7-466b-8e19-49f357f2e833
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AppScope: "api://smartphlex-prodeu/.default"
ApiHost: "https://app-eu.smartphlex.com/api/core"
GrpcHost: "http://axon-core-api-grpc.axon-core-prod.svc.cluster.local:9090"
AppName: "hacommsuat"

DataProtectionBlobStorageUri: 'https://axnprodstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-prod-kv-eun.vault.azure.net/keys/AxonDataProtection'