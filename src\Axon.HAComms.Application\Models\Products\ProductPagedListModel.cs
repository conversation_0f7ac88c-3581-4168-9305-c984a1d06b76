#nullable enable

using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.ProductExtensions;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Application.Models.Products
{
	public class ProductPagedListModel
	{
        public int Id { get; set; }

        [Required]
        public string? Name { get; set; }

		public ICollection<DrugSubstanceCodeAndName>? Substances { get; set; }

        public bool IsActive { get; set; }

       // public bool Expanded { get { return true; } }

        public ICollection<ProductExtensionModel>? ProductExtensions { get; set; }

        public ProductPagedListModel()
        {

        }

        public ProductPagedListModel(
			int id, 
			string name,
            ICollection<DrugSubstanceCodeAndName> substances,
            ICollection<ProductExtensionModel>? productExtensions,
            bool isActive)
		{
            Id = id;
			Name = name;
            Substances = substances;
            ProductExtensions = productExtensions;
            IsActive = isActive;
        }
    }
}
