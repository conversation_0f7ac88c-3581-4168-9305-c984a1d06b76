﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.DrugSubstances.Create;
using Axon.HAComms.Application.Commands.DrugSubstances.Delete;
using Axon.HAComms.Application.Commands.DrugSubstances.Update;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Queries.DrugSubstances.ListQuery;
using Axon.HAComms.Application.Queries.DrugSubstances.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/DrugSubstances")]
public class DrugSubstancesController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all drug substances
    /// </summary>
    /// <returns>All drug substances</returns>
    [HttpGet("all", Name = "GetDrugSubstancesList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewSubstanceList))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<DrugSubstanceModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetDrugSubstancesAsync()
        => await Send(new GetDrugSubstancesListQueryRequest());


    /// <summary>
    /// Get all drug substances
    /// </summary>
    /// <returns>All drug substances</returns>
    [HttpGet(Name = "GetPagedDrugSubstancesList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewSubstance))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<DrugSubstancePagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
        => await Send(new GetDrugSubstancesPagedListQueryRequest(filters, skip, take, order));

    /// <summary>
    /// Create drug substance
    /// </summary>
    /// <returns>Drug substance</returns>
    [HttpPost(Name = "CreateDrugSubstance")]
    //[HasPermissions(nameof(HacommsPermissions.CreateSubstance))]
    [ProducesResponseType(200, Type = typeof(CreateDrugSubstanceCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateDrugSubstanceAsync([FromBody] CreateDrugSubstanceCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update drug substance
    /// </summary>
    /// <returns>Updated drug substance reference</returns>
    [HttpPut(Name = "UpdateDrugSubstance")]
    //[HasPermissions(nameof(HacommsPermissions.EditSubstance))]
    [ProducesResponseType(200, Type = typeof(UpdateDrugSubstanceCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateDrugSubstanceAsync([FromBody] UpdateDrugSubstanceCommandRequest command) => await Send(command);


    /// <summary>
    /// Delete drug substance
    /// </summary>
    /// <returns>Deleted drug substance reference</returns>
    [HttpDelete("{id}", Name = "DeleteDrugSubstance")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteSubstance))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteDrugSubstanceAsync(int id) => await Send(new DeleteDrugSubstanceCommandRequest(id));
}
