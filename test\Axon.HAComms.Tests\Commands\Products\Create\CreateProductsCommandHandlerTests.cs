﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Products.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using Axon.HAComms.Tests.Builders.ProductExtensions;
using Axon.HAComms.Tests.Builders.ProductTypes;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Create;

public class CreateProductsCommandHandlerTests
{
    private readonly CreateProductCommandHandler handler;
    private readonly IProductsRepository productsRepo;
    private readonly IDrugSubstancesRepository drugSubstancesRepo;
    private readonly IRouteOfAdministrationRepository routeOfAdministrationRepo;
    private readonly IDosageFormsRepository dosageFormsRepo;
    private readonly IProductTypesRepository productTypesRepo;
    private readonly IProductExtensionsRepository productExtensionsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IUserProvider userProvider;
    private readonly IAuditService auditService;

    public CreateProductsCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");
        userProvider = Substitute.For<IUserProvider>();

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new DrugSubstancesMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
            cfg.AddProfile(new DosageFormsMappingProfile());
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
            cfg.AddProfile(new ProductTypesMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var logger = Substitute.For<ILogger<CreateProductCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        productsRepo = Substitute.For<IProductsRepository>();
        drugSubstancesRepo = Substitute.For<IDrugSubstancesRepository>();
        routeOfAdministrationRepo = Substitute.For<IRouteOfAdministrationRepository>();
        dosageFormsRepo = Substitute.For<IDosageFormsRepository>();
        productTypesRepo = Substitute.For<IProductTypesRepository>();
        handler = new CreateProductCommandHandler(productsRepo, drugSubstancesRepo, routeOfAdministrationRepo, dosageFormsRepo, productTypesRepo, productExtensionsRepo,
            mapper, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        var dosageForm = new DosageFormBuilder().Build();
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithIsActive(Fake.ProductExtension.IsActive)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DRUG_PRODUCT_CREATED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_CREATE,
                Arg.Any<Product>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequest_AddsProduct()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        var dosageForm = new DosageFormBuilder().Build();
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithIsActive(Fake.ProductExtension.IsActive)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);
        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.DRUG_PRODUCT_CREATED,
                AuditEventCategory.DRUG_PRODUCTS,
                AuditEventDescription.DRUG_PRODUCT_CREATE,
                Arg.Any<Product>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        await handler.Handle(request, CancellationToken.None);

        // Assert
        await drugSubstancesRepo
            .Received(1)
            .GetAllByIdsAsync(Arg.Is<int[]>(x => x[0] == drugSubstanceIds[0] && x[1] == drugSubstanceIds[1]));

        await productTypesRepo
            .Received(1)
            .GetAllByIdsAsync(Arg.Is<int[]>(x => x[0] == productTypeIds[0] && x[1] == productTypeIds[1]));

        await productTypesRepo
            .Received(1)
            .GetItemByFilterAsync(Arg.Any<Expression<Func<ProductType, bool>>>());

        await dosageFormsRepo
            .Received(1)
            .GetAllByIdsAsync(Arg.Is<int[]>(x => x[0] == productExtensions[0].DosageFormId));

        await routeOfAdministrationRepo
            .Received(1)
            .GetAllByIdsAsync(Arg.Is<int[]>(x => x[0] == productExtensions[0].RouteOfAdministrationIds[0]));

        productsRepo
            .ReceivedWithAnyArgs(1)
            .AddItem(default!);
        productsRepo
            .Received(1)
            .AddItem(Arg.Is<Product>(product => product.IsActive
                                                     && product.DrugSubstanceProducts.Count == drugSubstances.Count
                                                     && product.ProductProductTypes.Count == productTypes.Count));
        productExtensionsRepo
            .Received(1)
            .AddItem(Arg.Is<ProductExtension>(pe =>
                productExtensions[0].PCID == pe.PCID &&
                productExtensions[0].DosageFormId == pe.DosageFormId &&
                productExtensions[0].IsActive == pe.IsActive &&
                pe.ProductExtensionRouteOfAdministrations != null &&
                productExtensions[0].RouteOfAdministrationIds.Count == pe.ProductExtensionRouteOfAdministrations.Count));

        await productsRepo.Received(1).SaveChangesAsync(userProvider);
    }

    [Fact]
    public async Task Handle_MissingDosageForm_ThrowsException()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = async () => await handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_MissingRouteOfAdministration_ThrowsException()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(2);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        var dosageForm = new DosageFormBuilder().Build();
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([Fake.RouteOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = async () => await handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ManyProductExtensions_ReturnsSuccessResult()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        var productTypes = TestEntitiesGenerator<ProductType, ProductTypeBuilder>.Generate(3);
        var productTypeIds = productTypes.Select(d => d.Id).ToArray();
        productTypesRepo.GetAllByIdsAsync(productTypeIds).Returns(productTypes.ToArray());
        productTypesRepo.GetQueryableItems().Returns(productTypes.AsQueryable());

        var dosageForm = new DosageFormBuilder().Build();
        dosageFormsRepo.GetAllByIdsAsync(dosageForm.Id).Returns(Task.FromResult(new[] { dosageForm }));
        var routesOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(3);
        routeOfAdministrationRepo.GetAllByIdsAsync(routesOfAdministration.Select(r => r.Id).ToArray()).Returns(Task.FromResult(routesOfAdministration.ToArray()));

        var productExtension1 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministration[0].Id]).Build();

        var productExtension2 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministration[1].Id]).Build();

        var productExtension3 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministration[2].Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension1, productExtension2, productExtension3 };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], [.. productTypeIds]);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DRUG_PRODUCT_CREATED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_CREATE,
                Arg.Any<Product>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_MissingProductTypeIds_ThrowsException()
    {
        // Arrange
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(2);
        var drugSubstanceIds = drugSubstances.Select(d => d.Id).ToArray();
        drugSubstancesRepo.GetAllByIdsAsync(drugSubstanceIds).Returns(drugSubstances);
        drugSubstancesRepo.GetQueryableItems().Returns(drugSubstances.AsQueryable());

        productTypesRepo.GetAllByIdsAsync(Arg.Any<int>()).ReturnsNull();

        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministration.Id).Returns(Task.FromResult(new[] { routeOfAdministration }));

        var productExtension = ProductExtensionModelBuilder.Default()
            .WithRouteOfAdministrationIds([routeOfAdministration.Id]).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };

        var request = new CreateProductCommandRequest(productName, isActive, productExtensions, [.. drugSubstanceIds], []);

        // Act
        var result = async () => await handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
