﻿# Axon.HAComms.Api
[![Build Status](https://dev.azure.com/Phlexglobal/Axon/_apis/build/status/Axon.HAComms.Api.Build?repoName=Axon.HAComms.Api&branchName=develop)](https://dev.azure.com/Phlexglobal/Axon/_build/latest?definitionId={REPLACE}&repoName=Axon.HAComms.Api&branchName=develop)

## Project Overview
The software solution intends to offer an opportunity to analyze interactions with health regulatory authorities, including their trends, interpretations of guidelines, requirements, and expectations. This is a REST API project which provides client SDK.

### Project Structure

```

├── deploy                                   # Deployment related files, scripts, charts etc
│    └── helm                                # Helm charts related to deployment
├── scripts                                  # Helpful scripts for setup, code generation, local deployment 
│    └── GenerateSdks.ps1                    # Script to regenerate the SDKs
├── src
     ├── Axon.HAComms.Api                    # Web API and application configuration
     ├── Axon.HAComms.Application            # Application layer, CQRS, Business models, Automapper, services and helpers
     ├── Axon.HAComms.Domain                 # Domain layer for domain entities and associated business logic
     ├── Axon.HAComms.Infrastructure         # Infrastructure for data persistence etc
     ├── sdk/Axon.HAComms.Sdk.Net            # Client .NET based SDK for consuming the API based on OpenAPI standards
     ├── sdk/Axon.HAComms.Sdk.TypeScript     # Client TypeScript based SDK for consuming the API based on OpenAPI standards
├── test
     ├── Axon.HAComms.ArchTests              # Check conformity to architectural rules
     ├── Axon.HAComms.IntegrationTests       # Integration tests requiring infrastructure presence to execute.
     ├── Axon.HAComms.Tests                  # Unit tests
     ├── Axon.HAComms.Tests.Common           # Shared project for the integration and unit tests containing some common test logic

```

## Local Infrastructure
The prerequisite infrastructure should be created via the the docker-compose file.
```
docker-compose -f .\docker-compose-test-inf.yml up -d
```

The local infrastructure consists of the following services:

|Service|Ports|Local Access|Credentials
|-|-|-|-|
|Microsoft SQL Server|1433|SQL Server Management Studio - localhost|sa & Pass@wOrd

Note: If you already have a local install of SQL Server, this either needs to be stopped before running the local infrastructure, or the exposed port needs to be changed to an available port.

## Migrations
To add new migration open Package Manager Console, set the default project to Axon.HAComms.Infrastructure and use command Add-Migration <migration name>
Example command:
```
Add-Migration InitialCreate
```

To create or update the database with new migrations open Package Manager Console, set the default project to Axon.HAComms.Infrastructure and use command Update-Database.
This will update database specified in setting `ConnectionStrings:default`.

## Getting Started
1. Change the following in `Axon.HAComms.Api/appsettings.json`
    - "ConnectionStrings:default" - connection string to local SQL Server database
    - "Audit:SqlServer:ConnectionString" - connection string to the Audit database (this can be the same as the above)
    - "Swagger:BasePath" - update to empty string in order to see the correct swagger page
    - "AzureSearch" - all settings have to match the Development environment as there is no local instance the Azure AI Search
2. Once the project is run you should see a Swagger page with all API endpoints listed
3. Each endpoint has [HasPermissions] attribute used to restrict access for not authorized users. 
4. The application is multi-tenant. All API requests require a valid tenant as part of the request parameters in order to retrieve the corresponding data.

## Deploy
### Helm Chart
Helm charts are used to deploy this API. See the ~/deploy/helm

Example install:
```
helm install example ./api-chart
```

## API
### SDK Generation
SDKs for C# and TypeScript is code generated based on the swagger by using GenerateSdks.ps1 script.

```
.\GenerateSdks.ps1
```

### Authentication and Authorisation users
For local development you need to add your user with permissions to appauth.json file:
```
Axon.HAComms.Api\test\Axon.HAComms.IntegrationTests
```

## Tests
### Integration Tests
The required infrastructure for executing the integration tests includes only SQL server.
Review the `appsetting.Development.json` config file in Axon.HAComms.IntegrationTests to ensure the connection string to the database is correct.
Users used in the integration tests need to be added to appauth.json file:
```
Axon.HAComms.Api\test\Axon.HAComms.IntegrationTests
```

# Build and Test
## Build
1. Run the Axon.HAComms.Api from Visual Studio 
2. A Swagger page is opened and provides a list with available APIs 

### Regenerate client SDKs
When a change to the models or API endpoints is made the client SDK should be updated. 
In order to update client SDKs you need to run the script scripts/GenerateSdks.ps1.

