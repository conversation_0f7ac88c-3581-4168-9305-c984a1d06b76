﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Domain.Entities;
using FluentValidation;

namespace Axon.HAComms.Application.Common;

public static class CommonValidators
{
    public static void AddCommunicationRequestValidation<T>(this AbstractValidator<T> validator, ISubmissionTypesRepository subTypeRepo, ICountriesRepository countryRepo)
        where T : CommunicationRequestModel
    {
        validator.RuleFor(x => x.Subject)
            .NotEmpty();

        validator.RuleFor(x => x.DateOfCommunication)
            .NotEmpty();

        validator.RuleFor(x => x.SubmissionTypeId)
            .NotEmpty()
            .Custom((submissionTypeId, context) =>
            {
                var exists = subTypeRepo.ExistsAsync(c => c.Id == submissionTypeId).GetAwaiter().GetResult();
                if (!exists)
                {
                    context.AddFailure(nameof(SubmissionType), $"Submission Type with id '{submissionTypeId}' does not exist.");
                }
            });

        validator.RuleFor(x => x.CountryId)
            .NotEmpty()
            .Custom((countryId, context) =>
            {
                var exists = countryRepo.ExistsAsync(c => c.Id == countryId).GetAwaiter().GetResult();
                if (!exists)
                {
                    context.AddFailure(nameof(Country), $"Country with id '{countryId}' does not exist.");
                }
            });

        validator.RuleFor(x => x.Applications.Count).LessThanOrEqualTo(5);
        validator.RuleFor(x => x.Applications)
            .Must(applications => applications.Select(x => x.Number).Distinct().Count() == applications.Count)
            .WithMessage("Duplicate Application Numbers are not allowed.");

        validator.RuleForEach(x => x.Applications).ChildRules(app =>
        {

            app.RuleFor(a => a.Number)
                .Must(NotContainIllegalCharacters)
                .WithMessage("Application Number cannot contain illegal characters.");

            app.RuleFor(a => a.Number).NotEmpty();
            app.RuleForEach(a => a.Submissions).ChildRules(sub =>
            {
                sub.RuleFor(s => s.Number).NotEmpty();
                sub.RuleFor(s => s.Number)
                    .Must(NotContainIllegalCharacters)
                    .WithMessage("Submission Number cannot contain illegal characters.");
            });
        }).When(x => x.Applications.Count != 0);
    }

    public static void AddProductRequestValidation<T>(this AbstractValidator<T> validator)
        where T : ProductRequestModel
    {
        validator.RuleFor(x => x.Name)
            .MaximumLength(30)
            .NotEmpty();

        validator.RuleFor(x => x.Name)
            .Must(NotContainIllegalCharacters)
            .WithMessage("Name cannot contain illegal characters.");

        validator.RuleFor(x => x.DrugSubstanceIds).NotEmpty();

        validator.RuleForEach(x => x.DrugSubstanceIds).ChildRules(cr1 =>
        {
            cr1.RuleFor(d => d).NotEqual(0).WithMessage(_ => "Drug substance Id must not be 0.");
        }).When(x => x.DrugSubstanceIds.Count != 0);

        validator.RuleFor(x => x.ProductTypeIds).NotEmpty();

        validator.RuleForEach(x => x.ProductTypeIds).ChildRules(cr1 =>
        {
            cr1.RuleFor(d => d).NotEqual(0).WithMessage(_ => "Product type Id must not be 0.");
        }).When(x => x.ProductTypeIds.Count != 0);


        validator.RuleFor(x => x.ProductExtensions).NotEmpty();

        validator.RuleForEach(x => x.ProductExtensions).ChildRules(cr1 =>
        {
            cr1.RuleFor(p => p.PCID).MaximumLength(15);
            cr1.RuleFor(a => a.PCID)
                .Must(pcid => pcid.All(char.IsLetterOrDigit) || pcid.Equals(Constants.NotAssigned))
                .WithMessage("PCID should only contain alphanumerical characters.");
            cr1.RuleFor(p => p.DosageFormId).NotEmpty();
            cr1.RuleFor(p => p.RouteOfAdministrationIds).NotEmpty();
            cr1.RuleForEach(p => p.RouteOfAdministrationIds).ChildRules(cr2 =>
            {
                cr2.RuleFor(r => r).NotEqual(0).WithMessage(_ => "Route of Administration Id must not be 0.");
            });
        }).When(x => x.ProductExtensions.Count != 0);
    }

    private static bool NotContainIllegalCharacters(string str)
    {
        return !Constants.ILLEGAL_CHARACTERS.Any(str.Contains);
    }
}
