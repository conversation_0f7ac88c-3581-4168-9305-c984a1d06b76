﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.ProductTypes;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetProductTypesListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductTypesApi api = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetProductTypesList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentProductTypesCount = dbContext.ProductTypes.Count();
        var productType1 = ProductTypeBuilder.Default().WithName(Fake.ProductType.Name).Build();
        var productType2 = ProductTypeBuilder.Default().WithName(Fake.ProductType.Name).Build();
        var productType3 = ProductTypeBuilder.Default().WithName(Fake.ProductType.Name).Build();
        await dbContext.ProductTypes.AddRangeAsync(productType1, productType2, productType3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await api.GetProductTypesListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentProductTypesCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(productType1.Name);
        responseNames.Should().Contain(productType2.Name);
        responseNames.Should().Contain(productType3.Name);

        dbContext.ProductTypes.RemoveRange(productType1, productType2, productType3);
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public Task DisposeAsync() => Task.CompletedTask;
}
