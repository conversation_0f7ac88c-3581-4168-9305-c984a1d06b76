﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.SubmissionTypes.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.SubmissionType;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.SubmissionTypes.ListQuery;

public class GetSubmissionTypeListQueryHandlerTests
{
    private readonly GetSubmissionTypeListQueryHandler handler;
    private readonly ISubmissionTypesRepository subsmissionTypeRepo;

    public GetSubmissionTypeListQueryHandlerTests()
    {
        subsmissionTypeRepo = Substitute.For<ISubmissionTypesRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new SubmissionTypeMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetSubmissionTypeListQueryHandler(subsmissionTypeRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<SubmissionType, SubmissionTypeBuilder>.Generate(3);

        subsmissionTypeRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetSubmissionTypeListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<SubmissionType, SubmissionTypeBuilder>.Generate(3);

        subsmissionTypeRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetSubmissionTypeListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }
}
