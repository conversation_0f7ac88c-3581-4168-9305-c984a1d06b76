﻿using MediatR;

namespace Axon.HAComms.Application.Commands.Tags.Create
{
    public class CreateTagCommandRequest : IRequest<CreateTagCommandResponse>
    {
        public string Name { get; }

        public string? Description { get; }

        public CreateTagCommandRequest(string name, string? description)
        {
            this.Name = name;
            this.Description = description;
        }
    }
}
