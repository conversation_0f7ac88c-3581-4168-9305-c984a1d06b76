﻿namespace Axon.HAComms.Domain.Constants;

public static class AuditEventCategory
{
    public const string SEARCH = "Search";
    public const string DRUG_PRODUCTS = "DrugProducts";
    public const string DRUG_SUBSTANCES = "DrugSubstances";
    public const string ROUTES_OF_ADMINISTRATION = "RoutesOfAdministration";
    public const string DOSAGE_FORMS = "DosageForms";
    public const string TAGS = "Tags";
    public const string COMMUNICATIONS = "Communications";
    public const string COMMENTS = "Comments";

    public static IReadOnlyCollection<string> All
        =>
        [
            SEARCH,
            DRUG_PRODUCTS,
            DRUG_SUBSTANCES,
            ROUTES_OF_ADMINISTRATION,
            DOSAGE_FORMS,
            TAGS,
            COMMUNICATIONS,
            COMMENTS,
        ];
}
