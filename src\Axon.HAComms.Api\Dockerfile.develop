#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0-buster-slim AS publish
WORKDIR /
COPY . .
WORKDIR "/src/Axon.HAComms.Api"
RUN dotnet build "Axon.HAComms.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Axon.HAComms.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Axon.HAComms.Api.dll"]