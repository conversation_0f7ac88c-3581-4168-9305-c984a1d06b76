﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Common.Builders;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Products;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateProductIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateProduct_UpdateNameAndStatus_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();

        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var productResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);

        //Act
        var newProductName = Fake.Product.Name;
        var newProductStatus = false;
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(newProductName)
            .WithIsActive(newProductStatus)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(newProductName);
        updatedProductResponse.IsActive.Should().Be(newProductStatus);
    }

    [Fact]
    public async Task UpdateProduct_ValidRequest_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.ProductTypes.Should().HaveCount(productTypeIds.Count);
        updatedProductResponse.DrugSubstances.Should().HaveCount(drugSubstances.Count);
        updatedProductResponse.DrugSubstances[0].Id.Should().Be(drugSubstanceIds[0]);
        updatedProductResponse.DrugSubstances[1].Id.Should().Be(drugSubstanceIds[1]);
        updatedProductResponse.DrugSubstances[2].Id.Should().Be(drugSubstanceIds[2]);

        updatedProductResponse.ProductExtensions.Should().HaveCount(1);
        var updatedProductExtension = updatedProductResponse.ProductExtensions.Single();
        updatedProductExtension.Pcid.Should().Be(productExtensionModel.Pcid);
        updatedProductExtension.DosageForm.Id.Should().Be(productExtensionModel.DosageFormId);
        updatedProductExtension.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension.RoutesOfAdministration.Single().Id.Should().Be(routeOfAdministration.Id);
    }

    [Fact]
    public async Task UpdateProduct_UpdateDrugSubstances_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).Take(5).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);
        var newDrugSubstanceIds = new List<int> { drugSubstanceIds[3], drugSubstanceIds[4] }.Concat(drugSubstances.Select(x => x.Id).Skip(5)).ToList();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(newDrugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.ProductTypes.Should().HaveCount(productTypeIds.Count);
        updatedProductResponse.DrugSubstances.Should().HaveCount(newDrugSubstanceIds.Count);
        updatedProductResponse.DrugSubstances[0].Id.Should().Be(newDrugSubstanceIds[0]);
        updatedProductResponse.DrugSubstances[1].Id.Should().Be(newDrugSubstanceIds[1]);
        updatedProductResponse.DrugSubstances[2].Id.Should().Be(newDrugSubstanceIds[2]);
        updatedProductResponse.DrugSubstances[3].Id.Should().Be(newDrugSubstanceIds[3]);
        updatedProductResponse.DrugSubstances[4].Id.Should().Be(newDrugSubstanceIds[4]);
        updatedProductResponse.DrugSubstances[5].Id.Should().Be(newDrugSubstanceIds[5]);
        updatedProductResponse.DrugSubstances[6].Id.Should().Be(newDrugSubstanceIds[6]);

        updatedProductResponse.DrugSubstances[0].Code.Should().Be(drugSubstances[3].Code);
        updatedProductResponse.DrugSubstances[1].Code.Should().Be(drugSubstances[4].Code);
        updatedProductResponse.DrugSubstances[2].Code.Should().Be(drugSubstances[5].Code);
        updatedProductResponse.DrugSubstances[3].Code.Should().Be(drugSubstances[6].Code);
        updatedProductResponse.DrugSubstances[4].Code.Should().Be(drugSubstances[7].Code);
        updatedProductResponse.DrugSubstances[5].Code.Should().Be(drugSubstances[8].Code);
        updatedProductResponse.DrugSubstances[6].Code.Should().Be(drugSubstances[9].Code);

        updatedProductResponse.DrugSubstances[0].Name.Should().Be(drugSubstances[3].Name);
        updatedProductResponse.DrugSubstances[1].Name.Should().Be(drugSubstances[4].Name);
        updatedProductResponse.DrugSubstances[2].Name.Should().Be(drugSubstances[5].Name);
        updatedProductResponse.DrugSubstances[3].Name.Should().Be(drugSubstances[6].Name);
        updatedProductResponse.DrugSubstances[4].Name.Should().Be(drugSubstances[7].Name);
        updatedProductResponse.DrugSubstances[5].Name.Should().Be(drugSubstances[8].Name);
        updatedProductResponse.DrugSubstances[6].Name.Should().Be(drugSubstances[9].Name);

        updatedProductResponse.DrugSubstances[0].Description.Should().Be(drugSubstances[3].Description);
        updatedProductResponse.DrugSubstances[1].Description.Should().Be(drugSubstances[4].Description);
        updatedProductResponse.DrugSubstances[2].Description.Should().Be(drugSubstances[5].Description);
        updatedProductResponse.DrugSubstances[3].Description.Should().Be(drugSubstances[6].Description);
        updatedProductResponse.DrugSubstances[4].Description.Should().Be(drugSubstances[7].Description);
        updatedProductResponse.DrugSubstances[5].Description.Should().Be(drugSubstances[8].Description);
        updatedProductResponse.DrugSubstances[6].Description.Should().Be(drugSubstances[9].Description);
    }

    [Fact]
    public async Task UpdateProduct_UpdateDrugSubstancesWithNone_ThrowsArgumentException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 5);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances([])
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Drug Substance Ids' must not be empty.");
    }

    [Fact]
    public async Task UpdateProduct_UpdateDrugSubstancesWithEmptyIds_ThrowsException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).Take(5).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);

        var rng = new Random();
        drugSubstanceIds.AddRange(new List<int>() { 0, 0, 0 });
        var shuffledDrugSubstanceIds = drugSubstanceIds.OrderBy(_ => rng.Next()).ToList();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(shuffledDrugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Drug substance Id must not be 0.");
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductExtensions_ReturnsOk()
    {
        /*
         * Scenario:
         * Create product with 2 product extensions
         * Update product to remove first extension, edit second extension and add 2 new extensions
         * Product should have 3 extensions
         */

        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductExtensions.Should().HaveCount(2);

        var productExtensionModel3 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[2].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[2].Id])
            .Build();
        var productExtensionModel4 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[3].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[3].Id])
            .Build();

        // Remove first extension and change remaining extension
        var updatedProductExtensions = new List<ProductExtensionModel> {
            new()
            {
                Id = productResponse.ProductExtensions[1].Id,
                Pcid = productResponse.ProductExtensions[1].Pcid,
                IsActive = productResponse.ProductExtensions[1].IsActive,
                DosageFormId = productResponse.ProductExtensions[1].DosageForm.Id,
                RouteOfAdministrationIds = productResponse.ProductExtensions[1].RoutesOfAdministration.Select(x => x.Id).ToList()
            }
        };
        var productExtensionModel5 = ProductExtensionSdkModelBuilder.Default()
            .WithId(updatedProductExtensions[0].Id)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[4].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[4].Id])
            .Build();
        updatedProductExtensions[0] = productExtensionModel5;

        updatedProductExtensions.AddRange(new List<ProductExtensionModel> { productExtensionModel3, productExtensionModel4 });

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions.ToArray()).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.DrugSubstances.Should().HaveCount(3);
        updatedProductResponse.ProductTypes.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions.Should().HaveCount(3);

        var updatedProductExtension1 = updatedProductResponse.ProductExtensions[0];
        updatedProductExtension1.Pcid.Should().Be(productExtensionModel5.Pcid);
        updatedProductExtension1.DosageForm.Id.Should().Be(productExtensionModel5.DosageFormId);
        updatedProductExtension1.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension1.RoutesOfAdministration.Single().Id.Should().Be(testRouteOfAdministrationEntities[4].Id);

        var updatedProductExtension2 = updatedProductResponse.ProductExtensions[1];
        updatedProductExtension2.Pcid.Should().Be(productExtensionModel3.Pcid);
        updatedProductExtension2.DosageForm.Id.Should().Be(productExtensionModel3.DosageFormId);
        updatedProductExtension2.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension2.RoutesOfAdministration.Single().Id.Should().Be(testRouteOfAdministrationEntities[2].Id);

        var updatedProductExtension3 = updatedProductResponse.ProductExtensions[2];
        updatedProductExtension3.Pcid.Should().Be(productExtensionModel4.Pcid);
        updatedProductExtension3.DosageForm.Id.Should().Be(productExtensionModel4.DosageFormId);
        updatedProductExtension3.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension3.RoutesOfAdministration.Single().Id.Should().Be(testRouteOfAdministrationEntities[3].Id);
    }

    [Fact]
    public async Task UpdateProduct_AddProductExtensions_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[2].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductExtensions.Should().HaveCount(2);

        // Add 2 new product extensions
        var productExtensionModel3 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[2].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[2].Id, testRouteOfAdministrationEntities[3].Id,
                testRouteOfAdministrationEntities[4].Id
            ])
            .Build();

        var productExtensionModel4 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[3].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id,
                testRouteOfAdministrationEntities[2].Id, testRouteOfAdministrationEntities[3].Id,
                testRouteOfAdministrationEntities[4].Id
            ])
            .Build();
        var updatedProductExtensions = new[] { productExtensionModel3, productExtensionModel4 };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.DrugSubstances.Should().HaveCount(2);
        updatedProductResponse.ProductTypes.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);

        var updatedProductExtension1 = updatedProductResponse.ProductExtensions[0];
        updatedProductExtension1.Pcid.Should().Be(productExtensionModel3.Pcid);
        updatedProductExtension1.DosageForm.Id.Should().Be(productExtensionModel3.DosageFormId);
        updatedProductExtension1.RoutesOfAdministration.Should().HaveCount(3);

        updatedProductExtension1.RoutesOfAdministration[0].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[0]);
        updatedProductExtension1.RoutesOfAdministration[1].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[1]);
        updatedProductExtension1.RoutesOfAdministration[2].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[2]);

        var updatedProductExtension2 = updatedProductResponse.ProductExtensions[1];
        updatedProductExtension2.Pcid.Should().Be(productExtensionModel4.Pcid);
        updatedProductExtension2.DosageForm.Id.Should().Be(productExtensionModel4.DosageFormId);
        updatedProductExtension2.RoutesOfAdministration.Should().HaveCount(5);
        updatedProductExtension2.RoutesOfAdministration[0].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[0]);
        updatedProductExtension2.RoutesOfAdministration[1].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[1]);
        updatedProductExtension2.RoutesOfAdministration[2].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[2]);
        updatedProductExtension2.RoutesOfAdministration[3].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[3]);
        updatedProductExtension2.RoutesOfAdministration[4].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[4]);
    }

    [Fact]
    public async Task UpdateProduct_UpdateDosageForm_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductExtensions.Should().HaveCount(2);

        // Update dosage forms
        var updatedDosageForm1 = DosageFormSdkModelBuilder.Default().WithId(testDosageForms[2].Id).WithName(testDosageForms[2].Name).Build();
        var updatedDosageForm2 = DosageFormSdkModelBuilder.Default().WithId(testDosageForms[3].Id).WithName(testDosageForms[3].Name).Build();
        productResponse.ProductExtensions[0].DosageForm.Id = testDosageForms[2].Id;
        productResponse.ProductExtensions[1].DosageForm.Id = testDosageForms[3].Id;

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithIsActive(productResponse.IsActive)
            .WithProductExtensions(productResponse.ProductExtensions.Select(ext =>
                new ProductExtensionModel()
                {
                    Id = ext.Id,
                    Pcid = ext.Pcid,
                    IsActive = ext.IsActive,
                    DosageFormId = ext.DosageForm.Id,
                    RouteOfAdministrationIds = ext.RoutesOfAdministration.Select(x => x.Id).ToList()
                }).ToArray()).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.DrugSubstances.Should().HaveCount(2);
        updatedProductResponse.ProductTypes.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);

        var updatedProductExtension1 = updatedProductResponse.ProductExtensions[0];
        updatedProductExtension1.Pcid.Should().Be(productExtensionModel1.Pcid);
        updatedProductExtension1.DosageForm.Id.Should().Be(updatedDosageForm1.Id);
        updatedProductExtension1.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension1.RoutesOfAdministration.Single().Id.Should().Be(testRouteOfAdministrationEntities[0].Id);

        var updatedProductExtension2 = updatedProductResponse.ProductExtensions[1];
        updatedProductExtension2.Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProductExtension2.DosageForm.Id.Should().Be(updatedDosageForm2.Id);
        updatedProductExtension2.RoutesOfAdministration.Should().HaveCount(1);
        updatedProductExtension2.RoutesOfAdministration.Single().Id.Should().Be(testRouteOfAdministrationEntities[1].Id);
    }

    [Fact]
    public async Task UpdateProduct_UpdateRouteOfAdministration_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[2].Id, testRouteOfAdministrationEntities[3].Id,
                testRouteOfAdministrationEntities[4].Id
            ])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductExtensions.Should().HaveCount(2);

        // Update routes of administration
        var productExtensionModel3 = ProductExtensionSdkModelBuilder.Default()
            .WithId(productResponse.ProductExtensions[0].Id)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[2].Id, testRouteOfAdministrationEntities[3].Id,
                testRouteOfAdministrationEntities[4].Id
            ])
            .Build();
        var productExtensionModel4 = ProductExtensionSdkModelBuilder.Default()
            .WithId(productResponse.ProductExtensions[1].Id)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[2].Id, testRouteOfAdministrationEntities[3].Id,
                testRouteOfAdministrationEntities[4].Id, testRouteOfAdministrationEntities[0].Id,
                testRouteOfAdministrationEntities[1].Id
            ])
            .Build();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel3, productExtensionModel4)
            .Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.DrugSubstances.Should().HaveCount(2);
        updatedProductResponse.ProductTypes.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);

        var updatedProductExtension1 = updatedProductResponse.ProductExtensions[0];
        updatedProductExtension1.Pcid.Should().Be(productExtensionModel3.Pcid);
        updatedProductExtension1.DosageForm.Id.Should().Be(productExtensionModel3.DosageFormId);
        updatedProductExtension1.RoutesOfAdministration.Should().HaveCount(3);

        updatedProductExtension1.RoutesOfAdministration[0].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[0]);
        updatedProductExtension1.RoutesOfAdministration[1].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[1]);
        updatedProductExtension1.RoutesOfAdministration[2].Id.Should().Be(productExtensionModel3.RouteOfAdministrationIds[2]);

        var updatedProductExtension2 = updatedProductResponse.ProductExtensions[1];
        updatedProductExtension2.Pcid.Should().Be(productExtensionModel4.Pcid);
        updatedProductExtension2.DosageForm.Id.Should().Be(productExtensionModel4.DosageFormId);
        updatedProductExtension2.RoutesOfAdministration.Should().HaveCount(5);

        updatedProductExtension2.RoutesOfAdministration[0].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[0]);
        updatedProductExtension2.RoutesOfAdministration[1].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[1]);
        updatedProductExtension2.RoutesOfAdministration[2].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[2]);
        updatedProductExtension2.RoutesOfAdministration[3].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[3]);
        updatedProductExtension2.RoutesOfAdministration[4].Id.Should().Be(productExtensionModel4.RouteOfAdministrationIds[4]);
    }

    [Fact]
    public async Task UpdateProduct_DuplicateRouteOfAdministration_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1)
            .Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductExtensions.Should().HaveCount(1);

        // Duplicate routes
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithId(productResponse.ProductExtensions[0].Id)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([
                testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id,
                testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id
            ])
            .Build();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel2)
            .Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.DrugSubstances.Should().HaveCount(2);
        updatedProductResponse.ProductTypes.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions.Should().HaveCount(1);

        var updatedProductExtension1 = updatedProductResponse.ProductExtensions[0];
        updatedProductExtension1.Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProductExtension1.DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);
        updatedProductExtension1.RoutesOfAdministration.Should().HaveCount(2);
        updatedProductExtension1.RoutesOfAdministration[0].Id.Should().Be(productExtensionModel2.RouteOfAdministrationIds[0]);
        updatedProductExtension1.RoutesOfAdministration[1].Id.Should().Be(productExtensionModel2.RouteOfAdministrationIds[1]);
    }

    [Fact]
    public async Task UpdateProduct_PassDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var productName1 = Fake.Product.Name;
        var productName2 = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;

        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        // Create product 1
        var createProductRequest1 = new CreateProductCommandRequestBuilder()
            .WithName(productName1)
            .WithIsActive(isActive)
            .WithDrugSubstances(drugSubstanceIds.Take(2).ToList())
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1).Build();
        var productResponse1 = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest1);

        // Create product 2
        var createProductRequest2 = new CreateProductCommandRequestBuilder()
            .WithName(productName2)
            .WithIsActive(isActive)
            .WithDrugSubstances(drugSubstanceIds.Skip(2).Take(2).ToList())
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel2).Build();
        await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest2);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse1.Id)
            .WithName(productName2)
            .WithIsActive(isActive)
            .WithDrugSubstances(drugSubstanceIds.Take(2).ToList())
            .WithProductTypes(productTypeIds.Take(2).ToList())
            .WithProductExtensions(productExtensionModel2).Build();
        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AlreadyExists", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_PassInvalidProductId_ThrowsEntityNotFoundException()
    {
        //Arrange
        var productId = Fake.Product.Id;
        var productName = Fake.Product.Name;
        var isActive = Fake.Product.IsActive;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        await ProductsTestEntitiesBuilder.Build(dbContext, 7);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productId)
            .WithName(productName)
            .WithIsActive(isActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("EntityNotFoundException", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_WithActiveProductExtensionsWithExactDuplicateCombinations_ReturnsOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routesOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(2);
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1).Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[1].Id])
            .Build();

        var updatedProductExtensions = new[]
        {
            new ProductExtensionModel()
            {
                Id = productResponse.ProductExtensions[0].Id,
                DosageFormId = productResponse.ProductExtensions[0].DosageForm.Id,
                IsActive = productResponse.ProductExtensions[0].IsActive,
                Pcid = productResponse.ProductExtensions[0].Pcid,
                RouteOfAdministrationIds = productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).ToList()
            },
            productExtensionModel2
        };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productResponse.Name)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions).Build();

        var updateProductResponse = await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        updateProductResponse.Should().NotBeNull();

        var updatedProduct = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);
        //Assert
        updatedProduct.Should().NotBeNull();
        updatedProduct.Name.Should().Be(updateProductRequest.Name);
        updatedProduct.DrugSubstances.Should().HaveCount(drugSubstanceIds.Count);
        updatedProduct.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);

        updatedProduct.ProductExtensions.Should().HaveCount(2);
        updatedProduct.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        updatedProduct.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel1.RouteOfAdministrationIds);
        updatedProduct.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        updatedProduct.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProduct.ProductExtensions[1].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel2.RouteOfAdministrationIds);
        updatedProduct.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);
    }

    [Fact]
    public async Task UpdateProduct_WithActiveProductExtensionsWithSomeDuplicateRoutesOfAdministration_ReturnsOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var routesOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(3);

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1).Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[2].Id])
            .Build();

        var updatedProductExtensions = new[]
        {
            new ProductExtensionModel()
            {
                Id = productResponse.ProductExtensions[0].Id,
                DosageFormId = productResponse.ProductExtensions[0].DosageForm.Id,
                IsActive = productResponse.ProductExtensions[0].IsActive,
                Pcid = productResponse.ProductExtensions[0].Pcid,
                RouteOfAdministrationIds = productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).ToList()
            },
            productExtensionModel2
        };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productResponse.Name)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions).Build();

        var updateProductResponse = await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        updateProductResponse.Should().NotBeNull();

        var updatedProduct = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);
        //Assert
        updatedProduct.Should().NotBeNull();
        updatedProduct.Name.Should().Be(updateProductRequest.Name);
        updatedProduct.DrugSubstances.Should().HaveCount(drugSubstanceIds.Count);
        updatedProduct.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);

        updatedProduct.ProductExtensions.Should().HaveCount(2);
        updatedProduct.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        updatedProduct.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel1.RouteOfAdministrationIds);
        updatedProduct.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        updatedProduct.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProduct.ProductExtensions[1].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel2.RouteOfAdministrationIds);
        updatedProduct.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);
    }

    [Fact]
    public async Task UpdateProduct_WithActiveProductExtensionsWithDuplicateRoutesOfAdministrationInSingleProductExtension_ReturnOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routesOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(2);
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[0].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[1].Id])
            .Build();

        var updatedProductExtensions = new[]
        {
            new ProductExtensionModel()
            {
                Id = productResponse.ProductExtensions[0].Id,
                DosageFormId = productResponse.ProductExtensions[0].DosageForm.Id,
                IsActive = productResponse.ProductExtensions[0].IsActive,
                Pcid = productResponse.ProductExtensions[0].Pcid,
                RouteOfAdministrationIds = productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).ToList()
            },
            productExtensionModel2
        };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productResponse.Name)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productResponse.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().BeTrue();
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(dosageForm.Id);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(1);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(routesOfAdministrationEntities[0].Id);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().BeTrue();
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(dosageForm.Id);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(1);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().Be(routesOfAdministrationEntities[1].Id);
    }

    [Fact]
    public async Task UpdateProduct_WithInActiveProductExtensionsWithExactDuplicateCombinations_ReturnOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var routesOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(2);

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[0].Id, routesOfAdministrationEntities[1].Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithIsActive(true)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routesOfAdministrationEntities[1].Id, routesOfAdministrationEntities[0].Id])
            .Build();

        var updatedProductExtensions = new[]
        {
            new ProductExtensionModel()
            {
                Id = productResponse.ProductExtensions[0].Id,
                DosageFormId = productResponse.ProductExtensions[0].DosageForm.Id,
                IsActive = false,
                Pcid = productResponse.ProductExtensions[0].Pcid,
                RouteOfAdministrationIds = productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).ToList()
            },
            productExtensionModel2
        };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productResponse.Name)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(updatedProductExtensions).Build();

        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productResponse.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().BeFalse();
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(dosageForm.Id);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(updatedProductExtensions[0].RouteOfAdministrationIds[0]);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().Be(updatedProductExtensions[0].RouteOfAdministrationIds[1]);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().BeTrue();
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(dosageForm.Id);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().Be(updatedProductExtensions[0].RouteOfAdministrationIds[0]);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[1].Id.Should().Be(updatedProductExtensions[0].RouteOfAdministrationIds[1]);
    }

    [Fact]
    public async Task UpdateProduct_DrugSubstancesWithAssociatedComment_ThrowsAssociationExistsException()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = createdProduct.ProductExtensions[0].Id, RouteOfAdministrationIds = createdProduct.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).Skip(1).Take(1).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).Skip(1).Take(1).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.DosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.RoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_ProductExtensionRoutesOfAdministrationWithAssociatedComment_ThrowsAssociationExistsException()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomRoutesOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntities(3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = createdProduct.ProductExtensions[0].Id, RouteOfAdministrationIds = createdProduct.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.DosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = randomRoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_ProductExtensionRoutesOfAdministrationWithoutAssociatedComment_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = createdProduct.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomRoutesOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntities(3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.DosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.Id == createdProduct.ProductExtensions[0].Id ? pe.RoutesOfAdministration.Select(r => r.Id).ToList() : randomRoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id
            }).ToArray()).Build();

        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        var updatedProductResponse = await productApi.GetProductAsync(createdProduct.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(updateProductRequest.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(updateProductRequest.ProductExtensions[0].Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().Be(updateProductRequest.ProductExtensions[0].IsActive);
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[0].DosageFormId);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(updateProductRequest.ProductExtensions[1].Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().Be(updateProductRequest.ProductExtensions[1].IsActive);
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[1].DosageFormId);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[2].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
    }

    [Fact]
    public async Task UpdateProduct_ProductExtensionDosageFormWithoutAssociatedComment_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = createdProduct.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomDosageForm = await dbContext.DosageForms.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.Id == createdProduct.ProductExtensions[0].Id ? pe.DosageForm.Id : randomDosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.RoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(createdProduct.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(updateProductRequest.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(updateProductRequest.ProductExtensions[0].Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().Be(updateProductRequest.ProductExtensions[0].IsActive);
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[0].DosageFormId);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(updateProductRequest.ProductExtensions[1].Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().Be(updateProductRequest.ProductExtensions[1].IsActive);
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[1].DosageFormId);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
    }

    [Fact]
    public async Task UpdateProduct_ProductExtensionRoutesOfAdministrationAndDosageFormWithoutAssociatedComment_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = createdProduct.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomRoutesOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntities(3);
        var randomDosageForm = await dbContext.DosageForms.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.Id == createdProduct.ProductExtensions[0].Id ? pe.DosageForm.Id : randomDosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.Id == createdProduct.ProductExtensions[0].Id ? pe.RoutesOfAdministration.Select(r => r.Id).ToList() : randomRoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        var updatedProductResponse = await productApi.GetProductAsync(createdProduct.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(updateProductRequest.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(updateProductRequest.ProductExtensions[0].Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().Be(updateProductRequest.ProductExtensions[0].IsActive);
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[0].DosageFormId);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(updateProductRequest.ProductExtensions[1].Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().Be(updateProductRequest.ProductExtensions[1].IsActive);
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[1].DosageFormId);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[2].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
    }

    [Fact]
    public async Task UpdateProduct_ProductExtensionDosageFormWithAssociatedComment_ThrowsAssociationExistsException()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = createdProduct.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomDosageForm = await dbContext.DosageForms.FirstAsync(d => d.Id != createdProduct.ProductExtensions[0].DosageForm.Id);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = randomDosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.RoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_DeleteProductExtensionWithAssociatedComment_ThrowsAssociationExistsException()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = createdProduct.ProductExtensions[0].Id, RouteOfAdministrationIds = createdProduct.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(createdProduct.ProductExtensions.Skip(1).Select(pe => new ProductExtensionModel()
            {
                Id = pe.Id,
                DosageFormId = pe.DosageForm.Id,
                IsActive = pe.IsActive,
                Pcid = pe.Pcid,
                RouteOfAdministrationIds = pe.RoutesOfAdministration.Select(r => r.Id).ToList(),
                ProductId = createdProduct.Id,
            }).ToArray()).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task UpdateProduct_AddProductExtension_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var createdProduct = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var randomDosageForm = await dbContext.DosageForms.GetRandomEntity();
        var randomRoutesOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntities(3);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = createdProduct.ProductExtensions[0].Id, RouteOfAdministrationIds = createdProduct.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(createdProduct.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        Assert.NotNull(createResponse);

        createdProduct.DrugSubstances.Should().HaveCount(3);

        var productExtensions = createdProduct.ProductExtensions.Select(pe => new ProductExtensionModel()
        {
            Id = pe.Id,
            DosageFormId = pe.DosageForm.Id,
            IsActive = pe.IsActive,
            Pcid = pe.Pcid,
            RouteOfAdministrationIds = pe.RoutesOfAdministration.Select(r => r.Id).ToList(),
            ProductId = createdProduct.Id,
        }).ToList();

            productExtensions.Add(new ProductExtensionModel()
            {
            DosageFormId = randomDosageForm.Id,
            IsActive = true,
            Pcid = Fake.ProductExtension.PCID,
            ProductId = createdProduct.Id,
            RouteOfAdministrationIds = randomRoutesOfAdministration.Select(r => r.Id).ToList()
        });

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(createdProduct.Id)
            .WithName(createdProduct.Name)
            .WithIsActive(createdProduct.IsActive)
            .WithDrugSubstances(createdProduct.DrugSubstances.Select(s => s.Id).ToList())
            .WithProductTypes(createdProduct.ProductTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(productExtensions.ToArray()).Build();

        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(createdProduct.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(updateProductRequest.Name);
        updatedProductResponse.ProductExtensions.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions[0].Pcid.Should().Be(updateProductRequest.ProductExtensions[0].Pcid);
        updatedProductResponse.ProductExtensions[0].IsActive.Should().Be(updateProductRequest.ProductExtensions[0].IsActive);
        updatedProductResponse.ProductExtensions[0].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[0].DosageFormId);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[0].RouteOfAdministrationIds);

        updatedProductResponse.ProductExtensions[1].Pcid.Should().Be(updateProductRequest.ProductExtensions[1].Pcid);
        updatedProductResponse.ProductExtensions[1].IsActive.Should().Be(updateProductRequest.ProductExtensions[1].IsActive);
        updatedProductResponse.ProductExtensions[1].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[1].DosageFormId);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(2);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[1].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[1].RouteOfAdministrationIds);

        updatedProductResponse.ProductExtensions[2].Pcid.Should().Be(updateProductRequest.ProductExtensions[2].Pcid);
        updatedProductResponse.ProductExtensions[2].IsActive.Should().Be(updateProductRequest.ProductExtensions[2].IsActive);
        updatedProductResponse.ProductExtensions[2].DosageForm.Id.Should().Be(updateProductRequest.ProductExtensions[2].DosageFormId);
        updatedProductResponse.ProductExtensions[2].RoutesOfAdministration.Should().HaveCount(3);
        updatedProductResponse.ProductExtensions[2].RoutesOfAdministration[0].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[2].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[2].RoutesOfAdministration[1].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[2].RouteOfAdministrationIds);
        updatedProductResponse.ProductExtensions[2].RoutesOfAdministration[2].Id.Should().BeOneOf(updateProductRequest.ProductExtensions[2].RouteOfAdministrationIds);
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductTypesWithoutAssociatedComments_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).Take(3).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductTypes.Should().HaveCount(3);
        var newProductTypeIds = new List<int> { productTypeIds[0], productTypeIds[1] }.Concat(productTypes.Select(x => x.Id).Skip(4)).ToList();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(newProductTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var updateProductCommandResponse = await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.ProductTypes.Should().HaveCount(newProductTypeIds.Count);
        updatedProductResponse.ProductTypes[0].Id.Should().Be(newProductTypeIds[0]);
        updatedProductResponse.ProductTypes[1].Id.Should().Be(newProductTypeIds[1]);
        updatedProductResponse.ProductTypes[2].Id.Should().Be(newProductTypeIds[2]);

        updatedProductResponse.ProductTypes[0].Name.Should().Be(productTypes[0].Name);
        updatedProductResponse.ProductTypes[1].Name.Should().Be(productTypes[1].Name);
        updatedProductResponse.ProductTypes[2].Name.Should().Be(productTypes[4].Name);

        updateProductCommandResponse.Id.Should().Be(productResponse.Id);
        updateProductCommandResponse.UpdatedCommentsCount.Should().Be(0);
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductTypesWithAssociatedComments_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration1 = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var routeOfAdministration2 = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).Take(3).ToList();
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration1.Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration2.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductTypes.Should().HaveCount(3);

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productResponse.ProductExtensions[0].Id, RouteOfAdministrationIds = productResponse.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(productResponse.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(createCommunicationResponse.Id)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productResponse.ProductExtensions[1].Id, RouteOfAdministrationIds = productResponse.ProductExtensions[1].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(productResponse.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Take(2).Select(x => x.Id).ToArray()).Build();
        await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        var newProductTypeIds = new List<int> { productTypeIds[0], productTypeIds[1] }.Concat(productTypes.Select(x => x.Id).Skip(4)).ToList();

        productExtensionModel1.Id = productResponse.ProductExtensions[0].Id;
        productExtensionModel2.Id = productResponse.ProductExtensions[1].Id;

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(newProductTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();
        var updateProductCommandResponse = await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.ProductTypes.Should().HaveCount(newProductTypeIds.Count);
        updatedProductResponse.ProductTypes[0].Id.Should().Be(newProductTypeIds[0]);
        updatedProductResponse.ProductTypes[1].Id.Should().Be(newProductTypeIds[1]);
        updatedProductResponse.ProductTypes[2].Id.Should().Be(newProductTypeIds[2]);

        updatedProductResponse.ProductTypes[0].Name.Should().Be(productTypes[0].Name);
        updatedProductResponse.ProductTypes[1].Name.Should().Be(productTypes[1].Name);
        updatedProductResponse.ProductTypes[2].Name.Should().Be(productTypes[4].Name);

        updateProductCommandResponse.UpdatedCommentsCount.Should().Be(2);
        updateProductCommandResponse.Id.Should().Be(productResponse.Id);
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductTypesWithNone_ThrowsArgumentException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 5);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes([])
            .WithProductExtensions(productExtensionModel).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Product Type Ids' must not be empty.");
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductTypesWithEmptyIds_ThrowsException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).Take(5).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);

        Random rng = new();
        productTypeIds.AddRange([0, 0]);
        var shuffledProductTypeIds = productTypeIds.OrderBy(_ => rng.Next()).ToList();

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(shuffledProductTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Product type Id must not be 0.");
    }

    [Fact]
    public async Task UpdateProduct_UpdateWithNotCategorizedProductTypes_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).Take(3).ToList();
        var notCategorizedProductType = ApiTestHelper.NotCategorizedProductType(dbContext);
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.ProductTypes.Should().HaveCount(3);
        var newProductTypeIds = new List<int> { notCategorizedProductType.Id };

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(newProductTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        await productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);
        var updatedProductResponse = await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        updatedProductResponse.Should().NotBeNull();
        updatedProductResponse.Name.Should().Be(productName);
        updatedProductResponse.ProductTypes.Should().HaveCount(newProductTypeIds.Count);
        updatedProductResponse.ProductTypes[0].Id.Should().Be(newProductTypeIds[0]);
        updatedProductResponse.ProductTypes[0].Name.Should().Be(notCategorizedProductType.Name);
    }

    [Fact]
    public async Task UpdateProduct_UpdateProductTypesWithInvalidCombinationOfIds_ThrowsException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 10);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).Take(5).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var notCategorizedProductType = ApiTestHelper.NotCategorizedProductType(dbContext);
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        productResponse.DrugSubstances.Should().HaveCount(5);

        productTypeIds.Add(notCategorizedProductType.Id);

        //Act
        var updateProductRequest = new UpdateProductCommandRequestBuilder()
            .WithId(productResponse.Id)
            .WithName(productName)
            .WithIsActive(productResponse.IsActive)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var updateProductResponse = () => productApi.UpdateProductAsync(TenantConstants.DEFAULT_TENANT, updateProductRequest);

        //Assert
        var exception = await updateProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity with \\\"ProductTypeIds\\\" = ({string.Join(',', productTypeIds)}) is not a valid combination.");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Comments.Clear();
        dbContext.Communications.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DrugSubstances.Clear();
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        dbContext.Tags.Clear();
        await dbContext.SaveChangesAsync();
        dbContext.ChangeTracker.Clear();
    }
}
