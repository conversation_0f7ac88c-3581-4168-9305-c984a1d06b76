﻿using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Exceptions;

namespace Axon.HAComms.Application.Helpers;

public static class ProductsHelper
{
    public static async Task ValidateProductTypeCombinationAsync(IProductTypesRepository productTypesRepo, IList<int> productTypeIds)
    {
        var notCategorizedProductType = await productTypesRepo.GetItemByFilterAsync(x => x.Name.ToLower().Equals(Constants.NotCategorized.ToLower()));
        if (notCategorizedProductType != null && productTypeIds.Count > 1 && productTypeIds.Contains(notCategorizedProductType.Id))
        {
            throw new InvalidCombinationException("ProductTypeIds", string.Join(',', productTypeIds));
        }
    }
}
