﻿begin transaction;

Update DrugSubstances set Name = N'Leptospira interrogans,serogroup icterohaemorrhagi' where Code = '9015'
Update DrugSubstances set Name = N'cefacetrile sodium' where Code = '9012'
Update DrugSubstances set Name = N'oxytetracycline dihydrate' where Code = '9011'
Update DrugSubstances set Name = N'amoxicillin trihydrate' where Code = '8983'
Update DrugSubstances set Name = N'newcastle disease virus' where Code = '8941'
Update DrugSubstances set Name = N'phenylephrine' where Code = '8847'
Update DrugSubstances set Name = N'prednisolone' where Code = '8826'
Update DrugSubstances set Name = N'pimobendan' where Code = '8795'
Update DrugSubstances set Name = N'sulfadiazine' where Code = '8774'
Update DrugSubstances set Name = N'Haemophilus parasuis' where Code = '8763'
Update DrugSubstances set Name = N'dl-methionine' where Code = '8615'
Update DrugSubstances set Name = N'hyocine' where Code = '8570'
Update DrugSubstances set Name = N'tipranavir' where Code = '8380'
Update DrugSubstances set Name = N'inclusion body hepatitis virus' where Code = '8377'
Update DrugSubstances set Name = N'Canine coronavirus' where Code = '8370'
Update DrugSubstances set Name = N'Canine adenovirus Type 1,Cornell' where Code = '8369'
Update DrugSubstances set Name = N'streptococcus pyogenes' where Code = '8368'
Update DrugSubstances set Name = N'salmonella schottmuelleri' where Code = '8367'
Update DrugSubstances set Name = N'croscarmellose sodium' where Code = '8340'
Update DrugSubstances set Name = N'dl-tryptophan' where Code = '8282'
Update DrugSubstances set Name = N'vervain herb' where Code = '8123'
Update DrugSubstances set Name = N'common sorrel, herb' where Code = '8122'
Update DrugSubstances set Name = N'pseudoephedrine sulphate' where Code = '8039'
Update DrugSubstances set Name = N'tristearin' where Code = '8038'
Update DrugSubstances set Name = N'Feline herpesvirus (Feline rhinotracheitis virus)' where Code = '8028'
Update DrugSubstances set Name = N'Leptospira bratislava,JEZ' where Code = '8027'
Update DrugSubstances set Name = N'erav' where Code = '8022'
Update DrugSubstances set Name = N'Lawsonia intracellularis' where Code = '8021'
Update DrugSubstances set Name = N'Leptospira hardjo' where Code = '8017'
Update DrugSubstances set Name = N'Leptospira kirschneri,serogroup grippotyphosa' where Code = '8016'
Update DrugSubstances set Name = N'glucagon' where Code = '8000'
Update DrugSubstances set Name = N'Escherichia coli' where Code = '7997'
Update DrugSubstances set Name = N'Salmonella choleraesuis' where Code = '7994'
Update DrugSubstances set Name = N'rabies virus' where Code = '7993'
Update DrugSubstances set Name = N'infectious bronchitis virus' where Code = '7989'
Update DrugSubstances set Name = N'clofenvinfos' where Code = '7982'
Update DrugSubstances set Name = N'povidone iodine' where Code = '7975'
Update DrugSubstances set Name = N'canine parainfluenza virus' where Code = '7961'
Update DrugSubstances set Name = N'avian encephalomyelitis virus, calnek' where Code = '7938'
Update DrugSubstances set Name = N'bromhexine' where Code = '7921'
Update DrugSubstances set Name = N'clhaemolyticum' where Code = '7918'
Update DrugSubstances set Name = N'porcine parvovirus' where Code = '7909'
Update DrugSubstances set Name = N'Pasteurella multocida' where Code = '7904'
Update DrugSubstances set Name = N'Erysipelothrix rhusiopathiae' where Code = '7900'
Update DrugSubstances set Name = N'propofol' where Code = '7879'
Update DrugSubstances set Name = N'tiamulin fumarate' where Code = '7877'
Update DrugSubstances set Name = N'bunazosin hydrochloride' where Code = '7876'
Update DrugSubstances set Name = N'lysine' where Code = '7875'
Update DrugSubstances set Name = N'lysine hydrochloride' where Code = '7874'
Update DrugSubstances set Name = N'miconazole' where Code = '7871'
Update DrugSubstances set Name = N'tylosin' where Code = '7870'
Update DrugSubstances set Name = N'arginine' where Code = '7864'
Update DrugSubstances set Name = N'phenylbutazone calcium dihydrate' where Code = '7862'
Update DrugSubstances set Name = N'menadione' where Code = '7859'
Update DrugSubstances set Name = N'phenoxymethylpenicillin' where Code = '7857'
Update DrugSubstances set Name = N'benethamine penicillin' where Code = '7856'
Update DrugSubstances set Name = N'benzathine benzylpenicillin' where Code = '7854'
Update DrugSubstances set Name = N'benzylpenicillin potassium' where Code = '7853'
Update DrugSubstances set Name = N'estradiol benzoate' where Code = '7852'
Update DrugSubstances set Name = N'estradiol enanthate' where Code = '7851'
Update DrugSubstances set Name = N'dexamethasone_hp' where Code = '7847'
Update DrugSubstances set Name = N'clonidine' where Code = '7842'
Update DrugSubstances set Name = N'ampicillin sodium' where Code = '7838'
Update DrugSubstances set Name = N'sulfathiazole' where Code = '7837'
Update DrugSubstances set Name = N'framycetin sulphate' where Code = '7834'
Update DrugSubstances set Name = N'chlortetracycline' where Code = '7818'
Update DrugSubstances set Name = N'hydrocortisone butyrate' where Code = '7801'
Update DrugSubstances set Name = N'thiamine hydrochloride' where Code = '7794'
Update DrugSubstances set Name = N'ketamine hydrochloride' where Code = '7777'
Update DrugSubstances set Name = N'tenecteplase' where Code = '7776'
Update DrugSubstances set Name = N'miripirium chloride' where Code = '7758'
Update DrugSubstances set Name = N'clarithromycin' where Code = '7748'
Update DrugSubstances set Name = N'methylprednisolone' where Code = '7687'
Update DrugSubstances set Name = N'flibanserin' where Code = '7550'
Update DrugSubstances set Name = N'taurolidine' where Code = '7505'
Update DrugSubstances set Name = N'tamsulosin hydrochloride' where Code = '7504'
Update DrugSubstances set Name = N'sucralfate' where Code = '7490'
Update DrugSubstances set Name = N'ramifenazone' where Code = '7477'
Update DrugSubstances set Name = N'omeprazole_hp' where Code = '7470'
Update DrugSubstances set Name = N'nimesulide' where Code = '7469'
Update DrugSubstances set Name = N'nystatin' where Code = '7465'
Update DrugSubstances set Name = N'tasonermin' where Code = '7463'
Update DrugSubstances set Name = N'oxytocin' where Code = '7458'
Update DrugSubstances set Name = N'paromomycin sulphate' where Code = '7456'
Update DrugSubstances set Name = N'primidone' where Code = '7435'
Update DrugSubstances set Name = N'progesterone' where Code = '7434'
Update DrugSubstances set Name = N'naphazoline hydrochloride' where Code = '7427'
Update DrugSubstances set Name = N'miconazole nitrate' where Code = '7412'
Update DrugSubstances set Name = N'metformin hydrochloride' where Code = '7408'
Update DrugSubstances set Name = N'mebendazole' where Code = '7398'
Update DrugSubstances set Name = N'levamisole hydrochloride' where Code = '7392'
Update DrugSubstances set Name = N'josamycin' where Code = '7387'
Update DrugSubstances set Name = N'interferon gamma-1b' where Code = '7384'
Update DrugSubstances set Name = N'tiotropium bromide monohydrate' where Code = '7376'
Update DrugSubstances set Name = N'fenoxazoline hydrochloride' where Code = '7354'
Update DrugSubstances set Name = N'griseofulvin' where Code = '7328'
Update DrugSubstances set Name = N'diazinon' where Code = '7320'
Update DrugSubstances set Name = N'flubendazole' where Code = '7316'
Update DrugSubstances set Name = N'furosemide' where Code = '7299'
Update DrugSubstances set Name = N'cresol' where Code = '7291'
Update DrugSubstances set Name = N'vine leaf extract' where Code = '7271'
Update DrugSubstances set Name = N'guaifenesin' where Code = '7249'
Update DrugSubstances set Name = N'menbutone' where Code = '7217'
Update DrugSubstances set Name = N'sodium thiosulphate' where Code = '7191'
Update DrugSubstances set Name = N'thimerosal' where Code = '7150'
Update DrugSubstances set Name = N'romifidine hydrochloride' where Code = '7148'
Update DrugSubstances set Name = N'simvastatin' where Code = '7143'
Update DrugSubstances set Name = N'nonivamide' where Code = '7122'
Update DrugSubstances set Name = N'coconut oil' where Code = '7049'
Update DrugSubstances set Name = N'dihydrostreptomycin' where Code = '7039'
Update DrugSubstances set Name = N'diethylcarbamazine citrate' where Code = '7031'
Update DrugSubstances set Name = N'xylazine' where Code = '7009'
Update DrugSubstances set Name = N'deslorelin' where Code = '7001'
Update DrugSubstances set Name = N'diclazuril' where Code = '6994'
Update DrugSubstances set Name = N'dextran' where Code = '6992'
Update DrugSubstances set Name = N'cythioate' where Code = '6990'
Update DrugSubstances set Name = N'cimaterol' where Code = '6947'
Update DrugSubstances set Name = N'chlorobutanol' where Code = '6946'
Update DrugSubstances set Name = N'cefadroxil' where Code = '6943'
Update DrugSubstances set Name = N'cimetropium bromide' where Code = '6941'
Update DrugSubstances set Name = N'budesonide' where Code = '6926'
Update DrugSubstances set Name = N'carnidazole' where Code = '6924'
Update DrugSubstances set Name = N'bacitracin zinc' where Code = '6912'
Update DrugSubstances set Name = N'dichlorvos' where Code = '6907'
Update DrugSubstances set Name = N'cabergoline' where Code = '6892'
Update DrugSubstances set Name = N'telmisartan' where Code = '6875'
Update DrugSubstances set Name = N'cloxacillin benzathine' where Code = '6863'
Update DrugSubstances set Name = N'cloxacillin sodium' where Code = '6862'
Update DrugSubstances set Name = N'talipexole dihydrochloride' where Code = '6851'
Update DrugSubstances set Name = N'pramipexole dihydrochloride monohydrate' where Code = '6833'
Update DrugSubstances set Name = N'insulin human' where Code = '6812'
Update DrugSubstances set Name = N'zinc sulphate monohydrate' where Code = '6809'
Update DrugSubstances set Name = N'biotin' where Code = '6792'
Update DrugSubstances set Name = N'eleutherococcus extract' where Code = '6777'
Update DrugSubstances set Name = N'doxycycline hyclate' where Code = '6774'
Update DrugSubstances set Name = N'docusate sodium' where Code = '6773'
Update DrugSubstances set Name = N'rosemary' where Code = '6755'
Update DrugSubstances set Name = N'alum potassium anhydrous' where Code = '6699'
Update DrugSubstances set Name = N'nicoboxil' where Code = '6685'
Update DrugSubstances set Name = N'tiamulin' where Code = '6677'
Update DrugSubstances set Name = N'vetrabutine hydrochloride' where Code = '6676'
Update DrugSubstances set Name = N'choline chloride' where Code = '6672'
Update DrugSubstances set Name = N'lacidipine' where Code = '6660'
Update DrugSubstances set Name = N'metamizole magnesium' where Code = '6659'
Update DrugSubstances set Name = N'chlorocresol' where Code = '6658'
Update DrugSubstances set Name = N'nevirapine' where Code = '6654'
Update DrugSubstances set Name = N'sorbitan oleate' where Code = '6648'
Update DrugSubstances set Name = N'pirenzepine dihydrochloride monohydrate' where Code = '6645'
Update DrugSubstances set Name = N'benzethonium chloride' where Code = '6642'
Update DrugSubstances set Name = N'lansoprazole' where Code = '6639'
Update DrugSubstances set Name = N'isosorbide mononitrate' where Code = '6637'
Update DrugSubstances set Name = N'nitrofurantoin' where Code = '6636'
Update DrugSubstances set Name = N'enilconazole' where Code = '6633'
Update DrugSubstances set Name = N'epinastine hydrochloride' where Code = '6632'
Update DrugSubstances set Name = N'menthol' where Code = '6608'
Update DrugSubstances set Name = N'sorbitan trioleate' where Code = '6606'
Update DrugSubstances set Name = N'amphotericin b' where Code = '6601'
Update DrugSubstances set Name = N'amantadine hydrochloride' where Code = '6594'
Update DrugSubstances set Name = N'alteplase' where Code = '6589'
Update DrugSubstances set Name = N'alfaprostol' where Code = '6588'
Update DrugSubstances set Name = N'triamcinolone acetonide' where Code = '6579'
Update DrugSubstances set Name = N'prednisolone acetate' where Code = '6573'
Update DrugSubstances set Name = N'nicotinic acid' where Code = '6565'
Update DrugSubstances set Name = N'medroxyprogesterone acetate' where Code = '6561'
Update DrugSubstances set Name = N'heparin sodium' where Code = '6550'
Update DrugSubstances set Name = N'sodium fusidate' where Code = '6548'
Update DrugSubstances set Name = N'ademetionine' where Code = '6521'
Update DrugSubstances set Name = N'aciclovir' where Code = '6518'
Update DrugSubstances set Name = N'acepromazine maleate' where Code = '6513'
Update DrugSubstances set Name = N'carboxymethylcellulose sodium' where Code = '6503'
Update DrugSubstances set Name = N'diclofenac sodium' where Code = '6501'
Update DrugSubstances set Name = N'ergocalciferol' where Code = '6480'
Update DrugSubstances set Name = N'triacetin' where Code = '6465'
Update DrugSubstances set Name = N'azaperone' where Code = '6452'
Update DrugSubstances set Name = N'algestone acetophenide' where Code = '6447'
Update DrugSubstances set Name = N'threonine' where Code = '6446'
Update DrugSubstances set Name = N'pyridoxine hydrochloride' where Code = '6445'
Update DrugSubstances set Name = N'l-methionine' where Code = '6444'
Update DrugSubstances set Name = N'arginine hydrochloride' where Code = '6443'
Update DrugSubstances set Name = N'l-histidine monohydrochloride' where Code = '6441'
Update DrugSubstances set Name = N'sulfamethoxazole' where Code = '6436'
Update DrugSubstances set Name = N'prothipendyl' where Code = '6419'
Update DrugSubstances set Name = N'polysorbate 80' where Code = '6415'
Update DrugSubstances set Name = N'hypromellose' where Code = '6412'
Update DrugSubstances set Name = N'hydroxypropylcellulose' where Code = '6411'
Update DrugSubstances set Name = N'ibuprofen' where Code = '6408'
Update DrugSubstances set Name = N'piroxicam' where Code = '6405'
Update DrugSubstances set Name = N'cefalexin monohydrate' where Code = '6404'
Update DrugSubstances set Name = N'trimethoprim' where Code = '6402'
Update DrugSubstances set Name = N'dexpanthenol' where Code = '6401'
Update DrugSubstances set Name = N'sulfamethoxypyridazine' where Code = '6395'
Update DrugSubstances set Name = N'phenylbutazone_hp' where Code = '6393'
Update DrugSubstances set Name = N'tylosin tartrate' where Code = '6390'
Update DrugSubstances set Name = N'oxymetazoline hydrochloride' where Code = '6389'
Update DrugSubstances set Name = N'diltiazem hydrochloride' where Code = '6388'
Update DrugSubstances set Name = N'glutamic acid' where Code = '6386'
Update DrugSubstances set Name = N'cysteine hydrochloride anhydrous' where Code = '6381'
Update DrugSubstances set Name = N'prolintane hydrochloride' where Code = '6366'
Update DrugSubstances set Name = N'kanamycin monosulphate' where Code = '6365'
Update DrugSubstances set Name = N'nicotinamide' where Code = '6359'
Update DrugSubstances set Name = N'vitamin a' where Code = '6357'
Update DrugSubstances set Name = N'cryofluorane' where Code = '6352'
Update DrugSubstances set Name = N'thiamine' where Code = '6346'
Update DrugSubstances set Name = N'chloramphenicol' where Code = '6342'
Update DrugSubstances set Name = N'cellacefate' where Code = '6334'
Update DrugSubstances set Name = N'metamizole sodium  monohydrate' where Code = '6329'
Update DrugSubstances set Name = N'chlortetracycline hydrochloride' where Code = '6327'
Update DrugSubstances set Name = N'carprofen' where Code = '6323'
Update DrugSubstances set Name = N'furazolidone_hp' where Code = '6320'
Update DrugSubstances set Name = N'tramazoline hydrochloride monohydrate' where Code = '6317'
Update DrugSubstances set Name = N'calcium pantothenate' where Code = '6301'
Update DrugSubstances set Name = N'bendroflumethiazide' where Code = '6290'
Update DrugSubstances set Name = N'clonidine hydrochloride' where Code = '6281'
Update DrugSubstances set Name = N'benzyl alcohol' where Code = '6262'
Update DrugSubstances set Name = N'dembrexine hydrochloride' where Code = '6258'
Update DrugSubstances set Name = N'oxytetracycline hydrochloride' where Code = '6250'
Update DrugSubstances set Name = N'gliquidone' where Code = '6216'
Update DrugSubstances set Name = N'benzoxonium chloride' where Code = '6205'
Update DrugSubstances set Name = N'tartrazine' where Code = '6198'
Update DrugSubstances set Name = N'ferric oxide yellow' where Code = '6196'
Update DrugSubstances set Name = N'riboflavin' where Code = '6194'
Update DrugSubstances set Name = N'folic acid' where Code = '6191'
Update DrugSubstances set Name = N'cyanocobalamin' where Code = '6190'
Update DrugSubstances set Name = N'meglumine' where Code = '6188'
Update DrugSubstances set Name = N'poloxamer' where Code = '6187'
Update DrugSubstances set Name = N'repaglinide' where Code = '6186'
Update DrugSubstances set Name = N'oxazepam' where Code = '6184'
Update DrugSubstances set Name = N'sodium citrate anhydrous' where Code = '6182'
Update DrugSubstances set Name = N'aspartame' where Code = '6181'
Update DrugSubstances set Name = N'glycerol' where Code = '6178'
Update DrugSubstances set Name = N'methylcellulose' where Code = '6168'
Update DrugSubstances set Name = N'doxylamine succinate' where Code = '6166'
Update DrugSubstances set Name = N'sertraline hydrochloride' where Code = '6158'
Update DrugSubstances set Name = N'ethylcellulose' where Code = '6137'
Update DrugSubstances set Name = N'sodium acetate, anhydrous' where Code = '6116'
Update DrugSubstances set Name = N'glucose anhydrous' where Code = '6113'
Update DrugSubstances set Name = N'calcium chloride dihydrate' where Code = '6109'
Update DrugSubstances set Name = N'orange flavour' where Code = '6102'
Update DrugSubstances set Name = N'sodium picosulfate monohydrate' where Code = '6099'
Update DrugSubstances set Name = N'dipyridamole' where Code = '6098'
Update DrugSubstances set Name = N'chlorphenamine maleate' where Code = '6095'
Update DrugSubstances set Name = N'isoprenaline sulphate dihydrate' where Code = '6093'
Update DrugSubstances set Name = N'ipratropium bromide monohydrate' where Code = '6091'
Update DrugSubstances set Name = N'macrogol' where Code = '6090'
Update DrugSubstances set Name = N'betacarotene' where Code = '6088'
Update DrugSubstances set Name = N'sodium cyclamate' where Code = '6086'
Update DrugSubstances set Name = N'ampicillin' where Code = '6081'
Update DrugSubstances set Name = N'saccharin sodium, dihydrate' where Code = '6079'
Update DrugSubstances set Name = N'citric acid anhydrous' where Code = '6075'
Update DrugSubstances set Name = N'ascorbic acid' where Code = '6073'
Update DrugSubstances set Name = N'edetic acid' where Code = '6065'
Update DrugSubstances set Name = N'povidone' where Code = '6063'
Update DrugSubstances set Name = N'dimeticone' where Code = '6060'
Update DrugSubstances set Name = N'bisacodyl' where Code = '6056'
Update DrugSubstances set Name = N'bromhexine hydrochloride' where Code = '6055'
Update DrugSubstances set Name = N'salbutamol sulfate' where Code = '6052'
Update DrugSubstances set Name = N'sulfadiazine sodium' where Code = '6050'
Update DrugSubstances set Name = N'orciprenaline sulphate' where Code = '6045'
Update DrugSubstances set Name = N'oxyclozanide' where Code = '6043'
Update DrugSubstances set Name = N'meloxicam' where Code = '6038'
Update DrugSubstances set Name = N'brotizolam' where Code = '6035'
Update DrugSubstances set Name = N'clenbuterol hydrochloride' where Code = '6033'
Update DrugSubstances set Name = N'mexiletine hydrochloride' where Code = '6032'
Update DrugSubstances set Name = N'paracetamol' where Code = '6031'
Update DrugSubstances set Name = N'oxitropium bromide' where Code = '6028'
Update DrugSubstances set Name = N'phenylephrine hydrochloride' where Code = '6027'
Update DrugSubstances set Name = N'hydrochlorothiazide_hp' where Code = '6025'
Update DrugSubstances set Name = N'enalapril maleate' where Code = '6024'
Update DrugSubstances set Name = N'fenoterol hydrobromide' where Code = '6023'
Update DrugSubstances set Name = N'trazodone hydrochloride' where Code = '6021'
Update DrugSubstances set Name = N'etilefrine hydrochloride' where Code = '6016'
Update DrugSubstances set Name = N'morphine sulfate' where Code = '6014'
Update DrugSubstances set Name = N'sucrose' where Code = '6009'
Update DrugSubstances set Name = N'ambroxol hydrochloride' where Code = '6006'
Update DrugSubstances set Name = N'lactose monohydrate' where Code = '6002'
Update DrugSubstances set Name = N'placebo dabigatran etexilate' where Code = '5526214'
Update DrugSubstances set Name = N'placebo nintedanib' where Code = '5526213'
Update DrugSubstances set Name = N'placebo afatinib dimaleate' where Code = '5526212'
Update DrugSubstances set Name = N'trilaciclib' where Code = '5520341'
Update DrugSubstances set Name = N'Insulin glargine' where Code = '5520338'
Update DrugSubstances set Name = N'erlotinib' where Code = '5477016'
Update DrugSubstances set Name = N'alpelisib' where Code = '5472256'
Update DrugSubstances set Name = N'butylscopolamine' where Code = '5462365'
Update DrugSubstances set Name = N'classical swine fever, c-strain_hp' where Code = '5459231'
Update DrugSubstances set Name = N'survodutide' where Code = '5456455'
Update DrugSubstances set Name = N'poziotinib' where Code = '5452570'
Update DrugSubstances set Name = N'kanamycin' where Code = '5443557'
Update DrugSubstances set Name = N'irinotecan' where Code = '5443555'
Update DrugSubstances set Name = N'duloxetine' where Code = '5443551'
Update DrugSubstances set Name = N'docusate' where Code = '5443550'
Update DrugSubstances set Name = N'deleobuvir' where Code = '5443549'
Update DrugSubstances set Name = N'bevacizumab' where Code = '5439347'
Update DrugSubstances set Name = N'morphine' where Code = '5423739'
Update DrugSubstances set Name = N'epinastine' where Code = '5423738'
Update DrugSubstances set Name = N'diltiazem' where Code = '5423737'
Update DrugSubstances set Name = N'pirenzepine' where Code = '5423736'
Update DrugSubstances set Name = N'algestone' where Code = '5423735'
Update DrugSubstances set Name = N'prolintane' where Code = '5423734'
Update DrugSubstances set Name = N'talipexole' where Code = '5423733'
Update DrugSubstances set Name = N'nannizzia' where Code = '5422482'
Update DrugSubstances set Name = N'tamsulosin' where Code = '5407648'
Update DrugSubstances set Name = N'tiotropium bromide' where Code = '5407636'
Update DrugSubstances set Name = N'afatinib' where Code = '5407635'
Update DrugSubstances set Name = N'pramipexole dihydrochloride' where Code = '5407628'
Update DrugSubstances set Name = N'pramipexole' where Code = '5407627'
Update DrugSubstances set Name = N'metamizole' where Code = '5407626'
Update DrugSubstances set Name = N'ipratropium' where Code = '5407622'
Update DrugSubstances set Name = N'clenbuterol' where Code = '5407621'
Update DrugSubstances set Name = N'fenoterol' where Code = '5407620'
Update DrugSubstances set Name = N'ambroxol' where Code = '5407617'
Update DrugSubstances set Name = N'zongertinib' where Code = '5403595'
Update DrugSubstances set Name = N'olmutinib' where Code = '5402503'
Update DrugSubstances set Name = N'calcium phosphinate' where Code = '5379483'
Update DrugSubstances set Name = N'trametinib' where Code = '5368871'
Update DrugSubstances set Name = N'velagliflozin' where Code = '5311109'
Update DrugSubstances set Name = N'stabilizer a' where Code = '5268991'
Update DrugSubstances set Name = N'Chromium' where Code = '5240881'
Update DrugSubstances set Name = N'CALCIUM IODATE' where Code = '5240215'
Update DrugSubstances set Name = N'green lip mussel extract' where Code = '5211524'
Update DrugSubstances set Name = N'Icaridin' where Code = '5211519'
Update DrugSubstances set Name = N'Enterococcous Faecium' where Code = '5210068'
Update DrugSubstances set Name = N'oxaliplatin' where Code = '5186305'
Update DrugSubstances set Name = N'Dulbecco''s Phosphate Buffered Saline (DPBS)' where Code = '5182969'
Update DrugSubstances set Name = N'montanide isa 207 VG' where Code = '5182965'
Update DrugSubstances set Name = N'streptomycin sulfate solution' where Code = '5166846'
Update DrugSubstances set Name = N'pembrolizumab' where Code = '5156212'
Update DrugSubstances set Name = N'Cloprostenol' where Code = '4997244'
Update DrugSubstances set Name = N'tylosin phosphate' where Code = '4995408'
Update DrugSubstances set Name = N'brigimadlin' where Code = '4861539'
Update DrugSubstances set Name = N'ezabenlimab' where Code = '4820291'
Update DrugSubstances set Name = N'Effer-Soda 12' where Code = '4658787'
Update DrugSubstances set Name = N'montanide isa 708' where Code = '4637877'
Update DrugSubstances set Name = N'lactose' where Code = '4507000'
Update DrugSubstances set Name = N'stearoyl macrogolglycerides' where Code = '4444617'
Update DrugSubstances set Name = N'DMEM' where Code = '4300593'
Update DrugSubstances set Name = N'spesolimab' where Code = '4279007'
Update DrugSubstances set Name = N'red vine leaf extract' where Code = '4179777'
Update DrugSubstances set Name = N'sotorasib' where Code = '30293166'
Update DrugSubstances set Name = N'ketamine' where Code = '30292991'
Update DrugSubstances set Name = N'trazodone' where Code = '30292376'
Update DrugSubstances set Name = N'tramazoline' where Code = '30292375'
Update DrugSubstances set Name = N'sertraline' where Code = '30292374'
Update DrugSubstances set Name = N'isoprenaline' where Code = '30292373'
Update DrugSubstances set Name = N'faldaprevir' where Code = '30292372'
Update DrugSubstances set Name = N'doxylamine' where Code = '30292371'
Update DrugSubstances set Name = N'diclofenac' where Code = '30292370'
Update DrugSubstances set Name = N'cefalexin' where Code = '30292368'
Update DrugSubstances set Name = N'bunazosin' where Code = '30292366'
Update DrugSubstances set Name = N'amoxicillin' where Code = '30292365'
Update DrugSubstances set Name = N'diphenhydramine' where Code = '30292363'
Update DrugSubstances set Name = N'dembrexine' where Code = '30292361'
Update DrugSubstances set Name = N'methocarbamol' where Code = '30291703'
Update DrugSubstances set Name = N'fradafiban' where Code = '30291700'
Update DrugSubstances set Name = N'tesofensine' where Code = '30291148'
Update DrugSubstances set Name = N'terbutaline' where Code = '30291147'
Update DrugSubstances set Name = N'temoporfin' where Code = '30291146'
Update DrugSubstances set Name = N'pivmecillinam' where Code = '30291145'
Update DrugSubstances set Name = N'methadone' where Code = '30291143'
Update DrugSubstances set Name = N'meclocycline' where Code = '30291142'
Update DrugSubstances set Name = N'thiethylperazine' where Code = '30291141'
Update DrugSubstances set Name = N'tiapride' where Code = '30291140'
Update DrugSubstances set Name = N'tioxolone' where Code = '30291139'
Update DrugSubstances set Name = N'tolciclate' where Code = '30291138'
Update DrugSubstances set Name = N'mabuterol' where Code = '30291137'
Update DrugSubstances set Name = N'tretinoin' where Code = '30291136'
Update DrugSubstances set Name = N'vinorelbine' where Code = '30291135'
Update DrugSubstances set Name = N'zafirlukast' where Code = '30291134'
Update DrugSubstances set Name = N'loxoprofen' where Code = '30291132'
Update DrugSubstances set Name = N'lodoxamide' where Code = '30291125'
Update DrugSubstances set Name = N'levodropropizine' where Code = '30291124'
Update DrugSubstances set Name = N'ifosfamide' where Code = '30291123'
Update DrugSubstances set Name = N'idoxuridine' where Code = '30291122'
Update DrugSubstances set Name = N'hydrargaphen' where Code = '30291121'
Update DrugSubstances set Name = N'fusidic acid' where Code = '30291120'
Update DrugSubstances set Name = N'fominoben' where Code = '30291119'
Update DrugSubstances set Name = N'flutoprazepam' where Code = '30291118'
Update DrugSubstances set Name = N'famotidine' where Code = '30291117'
Update DrugSubstances set Name = N'etofenamate' where Code = '30291116'
Update DrugSubstances set Name = N'estriol' where Code = '30291100'
Update DrugSubstances set Name = N'estradiol valerate' where Code = '30291099'
Update DrugSubstances set Name = N'erythromycin' where Code = '30291098'
Update DrugSubstances set Name = N'epalrestat' where Code = '30291093'
Update DrugSubstances set Name = N'dydrogesterone' where Code = '30291091'
Update DrugSubstances set Name = N'doxepin' where Code = '30291089'
Update DrugSubstances set Name = N'dihydroergotamine' where Code = '30291087'
Update DrugSubstances set Name = N'diflorasone' where Code = '30291086'
Update DrugSubstances set Name = N'dibekacin' where Code = '30291085'
Update DrugSubstances set Name = N'dextromethorphan' where Code = '30291083'
Update DrugSubstances set Name = N'desogestrel' where Code = '30291082'
Update DrugSubstances set Name = N'talsaclidine' where Code = '30291081'
Update DrugSubstances set Name = N'roxatidine' where Code = '30291054'
Update DrugSubstances set Name = N'ramipril' where Code = '30291053'
Update DrugSubstances set Name = N'procaterol' where Code = '30291050'
Update DrugSubstances set Name = N'porfiromycin' where Code = '30291049'
Update DrugSubstances set Name = N'pentoxyverine' where Code = '30291048'
Update DrugSubstances set Name = N'orphenadrine' where Code = '30291047'
Update DrugSubstances set Name = N'natamycin' where Code = '30291046'
Update DrugSubstances set Name = N'zatebradine' where Code = '30291045'
Update DrugSubstances set Name = N'semorphone' where Code = '30291038'
Update DrugSubstances set Name = N'fenipentol' where Code = '30291035'
Update DrugSubstances set Name = N'draquinolol' where Code = '30291034'
Update DrugSubstances set Name = N'itasetron' where Code = '30291032'
Update DrugSubstances set Name = N'feprazone' where Code = '30291031'
Update DrugSubstances set Name = N'doramapimod' where Code = '30291028'
Update DrugSubstances set Name = N'sarakalim' where Code = '30291027'
Update DrugSubstances set Name = N'irampanel' where Code = '30291026'
Update DrugSubstances set Name = N'salmeterol' where Code = '30291025'
Update DrugSubstances set Name = N'falnidamol' where Code = '30291021'
Update DrugSubstances set Name = N'lefradafiban' where Code = '30291010'
Update DrugSubstances set Name = N'tanogitran' where Code = '30291009'
Update DrugSubstances set Name = N'vipoglanstat' where Code = '30290991'
Update DrugSubstances set Name = N'nebracetam' where Code = '30290990'
Update DrugSubstances set Name = N'piroxicillin' where Code = '30290989'
Update DrugSubstances set Name = N'pinokalant' where Code = '30290988'
Update DrugSubstances set Name = N'ontazolast' where Code = '30290987'
Update DrugSubstances set Name = N'palinavir' where Code = '30290984'
Update DrugSubstances set Name = N'sabiporide' where Code = '30290983'
Update DrugSubstances set Name = N'olcegepant' where Code = '30290982'
Update DrugSubstances set Name = N'pioglitazone' where Code = '30290978'
Update DrugSubstances set Name = N'otenzepad' where Code = '30290975'
Update DrugSubstances set Name = N'dirithromycin' where Code = '30290974'
Update DrugSubstances set Name = N'sulmazole' where Code = '30290973'
Update DrugSubstances set Name = N'falipamil' where Code = '30290972'
Update DrugSubstances set Name = N'mebeverine' where Code = '30290971'
Update DrugSubstances set Name = N'hydroxyzine' where Code = '30290970'
Update DrugSubstances set Name = N'levacetylmethadol' where Code = '30290959'
Update DrugSubstances set Name = N'enlimomab pegol' where Code = '30290952'
Update DrugSubstances set Name = N'bivatuzumab' where Code = '30290950'
Update DrugSubstances set Name = N'enlimomab' where Code = '30290948'
Update DrugSubstances set Name = N'sibrotuzumab' where Code = '30290947'
Update DrugSubstances set Name = N'mopidamol' where Code = '30290922'
Update DrugSubstances set Name = N'nardeterol' where Code = '30290920'
Update DrugSubstances set Name = N'mifentidine' where Code = '30290919'
Update DrugSubstances set Name = N'mexenone' where Code = '30290918'
Update DrugSubstances set Name = N'cyclandelate' where Code = '30290917'
Update DrugSubstances set Name = N'cromoglicic acid' where Code = '30290916'
Update DrugSubstances set Name = N'crobenetine' where Code = '30290915'
Update DrugSubstances set Name = N'clotrimazole' where Code = '30290912'
Update DrugSubstances set Name = N'clofibrate' where Code = '30290911'
Update DrugSubstances set Name = N'clodronic acid' where Code = '30290908'
Update DrugSubstances set Name = N'clobutinol' where Code = '30290907'
Update DrugSubstances set Name = N'clebopride' where Code = '30290906'
Update DrugSubstances set Name = N'cimetidine' where Code = '30290880'
Update DrugSubstances set Name = N'ciluprevir' where Code = '30290879'
Update DrugSubstances set Name = N'cilobradine' where Code = '30290878'
Update DrugSubstances set Name = N'cilnidipine' where Code = '30290877'
Update DrugSubstances set Name = N'chlortalidone' where Code = '30290876'
Update DrugSubstances set Name = N'ceftizoxime' where Code = '30290875'
Update DrugSubstances set Name = N'cefixime' where Code = '30290874'
Update DrugSubstances set Name = N'bunitrolol' where Code = '30290873'
Update DrugSubstances set Name = N'bumetanide' where Code = '30290872'
Update DrugSubstances set Name = N'brodimoprim' where Code = '30290870'
Update DrugSubstances set Name = N'bisoxatin' where Code = '30290865'
Update DrugSubstances set Name = N'betahistine' where Code = '30290861'
Update DrugSubstances set Name = N'bepafant' where Code = '30290858'
Update DrugSubstances set Name = N'benzydamine' where Code = '30290857'
Update DrugSubstances set Name = N'beclobrate' where Code = '30290856'
Update DrugSubstances set Name = N'bamethan' where Code = '30290855'
Update DrugSubstances set Name = N'azepexole' where Code = '30290854'
Update DrugSubstances set Name = N'argatroban' where Code = '30290853'
Update DrugSubstances set Name = N'aptiganel' where Code = '30290850'
Update DrugSubstances set Name = N'apaxifylline' where Code = '30290846'
Update DrugSubstances set Name = N'terbogrel' where Code = '30290826'
Update DrugSubstances set Name = N'naproxen' where Code = '30290825'
Update DrugSubstances set Name = N'flurbiprofen' where Code = '30290824'
Update DrugSubstances set Name = N'dalteparin' where Code = '30290822'
Update DrugSubstances set Name = N'samixogrel' where Code = '30290820'
Update DrugSubstances set Name = N'pomisartan' where Code = '30290819'
Update DrugSubstances set Name = N'doqualast' where Code = '30290818'
Update DrugSubstances set Name = N'alacepril' where Code = '30290817'
Update DrugSubstances set Name = N'interferon-alfa-2c' where Code = '30290813'
Update DrugSubstances set Name = N'alicaforsen' where Code = '30290812'
Update DrugSubstances set Name = N'tremacamra' where Code = '30290811'
Update DrugSubstances set Name = N'figopitant' where Code = '30290784'
Update DrugSubstances set Name = N'apalcillin' where Code = '30290776'
Update DrugSubstances set Name = N'apafant' where Code = '30290775'
Update DrugSubstances set Name = N'aminophylline' where Code = '30290774'
Update DrugSubstances set Name = N'amelubant' where Code = '30290773'
Update DrugSubstances set Name = N'alovudine' where Code = '30290772'
Update DrugSubstances set Name = N'alinidine' where Code = '30290771'
Update DrugSubstances set Name = N'alfatradiol' where Code = '30290769'
Update DrugSubstances set Name = N'alfacalcidol' where Code = '30290768'
Update DrugSubstances set Name = N'adimolol' where Code = '30290767'
Update DrugSubstances set Name = N'acitemate' where Code = '30290766'
Update DrugSubstances set Name = N'acemetacin' where Code = '30290765'
Update DrugSubstances set Name = N'danitracen' where Code = '30289951'
Update DrugSubstances set Name = N'pergolide' where Code = '30284699'
Update DrugSubstances set Name = N'flunisolide' where Code = '30284558'
Update DrugSubstances set Name = N'etilefrine' where Code = '30284557'
Update DrugSubstances set Name = N'mexiletine' where Code = '30284555'
Update DrugSubstances set Name = N'oxymetazoline' where Code = '30284552'
Update DrugSubstances set Name = N'isosorbide' where Code = '30284551'
Update DrugSubstances set Name = N'fenoxazoline' where Code = '30284549'
Update DrugSubstances set Name = N'naphazoline' where Code = '30284548'
Update DrugSubstances set Name = N'nerandomilast' where Code = '30282557'
Update DrugSubstances set Name = N'hyaluronic acid' where Code = '30270713'
Update DrugSubstances set Name = N'xentuzumab' where Code = '30269827'
Update DrugSubstances set Name = N'melarsomine' where Code = '30209755'
Update DrugSubstances set Name = N'ketoprofen' where Code = '30206803'
Update DrugSubstances set Name = N'diphenhydramine hydrochloride' where Code = '30000739'
Update DrugSubstances set Name = N'lidocaine hydrochloride monohydrate' where Code = '30000652'
Update DrugSubstances set Name = N'chlorhexidine' where Code = '30000298'
Update DrugSubstances set Name = N'lidocaine hydrochloride' where Code = '30000203'
Update DrugSubstances set Name = N'n.z. amine as' where Code = '29511'
Update DrugSubstances set Name = N'gallibacterium anatis' where Code = '29390'
Update DrugSubstances set Name = N'avibacterium (haemophilus) paragallinarum' where Code = '29350'
Update DrugSubstances set Name = N'stabilizer' where Code = '29290'
Update DrugSubstances set Name = N'metformin' where Code = '28870'
Update DrugSubstances set Name = N'nintedanib' where Code = '28830'
Update DrugSubstances set Name = N'carmine' where Code = '28750'
Update DrugSubstances set Name = N'pergolide mesilate' where Code = '28572'
Update DrugSubstances set Name = N'faldaprevir sodium' where Code = '28531'
Update DrugSubstances set Name = N'ivermectin' where Code = '28530'
Update DrugSubstances set Name = N'irinotecan hydrochloride' where Code = '28430'
Update DrugSubstances set Name = N'tiotropium' where Code = '28030'
Update DrugSubstances set Name = N'protamine sulphate' where Code = '27873'
Update DrugSubstances set Name = N'ranitidine' where Code = '27792'
Update DrugSubstances set Name = N'chondroitin sulphate' where Code = '27690'
Update DrugSubstances set Name = N'amlodipine' where Code = '27630'
Update DrugSubstances set Name = N'doxycycline' where Code = '27471'
Update DrugSubstances set Name = N'flunisolide hemihydrate' where Code = '27232'
Update DrugSubstances set Name = N'ciclesonide' where Code = '27211'
Update DrugSubstances set Name = N'glucomannan' where Code = '26959'
Update DrugSubstances set Name = N'velagliflozin proline monohydrate' where Code = '26890'
Update DrugSubstances set Name = N'ammonium hydroxide' where Code = '26733'
Update DrugSubstances set Name = N'fetal bovine serum' where Code = '26331'
Update DrugSubstances set Name = N'bme liquid gibco' where Code = '2632901'
Update DrugSubstances set Name = N'avenciguat' where Code = '2629948'
Update DrugSubstances set Name = N'emulsigen iii' where Code = '2624662'
Update DrugSubstances set Name = N'metamizole sodium' where Code = '2617824'
Update DrugSubstances set Name = N'iclepertin' where Code = '2603310'
Update DrugSubstances set Name = N'equine serum' where Code = '2598560'
Update DrugSubstances set Name = N'sodium citrate' where Code = '2596227'
Update DrugSubstances set Name = N'buffered thimerosal' where Code = '2595669'
Update DrugSubstances set Name = N'adjuvant-2' where Code = '2595569'
Update DrugSubstances set Name = N'adjuvant-1' where Code = '2595568'
Update DrugSubstances set Name = N'saline' where Code = '2595263'
Update DrugSubstances set Name = N'stabilizer - nzsg' where Code = '2595197'
Update DrugSubstances set Name = N'neomycin' where Code = '2594956'
Update DrugSubstances set Name = N'purified water' where Code = '2594124'
Update DrugSubstances set Name = N'thimerosal soln 5%' where Code = '2594123'
Update DrugSubstances set Name = N'mem' where Code = '2594122'
Update DrugSubstances set Name = N'raccoon poxvirus-rabies' where Code = '2593424'
Update DrugSubstances set Name = N'feline immunodeficiency virus' where Code = '2593409'
Update DrugSubstances set Name = N'tryptic (trypticase) soy broth' where Code = '2592229'
Update DrugSubstances set Name = N'MDBK cells' where Code = '2592225'
Update DrugSubstances set Name = N'montanide 888' where Code = '2591667'
Update DrugSubstances set Name = N'gentamicin' where Code = '2591665'
Update DrugSubstances set Name = N'alum' where Code = '2591329'
Update DrugSubstances set Name = N'bbronchiseptica' where Code = '2590942'
Update DrugSubstances set Name = N'sodium bisulfite' where Code = '2590539'
Update DrugSubstances set Name = N'potassium phosphate monobasic' where Code = '2589699'
Update DrugSubstances set Name = N'EDTA' where Code = '2589692'
Update DrugSubstances set Name = N'orciprenaline' where Code = '25887'
Update DrugSubstances set Name = N'salbutamol' where Code = '25871'
Update DrugSubstances set Name = N'l-glutamic acid' where Code = '2586205'
Update DrugSubstances set Name = N'quil-a' where Code = '2584926'
Update DrugSubstances set Name = N'physiological saline' where Code = '2584827'
Update DrugSubstances set Name = N'Mycobacterium paratuberculosis,18 (ST-18)' where Code = '2584746'
Update DrugSubstances set Name = N'emulsigen sa' where Code = '2583752'
Update DrugSubstances set Name = N'neocryl' where Code = '2583744'
Update DrugSubstances set Name = N'Chlamydia psittaci,Cello' where Code = '2583619'
Update DrugSubstances set Name = N'feline leukemia virus' where Code = '2583617'
Update DrugSubstances set Name = N'oil' where Code = '2583583'
Update DrugSubstances set Name = N'balanced salt solution' where Code = '2583509'
Update DrugSubstances set Name = N'bi 1057885' where Code = '2581612'
Update DrugSubstances set Name = N'Borrelia burgdorferi' where Code = '2581053'
Update DrugSubstances set Name = N'maltitol' where Code = '2577849'
Update DrugSubstances set Name = N'oxytetracycline' where Code = '25350'
Update DrugSubstances set Name = N'phenylbutazone calcium' where Code = '25251'
Update DrugSubstances set Name = N'empagliflozin' where Code = '25192'
Update DrugSubstances set Name = N'sodium selenite' where Code = '25050'
Update DrugSubstances set Name = N'bi 655906' where Code = '2500631'
Update DrugSubstances set Name = N'benazepril hydrochloride' where Code = '2498470'
Update DrugSubstances set Name = N'polypropylene glycol' where Code = '2497957'
Update DrugSubstances set Name = N'deleobuvir sodium' where Code = '24970'
Update DrugSubstances set Name = N'hedera helix extract' where Code = '2495413'
Update DrugSubstances set Name = N'propylene glycol dicaprylate-caprate' where Code = '2495312'
Update DrugSubstances set Name = N'aluminium distearat' where Code = '2495010'
Update DrugSubstances set Name = N'difloxacin' where Code = '2488219'
Update DrugSubstances set Name = N'hetacillin potassium' where Code = '2487743'
Update DrugSubstances set Name = N'difloxacin hydrochlorid' where Code = '2487693'
Update DrugSubstances set Name = N'idarucizumab' where Code = '2487432'
Update DrugSubstances set Name = N'risankizumab' where Code = '2487431'
Update DrugSubstances set Name = N'moxidectin' where Code = '2487150'
Update DrugSubstances set Name = N'polyethylene glycol monoisostearate' where Code = '2484686'
Update DrugSubstances set Name = N'oxfendazole' where Code = '2484680'
Update DrugSubstances set Name = N'racecadotril' where Code = '2482972'
Update DrugSubstances set Name = N'sodium sulfachlorpyridazine' where Code = '2481648'
Update DrugSubstances set Name = N'sulfachlorpyridazine' where Code = '2481647'
Update DrugSubstances set Name = N'equine herpesvirus,1:HRA' where Code = '2477018'
Update DrugSubstances set Name = N'ampicillin trihydrate' where Code = '24770'
Update DrugSubstances set Name = N'bevacizumab' where Code = '2474167'
Update DrugSubstances set Name = N'adalimumab' where Code = '2471067'
Update DrugSubstances set Name = N'd-alpha tocopheryl acetate' where Code = '2469884'
Update DrugSubstances set Name = N'nerandomilast' where Code = '2460585'
Update DrugSubstances set Name = N'simeticone' where Code = '24390'
Update DrugSubstances set Name = N'olodaterol hydrochloride' where Code = '24370'
Update DrugSubstances set Name = N'sodium citrate dihydrate' where Code = '24231'
Update DrugSubstances set Name = N'codeine phosphate hemihydrate' where Code = '24150'
Update DrugSubstances set Name = N'panax ginseng extract (redirun)' where Code = '23970'
Update DrugSubstances set Name = N'volasertib' where Code = '23732'
Update DrugSubstances set Name = N'olodaterol' where Code = '23393'
Update DrugSubstances set Name = N'tetrasodium edetate' where Code = '23350'
Update DrugSubstances set Name = N'linagliptin' where Code = '23051'
Update DrugSubstances set Name = N'glucose monohydrate' where Code = '22950'
Update DrugSubstances set Name = N'citric acid monohydrate' where Code = '22891'
Update DrugSubstances set Name = N'disodium edetate dihydrate' where Code = '22871'
Update DrugSubstances set Name = N'ipratropium bromide anhydrous' where Code = '22870'
Update DrugSubstances set Name = N'lactose anhydrous' where Code = '22750'
Update DrugSubstances set Name = N'Dulbecco''s modified Eagle Medium with Ham''s foritfied F12' where Code = '22350'
Update DrugSubstances set Name = N'copper complexes of chlorophylls' where Code = '22332'
Update DrugSubstances set Name = N'antifoam' where Code = '22290'
Update DrugSubstances set Name = N'butorphanol tartrate' where Code = '22270'
Update DrugSubstances set Name = N'ritobegron, KUC7483' where Code = '21690'
Update DrugSubstances set Name = N'glucosamine hydrochloride' where Code = '21570'
Update DrugSubstances set Name = N'afatinib dimaleate' where Code = '20970'
Update DrugSubstances set Name = N'crospovidone' where Code = '20791'
Update DrugSubstances set Name = N'polysorbate 20' where Code = '20750'
Update DrugSubstances set Name = N'nintedanib esylate' where Code = '20671'
Update DrugSubstances set Name = N'sodium dihydrogen phosphate dihydrate' where Code = '20650'
Update DrugSubstances set Name = N'cilobradine hydrochloride' where Code = '20422'
Update DrugSubstances set Name = N'dabigatran' where Code = '19914'
Update DrugSubstances set Name = N'dabigatran etexilate' where Code = '19901'
Update DrugSubstances set Name = N'chlorphenamine' where Code = '19573'
Update DrugSubstances set Name = N'climazolam' where Code = '19508'
Update DrugSubstances set Name = N'panax ginseng extract (g 115)' where Code = '19112'
Update DrugSubstances set Name = N'tetracycline hydrochloride' where Code = '18451'

commit transaction;

