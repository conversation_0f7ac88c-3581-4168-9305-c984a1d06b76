﻿using AutoMapper;
using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class CountryMappingProfile : Profile
    {
        public CountryMappingProfile()
        {
            CreateMap<Country, CountryModel>();
            CreateMap<CountryModel, Country>()
                .ForMember(dest => dest.Communications, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.Region, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore());
        }
    }
}
