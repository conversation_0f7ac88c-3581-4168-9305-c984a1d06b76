apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.azureWorkload.appName }}
  labels:
    azure.workload.identity/use: 'true'
  annotations:
    azure.workload.identity/client-id: {{ .Values.azureWorkload.clientId }}
    azure.workload.identity/tenant-id: {{ .Values.azureWorkload.tenantId }}
    azure.workload.identity/service-account-token-expiration: {{ quote .Values.azureWorkload.tokenExpiration }}
    checkov.io/skip1: CKV_K8S_21=Default namespace should not be used