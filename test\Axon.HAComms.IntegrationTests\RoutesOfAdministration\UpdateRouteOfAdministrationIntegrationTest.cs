﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.RoutesOfAdministration;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateRouteOfAdministrationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly RouteOfAdministrationApi routeOfAdministrationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateRouteOfAdministration_ValidRequest_ReturnsOk()
    {
        //Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;
        var requestObj1 = new CreateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Name);

        //Act
        var responseObj1 = await routeOfAdministrationApi.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();
        var requestObj2 = new UpdateRouteOfAdministrationCommandRequest(responseObj1.Id, routeOfAdministrationName);

        await routeOfAdministrationApi.UpdateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj2);
        var responseObj = await routeOfAdministrationApi.GetRouteOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        var sut = responseObj.Data.SingleOrDefault(s => s.Id == responseObj1.Id);
        sut.Should().NotBeNull();
        sut?.Name.Should().Be(requestObj2.Name);
    }

    [Fact]
    public async Task UpdateRouteOfAdministration_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var routeOfAdministrationName1 = Fake.RouteOfAdministration.Name;
        var routeOfAdministrationName2 = Fake.RouteOfAdministration.Name;

        var requestObj1 = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName1);
        var requestObj2 = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName2);

        //Act
        var responseObj1 = await routeOfAdministrationApi.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj1);
        var responseObj2 = await routeOfAdministrationApi.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        //Act
        var requestObj3 = new UpdateRouteOfAdministrationCommandRequest(responseObj1.Id, routeOfAdministrationName2);

        var routeOfAdministrationResponse = () => routeOfAdministrationApi.UpdateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj3);

        //Assert
        var exception = await routeOfAdministrationResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Route of administration with name '{routeOfAdministrationName2}' already exists.");
    }

    [Fact]
    public async Task UpdateRouteOfAdministration_InvalidRouteOfAdministrationId_ThrowsEntityNotFoundExceptionException()
    {
        //Arrange
        var routeOfAdministrationId = Fake.RouteOfAdministration.Id;
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;
        var request = new UpdateRouteOfAdministrationCommandRequest(routeOfAdministrationId, routeOfAdministrationName);

        //Act
        var response = () => routeOfAdministrationApi.UpdateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, request);

        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"RouteOfAdministration\\\" ({routeOfAdministrationId}) was not found.", exception.And.Message);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
