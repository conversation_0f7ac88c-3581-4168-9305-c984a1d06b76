﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance;

public class HACommsContext : DbContext
{
    public HACommsContext(DbContextOptions<HACommsContext> options) : base(options)
    {
    }

    protected HACommsContext(DbContextOptions options) : base(options)
    {
    }

    public DbSet<Communication> Communications { get; set; }

    public DbSet<Comment> Comments { get; set; }

    public DbSet<Country> Countries { get; set; }

    public DbSet<Product> DrugProducts { get; set; }

    public DbSet<ProductExtension> ProductExtensions { get; set; }

    public DbSet<DrugSubstance> DrugSubstances { get; set; }

    public DbSet<DosageForm> DosageForms { get; set; }

    public DbSet<RouteOfAdministration> RouteOfAdministrations { get; set; }

    public DbSet<SubmissionType> SubmissionTypes { get; set; }

    public DbSet<Tag> Tags { get; set; }

    public DbSet<CommunicationsView> CommunicationsView { get; set; }

    public DbSet<Domain.Entities.Application> Applications { get; set; }

    public DbSet<Submission> Submissions { get; set; }

    public DbSet<ProductType> ProductTypes { get; set; }

    public DbSet<Domain.Entities.Audit> Audit { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        optionsBuilder.UseSqlServer(o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery));
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new ApplicationConfiguration());
        modelBuilder.ApplyConfiguration(new CommunicationConfiguration());
        modelBuilder.ApplyConfiguration(new CommentConfiguration());
        modelBuilder.ApplyConfiguration(new CountryConfiguration());
        modelBuilder.ApplyConfiguration(new DosageFormConfiguration());
        modelBuilder.ApplyConfiguration(new DrugProductConfiguration());
        modelBuilder.ApplyConfiguration(new DrugSubstanceConfiguration());
        modelBuilder.ApplyConfiguration(new DrugSubstanceProductConfiguration());
        modelBuilder.ApplyConfiguration(new ProductTypeConfiguration());
        modelBuilder.ApplyConfiguration(new ProductExtensionConfiguration());
        modelBuilder.ApplyConfiguration(new RouteOfAdministrationConfiguration());
        modelBuilder.ApplyConfiguration(new SubmissionConfiguration());
        modelBuilder.ApplyConfiguration(new SubmissionTypeConfiguration());
        modelBuilder.ApplyConfiguration(new TagConfiguration());
        modelBuilder.ApplyConfiguration(new CommentDrugSubstancesConfiguration());
        modelBuilder.ApplyConfiguration(new CommentProductExtensionConfiguration());
        modelBuilder.ApplyConfiguration(new CommentProductExtensionRoutesOfAdministrationConfiguration());
        modelBuilder.ApplyConfiguration(new CommentTagsConfiguration());
        modelBuilder.ApplyConfiguration(new ProductExtensionRouteOfAdministrationConfiguration());
        modelBuilder.ApplyConfiguration(new ProductProductTypesConfiguration());
        modelBuilder.ApplyConfiguration(new AuditConfiguration());

        modelBuilder.Entity<CommunicationsView>()
            .HasNoKey()
            .ToView(nameof(CommunicationsView));
    }
}
