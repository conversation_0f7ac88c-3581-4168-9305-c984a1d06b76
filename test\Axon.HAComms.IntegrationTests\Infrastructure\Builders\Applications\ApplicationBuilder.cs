﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;

public class ApplicationBuilder
{
    private string number = Fake.Application.Number;
    private List<Submission> submissions = [];
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static ApplicationBuilder Default() => new();

    public Domain.Entities.Application Build()
    {
        return new Domain.Entities.Application
        {
            Number = number,
            Submissions = submissions,
            Tenant = tenant
        };
    }

    public ApplicationBuilder WithNumber(string number)
    {
        this.number = number;
        return this;
    }

    public ApplicationBuilder WithSubmissions(params Submission[] submissions)
    {
        this.submissions = submissions.ToList();
        return this;
    }

    public ApplicationBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
