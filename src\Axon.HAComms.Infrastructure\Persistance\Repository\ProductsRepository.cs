﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class ProductsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<ProductsRepository> logger)
    : SqlServerRepository<Product>(context, tenant, logger), IProductsRepository
{
    public async Task<IEnumerable<Product>> GetItemsAsync(Expression<Func<Product, object>> orderByExpression)
    {
        return await context
            .Set<Product>()
            .Include(x => x.DrugSubstances)
            .Include(x => x.ProductTypes)
            .Include(x => x.ProductExtensions)
                .ThenInclude(y => y.RouteOfAdministrations)
            .Include(x => x.ProductExtensions)
                .ThenInclude(y => y.DosageForm)
            .AsNoTracking()
            .OrderBy(orderByExpression)
            .ToListAsync();
    }

    public async Task DeleteItemAsync(int id)
    {
        var product = await context.Set<Product>().SingleAsync(c => c.Id == id);
        DeleteItem(product);
    }

    public IQueryable<Product> GetQueryableItems()
    {
        return context.Set<Product>();
    }

    public async Task<Product> GetItemAsync(int id)
    {
        var entity = await context.Set<Product>()
            .Include(x => x.DrugSubstanceProducts)
            .Include(x => x.ProductProductTypes)
            .Include(x => x.DrugSubstances)
            .Include (x => x.ProductTypes)
            .Include(x => x.ProductExtensions)
                .ThenInclude(y => y.RouteOfAdministrations)
            .Include(x => x.ProductExtensions)
                .ThenInclude(y => y.DosageForm)
            .SingleOrDefaultAsync(d => d.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(Product), id);
    }

    public async Task<bool> ExistsAsync(Expression<Func<Product, bool>> filter)
    {
        return await context.Set<Product>().AsNoTracking().AnyAsync(filter);
    }
}
