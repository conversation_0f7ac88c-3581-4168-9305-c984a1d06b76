using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Products.Delete;

internal class DeleteProductCommandHandler(
    IProductsRepository productsRepo,
    ICommentsRepository commentsRepo,
    ILogger<DeleteProductCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteProductCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteProductCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await productsRepo.GetItemAsync(request.Id);
        var productExtensionIds = entity.ProductExtensions.Select(pe => pe.Id).AsEnumerable();

        var hasComments = await commentsRepo.ExistsAsync(c => c.CommentProductExtensions != null && productExtensionIds.Any(pe => c.CommentProductExtensions.Select(z => z.ProductExtensionId).Contains(pe)));
        if (hasComments)
        {
            logger.LogInformation("Product {ProductName} has associated comments and cannot be deleted.", entity.Name);
            throw new AssociationExistsException("Product", entity.Name);
        }

        await productsRepo.DeleteItemAsync(request.Id);
        await productsRepo.SaveChangesAsync(userProvider);

        auditService.Log(
           correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
           AuditEventType.DRUG_PRODUCT_DELETED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_DELETE, entity);

        return Result.Success();
    }
}
