﻿namespace Axon.HAComms.Api.Services.Authorization
{
    public enum HacommsPermissions
    {
        // Comments
        CanSearchComments,
        CreateComment,
        EditComment,
        DeleteComment,

        // Communication
        ViewCommunication,
        EditCommunication,
        CreateCommunication, 
        DeleteCommunication,

        // Countries
        ViewCountry,

        // Products
        ViewProduct,
        ViewProductList,
        EditProduct,
        CreateProduct,
        DeleteProduct,
        ViewProductCode,

        //Route of Administration
        ViewRouteOfAdministration,
        ViewRouteOfAdministrationList,
        CreateRouteOfAdministration,
        EditRouteOfAdministration,
        DeleteRouteOfAdministration,

        //Dosage forms
        ViewDosageForm,
        ViewDosageFormList,
        CreateDosageForm,
        EditDosageForm,
        DeleteDosageForm,

        // Tags
        ViewTag,
        ViewTagList,
        CreateTag,
        EditTag,
        DeleteTag,

        //Substance
        ViewSubstance,
        ViewSubstanceList,
        EditSubstance,
        CreateSubstance,
        DeleteSubstance,

        ViewApplicationNumber,
        ViewSubmissionNumber,

        ViewSubmissionType,

        //Product Types
        ViewProductType,

        ViewAudit
    }
}
