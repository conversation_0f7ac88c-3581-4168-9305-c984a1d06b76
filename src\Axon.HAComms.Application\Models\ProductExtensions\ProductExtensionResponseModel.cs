﻿using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Application.Models.RoutesOfAdministration;

namespace Axon.HAComms.Application.Models.ProductExtensions
{
    public class ProductExtensionResponseModel
    {
        public int Id { get; set; }
        public string? PCID { get; set; }
        public DosageFormModel DosageForm { get; set; } = default!;
        public ICollection<RouteOfAdministrationModel> RoutesOfAdministration { get; set; } = new HashSet<RouteOfAdministrationModel>();
        public bool IsActive { get; set; }

        public bool IsAssociatedToComment { get; set; }

        public ProductExtensionResponseModel(int id, string pcid, DosageFormModel dosageForm, ICollection<RouteOfAdministrationModel> routesOfAdministration)
        {
            Id = id;
            PCID = pcid;
            DosageForm = dosageForm;
            RoutesOfAdministration = routesOfAdministration;
        }

        public ProductExtensionResponseModel()
        {
        }
    }
}
