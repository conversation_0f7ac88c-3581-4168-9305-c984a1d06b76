﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
	<Nullable>enable</Nullable>
	<ImplicitUsings>enable</ImplicitUsings>
	<IsPackable>false</IsPackable>
	<OutputType>Library</OutputType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	<PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="AutoFixture" Version="4.18.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
	  <PackageReference Include="NSubstitute" Version="5.1.0" />
	  <PackageReference Include="xunit" Version="2.7.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Axon.HAComms.Api.Sdk.Net\Axon.HAComms.Api.Sdk.Net.csproj" />
    <ProjectReference Include="..\..\src\Axon.HAComms.Application\Axon.HAComms.Application.csproj" />
  </ItemGroup>

</Project>
