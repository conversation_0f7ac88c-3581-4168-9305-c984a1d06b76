﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net8.0</TargetFramework>
	  <Nullable>enable</Nullable>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <IsPackable>false</IsPackable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

	<ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
		<PackageReference Include="MockQueryable.Core" Version="7.0.1" />
		<PackageReference Include="MockQueryable.NSubstitute" Version="7.0.1" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.5.7">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="3.1.20" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Axon.HAComms.Application\Axon.HAComms.Application.csproj" />
    <ProjectReference Include="..\Axon.HAComms.Tests.Common\Axon.HAComms.Tests.Common.csproj" />
  </ItemGroup>

</Project>
