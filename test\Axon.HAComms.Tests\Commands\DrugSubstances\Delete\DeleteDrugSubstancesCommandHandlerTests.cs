﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.DrugSubstances.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Delete;

public class DeleteDrugSubstancesCommandHandlerTests
{
    private readonly DeleteDrugSubstanceCommandHandler sut;
    private readonly IDrugSubstancesRepository drugSubstanceRepo;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteDrugSubstancesCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        drugSubstanceRepo = Substitute.For<IDrugSubstancesRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        auditService = Substitute.For<IAuditService>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<DeleteDrugSubstanceCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        sut = new DeleteDrugSubstanceCommandHandler(drugSubstanceRepo, commentsRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var entity = new DrugSubstance
        {
            Name = drugSubstanceName,
            Code = drugSubstanceCode,
            Description = drugSubstanceDescription
        };
        drugSubstanceRepo.GetItemAsync(entity.Id).Returns(entity);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(false);

        var request = new DeleteDrugSubstanceCommandRequest(entity.Id);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, default, false);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.DRUG_SUBSTANCE_DELETED, AuditEventCategory.DRUG_SUBSTANCES, AuditEventDescription.DRUG_SUBSTANCE_DELETE,
                Arg.Any<DrugSubstance>());
    }

    [Fact]
    public void Handle_WithAssociatedComments_ThrowsAssociationException()
    {
        // Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var entity = new DrugSubstance
        {
            Name = drugSubstanceName,
            Code = drugSubstanceCode,
            Description = drugSubstanceDescription
        };
        var request = new DeleteDrugSubstanceCommandRequest(entity.Id);
        drugSubstanceRepo.GetItemAsync(entity.Id).Returns(entity);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(true);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<AssociationExistsException>();
    }
}
