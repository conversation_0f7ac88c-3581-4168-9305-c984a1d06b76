﻿using Asp.Versioning.ApiExplorer;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Axon.HAComms.Api.Infrastructure.OpenApi
{
    public static class SwaggerHostFactory
    {
        /// <summary>
        /// This use used to generate the swagger.json file used for SDK generation
        /// https://github.com/domaindrivendev/Swashbuckle.AspNetCore#use-the-cli-tool-with-a-custom-host-configuration
        /// </summary>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        public static IHost CreateHost()
        {
            var builder = WebApplication.CreateBuilder(new WebApplicationOptions
            {
                ApplicationName = typeof(Program).Assembly.FullName,
                EnvironmentName = Environments.Development
            });

            builder.Services.AddApiVersioning(VersionConfiguration.VersioningOptions).AddApiExplorer(VersionConfiguration.ExplorerOptions);
            builder.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>>(s =>
            {
                var apiVersionDescProvider = s.GetService<IApiVersionDescriptionProvider>() ?? throw new InvalidOperationException($"Cannot resolve implementation of {nameof(IApiVersionDescriptionProvider)}");
                return new ConfigureSwaggerOptions(apiVersionDescProvider, AppConstants.API_TITLE, AppConstants.API_DESCRIPTION, string.Empty);
            });
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(
            c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey

                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });

            return builder.Build();
        }
    }
}
