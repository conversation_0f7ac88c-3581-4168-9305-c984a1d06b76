﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Submissions
{
    public class SubmissionBuilder : IBuilder<Submission>
    {
        private string number = Fake.Submission.Number;
        public Submission Build()
        {
            return new()
            {
                Number = number
            };
        }

        public SubmissionBuilder WithNumber(string number)
        {
            this.number = number;
            return this;
        }
    }
}
