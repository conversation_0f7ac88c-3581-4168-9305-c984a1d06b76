﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DosageForms;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateDosageFormIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DosageFormsApi dosageFormsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateDosageForm_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dosageFormName = Fake.DosageForm.Name;
        var requestObj = new CreateDosageFormCommandRequest(dosageFormName);

        //Act
        var responseObj = await dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Name.Should().Be(dosageFormName);
    }

    [Fact]
    public async Task CreateDosageForm_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var dosageFormName = Fake.DosageForm.Name;

        var requestObj1 = new CreateDosageFormCommandRequest(dosageFormName);

        var requestObj2 = new CreateDosageFormCommandRequest(dosageFormName);

        //Act
        var responseObj1 = await dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();

        var responseObj2 = () => dosageFormsApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        //Assert
        var exception = await responseObj2.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Dosage form with name '{dosageFormName}' already exists.");
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.DosageForms.Clear();
        await dbContext.SaveChangesAsync();
    }
}
