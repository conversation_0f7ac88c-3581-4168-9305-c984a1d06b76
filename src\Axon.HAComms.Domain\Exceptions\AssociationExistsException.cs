﻿
using System.Runtime.Serialization;

namespace Axon.HAComms.Domain.Exceptions
{
    public class AssociationExistsException : Exception
    {
        public AssociationExistsException() { }
        public AssociationExistsException(string message) : base(message) { }
        public AssociationExistsException(string message, Exception innerException) : base(message, innerException)
        { }

        public AssociationExistsException(string name, object key)
            : base($"Entity with \"{name}\" = ({key}) is associated with comments and cannot be modified.")
        {
        }
    }
}
