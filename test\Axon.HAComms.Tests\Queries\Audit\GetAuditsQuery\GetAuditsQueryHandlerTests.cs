﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Providers;
using Axon.HAComms.Application.Queries.Audit.GetAuditsQuery;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.OData;
using Microsoft.AspNetCore.OData.Extensions;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OData.Edm;
using Microsoft.OData.ModelBuilder;
using Microsoft.OData.UriParser;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Audit.GetAuditsQuery;

public class GetAuditsQueryHandlerTests
{
    private readonly IAuditProvider<ApplicationAudit> auditProvider;
    private readonly IEdmModel edmModel;
    private readonly GetAuditsQueryHandler sut;

    public GetAuditsQueryHandlerTests()
    {
        auditProvider = Substitute.For<IAuditProvider<ApplicationAudit>>();
        sut = new GetAuditsQueryHandler(auditProvider);

        var modelBuilder = new ODataConventionModelBuilder();
        modelBuilder.EntitySet<ApplicationAudit>("Audits");
        edmModel = modelBuilder.GetEdmModel();
    }

    [Fact]
    public async Task Handle_NoAuditsFromRepository_ReturnsEmptyArray()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.SetMaxTop(11).AddRouteComponents(routePrefix, edmModel), "");
        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var audits = new List<ApplicationAudit>().AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().BeEmpty();
    }

    [Fact]
    public async Task Handle_SomeAuditsFromRepository_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.SetMaxTop(11).AddRouteComponents(routePrefix, edmModel), "");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit> { new() { Id = id, Tenant = tenant } }
            .AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(1);
        result.ApplicationAudits.Should().Contain(x => x.Id == id);
    }

    [Fact]
    public async Task Handle_MultipleTenantAuditsFromRepository_ReturnsCorrectAuditsForTenant()
    {
        // Arrange
        const string tenant1 = "Contoso";
        const string tenant2 = "Phlex";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.SetMaxTop(11).AddRouteComponents(routePrefix, edmModel), "");

        var request = new GetAuditsQueryRequest(tenant1, oDataQueryOptions);
        var id1 = Guid.NewGuid();
        var id2 = Guid.NewGuid();
        var audits = new List<ApplicationAudit> { new() { Id = id1, Tenant = tenant1 }, new() { Id = id2, Tenant = tenant2 } }
            .AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(1);
        result.ApplicationAudits.Should().Contain(x => x.Tenant == tenant1);
        result.ApplicationAudits.Should().NotContain(x => x.Tenant == tenant2);
    }

    [Fact]
    public async Task Handle_GetAudits_WithOrderBy_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.OrderBy().SetMaxTop(11).AddRouteComponents(routePrefix, edmModel),
            "$orderby=StartDate asc");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit>
        {
            new() { Id = id, Tenant = tenant, StartDate = new DateTime(2024, 12, 02, 10, 00, 00, DateTimeKind.Utc) },
            new() { Id = id, Tenant = tenant, StartDate = new DateTime(2024, 12, 01, 09, 00, 00, DateTimeKind.Utc) },
            new() { Id = id, Tenant = tenant, StartDate = new DateTime(2024, 12, 01, 08, 00, 00, DateTimeKind.Utc) }
        }.AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(3);
        result.ApplicationAudits?[0].StartDate.Should().BeBefore(result.ApplicationAudits[1].StartDate);
        result.ApplicationAudits?[1].StartDate.Should().BeBefore(result.ApplicationAudits[2].StartDate);
    }

    [Fact]
    public async Task Handle_GetAudits_WithFilter_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.Filter().SetMaxTop(11).AddRouteComponents(routePrefix, edmModel),
            "$filter=EventType eq 'Test:Type2'");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit>
        {
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type2" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" }
        }.AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(1);
        result.ApplicationAudits?[0].EventType.Should().Be("Test:Type2");
    }

    [Fact]
    public async Task Handle_GetAudits_WithSkip_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.Filter().SetMaxTop(11).AddRouteComponents(routePrefix, edmModel),
            "$skip=2");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit>
        {
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type2" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" }
        }.AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_GetAudits_WithMaxTopNull_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.SetMaxTop(null).AddRouteComponents(routePrefix, edmModel), "");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit>
        {
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type2" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" }
        }.AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(3);
    }

    [Fact]
    public async Task Handle_GetAudits_WithMaxTopNull_WithTop_ReturnsCorrectAudits()
    {
        // Arrange
        const string tenant = "Contoso";
        var oDataQueryOptions = BuildOdataOptions((routePrefix, options) => options.SetMaxTop(null).AddRouteComponents(routePrefix, edmModel), "$top=2");

        var request = new GetAuditsQueryRequest(tenant, oDataQueryOptions);
        var id = Guid.NewGuid();
        var audits = new List<ApplicationAudit>
        {
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type2" },
            new() { Id = id, Tenant = tenant, EventType = "Test:Type1" }
        }.AsQueryable();

        auditProvider.QueryEvents().Returns(audits.BuildMock());

        // Act
        var result = await sut.Handle(request, default);

        // Assert
        result.Should().NotBeNull();
        result.ApplicationAudits.Should().NotBeNull();
        result.ApplicationAudits.Should().HaveCount(2);
    }

    private ODataQueryOptions<ApplicationAudit> BuildOdataOptions(Action<string, ODataOptions> setupAction, string query)
    {
        const string routePrefix = "odata";
        var entitySet = edmModel.EntityContainer.FindEntitySet("Audits");
        var path = new ODataPath(new EntitySetSegment(entitySet));

        var request = Create("GET",
            $"http://localhost/api?{query}",
            dataOptions => setupAction(routePrefix, dataOptions));

        request.ODataFeature().Model = edmModel;
        request.ODataFeature().Path = path;
        request.ODataFeature().RoutePrefix = routePrefix;

        var oDataQueryContext = new ODataQueryContext(edmModel, typeof(ApplicationAudit), new ODataPath());
        return new ODataQueryOptions<ApplicationAudit>(oDataQueryContext, request);
    }

    private static HttpRequest Create(string method, string uri, Action<ODataOptions> setupAction)
    {
        HttpContext context = new DefaultHttpContext();
        var request = context.Request;

        IServiceCollection services = new ServiceCollection();
        services.Configure(setupAction);
        context.RequestServices = services.BuildServiceProvider();

        request.Method = method;
        var requestUri = new Uri(uri);
        request.Scheme = requestUri.Scheme;
        request.Host = requestUri.IsDefaultPort ? new HostString(requestUri.Host) : new HostString(requestUri.Host, requestUri.Port);
        request.QueryString = new QueryString(requestUri.Query);
        request.Path = new PathString(requestUri.AbsolutePath);

        return request;
    }
}
