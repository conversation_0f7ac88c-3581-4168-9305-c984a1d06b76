/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CreateCommunicationCommandRequest
    /// </summary>
    [DataContract(Name = "CreateCommunicationCommandRequest")]
    public partial class CreateCommunicationCommandRequest : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateCommunicationCommandRequest" /> class.
        /// </summary>
        /// <param name="subject">subject.</param>
        /// <param name="submissionTypeId">submissionTypeId.</param>
        /// <param name="countryId">countryId.</param>
        /// <param name="dateOfCommunication">dateOfCommunication.</param>
        /// <param name="applications">applications.</param>
        /// <param name="comment">comment.</param>
        public CreateCommunicationCommandRequest(string subject = default(string), int submissionTypeId = default(int), int countryId = default(int), DateTime dateOfCommunication = default(DateTime), List<ApplicationModel> applications = default(List<ApplicationModel>), CreateCommentCommandRequest comment = default(CreateCommentCommandRequest))
        {
            this.Subject = subject;
            this.SubmissionTypeId = submissionTypeId;
            this.CountryId = countryId;
            this.DateOfCommunication = dateOfCommunication;
            this.Applications = applications;
            this.Comment = comment;
        }

        /// <summary>
        /// Gets or Sets Subject
        /// </summary>
        [DataMember(Name = "subject", EmitDefaultValue = true)]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionTypeId
        /// </summary>
        [DataMember(Name = "submissionTypeId", EmitDefaultValue = false)]
        public int SubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or Sets CountryId
        /// </summary>
        [DataMember(Name = "countryId", EmitDefaultValue = false)]
        public int CountryId { get; set; }

        /// <summary>
        /// Gets or Sets DateOfCommunication
        /// </summary>
        [DataMember(Name = "dateOfCommunication", EmitDefaultValue = false)]
        public DateTime DateOfCommunication { get; set; }

        /// <summary>
        /// Gets or Sets Applications
        /// </summary>
        [DataMember(Name = "applications", EmitDefaultValue = true)]
        public List<ApplicationModel> Applications { get; set; }

        /// <summary>
        /// Gets or Sets Comment
        /// </summary>
        [DataMember(Name = "comment", EmitDefaultValue = false)]
        public CreateCommentCommandRequest Comment { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CreateCommunicationCommandRequest {\n");
            sb.Append("  Subject: ").Append(Subject).Append("\n");
            sb.Append("  SubmissionTypeId: ").Append(SubmissionTypeId).Append("\n");
            sb.Append("  CountryId: ").Append(CountryId).Append("\n");
            sb.Append("  DateOfCommunication: ").Append(DateOfCommunication).Append("\n");
            sb.Append("  Applications: ").Append(Applications).Append("\n");
            sb.Append("  Comment: ").Append(Comment).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
