﻿using Axon.HAComms.Domain.Entities.Base;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Domain.Entities;

public class Product : MultiTenantEntity
{
    public Product()
    {
    }

    public Product(int id)
    {
        this.Id = id;
    }

    /// <summary>
    /// Name
    /// </summary>
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// IsActive
    /// </summary>
    public bool IsActive { get; set; }

    public int? ExternalId { get; set; }

    public ICollection<DrugSubstance> DrugSubstances { get; set; } = [];

    public ICollection<DrugSubstanceDrugProduct> DrugSubstanceProducts { get; set; } = [];

    public ICollection<ProductType> ProductTypes { get; set; } = new HashSet<ProductType>();

    public ICollection<ProductExtension> ProductExtensions { get; set; } = new List<ProductExtension>();

    public ICollection<ProductProductTypes> ProductProductTypes { get; set; } = new HashSet<ProductProductTypes>();

    public void AddProductExtensions(params ProductExtension[] productExtensions)
    {
        foreach (var productExtension in productExtensions)
        {
            ProductExtensions.Add(productExtension);
        }
    }

    public void UpdateDrugSubstances(List<DrugSubstance> substances)
    {
        DrugSubstances.Clear();

        foreach (var substance in substances)
        {
            DrugSubstances.Add(substance);
        }
    }
}
