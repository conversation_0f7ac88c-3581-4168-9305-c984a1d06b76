﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateCommentIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateComment_NonGenericComment_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })            
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        comments.Should().Contain(comment => comment.Id == createCommentResponse.Id);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();
        createdComment.ProductExtensions.Count.Should().Be(request.ProductExtensions.Count);
        createdComment.ProductExtensions[0].Id.Should().Be(request.ProductExtensions[0].ProductExtensionId);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Id));
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Name));
        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
        createdComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Name));
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Code).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Code));
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_CommentWithMultipleProductExtensions_ReturnsOk()
    {
        //Arrange
        var product = await ApiTestHelper.CreateProductForTest(dbContext, productApi, numberOfExtensions: 3);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product, dbContext);

        var productExtension1 = product.ProductExtensions[0];
        var productExtension2 = product.ProductExtensions[1];

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var productExtensionsList = new List<ProductExtensionCommentModel>
        {
            new() { ProductExtensionId = productExtension1.Id, RouteOfAdministrationIds = productExtension1.RoutesOfAdministration.Take(1).Select(r => r.Id).ToList() },
            new() { ProductExtensionId = productExtension2.Id, RouteOfAdministrationIds = productExtension2.RoutesOfAdministration.Select(r => r.Id).ToList() }
        };

        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(productExtensionsList.ToArray())
            .WithDrugSubstanceIds(product.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        comments.Should().Contain(comment => comment.Id == createCommentResponse.Id);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();

        createdComment.ProductExtensions.Count.Should().Be(request.ProductExtensions.Count);

        createdComment.ProductExtensions[0].Id.Should().Be(productExtension1.Id);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(productExtensionsList[0].RouteOfAdministrationIds);
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(
            productExtension1.RoutesOfAdministration.Where(roa => request.ProductExtensions[0].RouteOfAdministrationIds.Contains(roa.Id)).Select(r => r.Name)
          );

        createdComment.ProductExtensions[1].Id.Should().Be(productExtension2.Id);
        createdComment.ProductExtensions[1].RouteOfAdministrationIds.Should().BeEquivalentTo(productExtension2.RoutesOfAdministration.Select(r => r.Id));
        createdComment.ProductExtensions[1].RouteOfAdministrations.Select(r => r.Name).Should()
            .BeEquivalentTo(productExtension2.RoutesOfAdministration.Select(r => r.Name));

        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
        createdComment.DrugSubstances.Count.Should().Be(product.DrugSubstances.Count);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product.DrugSubstances.Select(ds => ds.Name));
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Code).Should().BeEquivalentTo(product.DrugSubstances.Select(ds => ds.Code));
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_CommentWithMultipleProductExtensions_WithProductExtensionFromDifferentProduct_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, numberOfExtensions: 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension1 = product1.ProductExtensions[0];
        var productExtension2 = product2.ProductExtensions[0];

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var productExtensionsList = new List<ProductExtensionCommentModel>
        {
            new() { ProductExtensionId = productExtension1.Id, RouteOfAdministrationIds = productExtension1.RoutesOfAdministration.Select(r => r.Id).ToList() },
            new() { ProductExtensionId = productExtension2.Id, RouteOfAdministrationIds = productExtension2.RoutesOfAdministration.Select(r => r.Id).ToList() }
        };

        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(productExtensionsList.ToArray())
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () =>  commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Product Extensions per comment must be for the same product.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeTrue();
        createdComment.Question.Should().Be(request.Question);
        createdComment.Response.Should().Be(request.Response);
        createdComment.Description.Should().BeNull();
        createdComment.ProductExtensions.Should().NotBeNullOrEmpty();
        createdComment.ProductExtensions.Count.Should().Be(1);
        createdComment.ProductExtensions[0].Id.Should().Be(request.ProductExtensions[0].ProductExtensionId);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r=>r.Id));
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should()
            .BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Name));
        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
        createdComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Name));
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Code).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Code));
    }

    [Fact]
    public async Task CreateComment_GenericComment_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(1);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeTrue();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();
        createdComment.ProductExtensions.Should().BeNullOrEmpty();
        createdComment.DrugSubstances.Should().BeEmpty();
        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
    }

    [Fact]
    public async Task CreateComment_GenericComment_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var createdComment = getCommentsResponse.Data.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeTrue();
        createdComment.IsQuestionIncluded.Should().BeTrue();
        createdComment.Question.Should().Be(request.Question);
        createdComment.Response.Should().Be(request.Response);
        createdComment.Description.Should().BeNull();
        createdComment.ProductExtensions.Should().BeNullOrEmpty();
        createdComment.DrugSubstances.Should().BeEmpty();
        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
    }

    [Fact]
    public async Task CreateComment_InvalidCommunication_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(Fake.Communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Communication\\\" ({request.CommunicationId}) was not found.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithInvalidProductExtension_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var invalidProductExtensionId = product1.ProductExtensions.Max(pe => pe.Id) + int.MaxValue;
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = invalidProductExtensionId, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should()
            .Contain($"Entity \\\"ProductExtension\\\" ({string.Join(", ", request.ProductExtensions.Select(pe => pe.ProductExtensionId))}) was not found.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithDuplicateProductExtensions_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
                })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Product Extensions must be unique.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithUniqueProductExtensions_ReturnsOk()
    {
        //Arrange
        var product = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product, dbContext);

        var productExtension1 = product.ProductExtensions[0];
        var productExtension2 = product.ProductExtensions[^1];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtension1.Id, RouteOfAdministrationIds = productExtension1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtension2.Id, RouteOfAdministrationIds = productExtension2.RoutesOfAdministration.Select(r => r.Id).ToList()
                })
            .WithDrugSubstanceIds(product.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();
        createdComment.ProductExtensions.Should().NotBeNullOrEmpty();
        createdComment.ProductExtensions.Count.Should().Be(2);
        createdComment.ProductExtensions[0].Id.Should().Be(request.ProductExtensions[0].ProductExtensionId);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(request.ProductExtensions[0].RouteOfAdministrationIds);
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(
            productExtension1.RoutesOfAdministration.Where(roa => request.ProductExtensions[0].RouteOfAdministrationIds.Contains(roa.Id)).Select(r => r.Name));

        createdComment.ProductExtensions[1].Id.Should().Be(request.ProductExtensions[1].ProductExtensionId);
        createdComment.ProductExtensions[1].RouteOfAdministrationIds.Should().BeEquivalentTo(request.ProductExtensions[1].RouteOfAdministrationIds);
        createdComment.ProductExtensions[1].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(
            productExtension2.RoutesOfAdministration.Where(roa => request.ProductExtensions[1].RouteOfAdministrationIds.Contains(roa.Id)).Select(r => r.Name));

        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.DrugSubstances.Count.Should().Be(product.DrugSubstances.Count);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product.DrugSubstances.Select(ds => ds.Name));
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Code).Should().BeEquivalentTo(product.DrugSubstances.Select(ds => ds.Code));
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithSubsetOfRouteOfAdministration_ReturnsOk()
    {
        //Arrange
        var testRouteOfAdministrationEntities = await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext,
            productApi,
            2,
            routeOfAdministrationList: testRouteOfAdministrationEntities);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Take(2).Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();
        createdComment.ProductExtensions.Should().NotBeNullOrEmpty();
        createdComment.ProductExtensions.Count.Should().Be(1);
        createdComment.ProductExtensions[0].Id.Should().Be(request.ProductExtensions[0].ProductExtensionId);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(request.ProductExtensions[0].RouteOfAdministrationIds);
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(
            productExtension.RoutesOfAdministration.Where(roa => request.ProductExtensions[0].RouteOfAdministrationIds.Contains(roa.Id)).Select(r => r.Name));

        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Name));
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Code).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Code));
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithoutRouteOfAdministration_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = []})
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Route Of Administration Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithInvalidRouteOfAdministration_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var invalidRouteOfAdministrationId = dbContext.RouteOfAdministrations.Max(r => r.Id) + int.MaxValue;
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = [invalidRouteOfAdministrationId]
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"RouteOfAdministration\\\" ({invalidRouteOfAdministrationId}) was not found.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithRouteOfAdministration_FromDifferentProductExtension_ThrowsException()
    {
        //Arrange
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var testRouteOfAdministrationEntities = await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 2);
        var product1 = await ApiTestHelper.CreateProductForTest(
            dbContext,
            productApi,
            routeOfAdministrationList: [testRouteOfAdministrationEntities[0]]);
        var product1ProductExtension = product1.ProductExtensions[0];
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var product2 = await ApiTestHelper.CreateProductForTest(
            dbContext,
            productApi,
            routeOfAdministrationList: [testRouteOfAdministrationEntities[1]]);
        var product2ProductExtension = product2.ProductExtensions[0];

        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"RouteOfAdministration\\\" ({testRouteOfAdministrationEntities[1].Id}) was not found.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithSubsetOfDrugSubstances_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, numberOfDrugSubstances: 5);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).Skip(1).Take(4).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        var comments = getCommentsResponse.Data;
        comments.Count.Should().Be(2);
        var createdComment = comments.Single(comment => comment.Id == createCommentResponse.Id);
        createdComment.IsGeneralGuidance.Should().BeFalse();
        createdComment.IsQuestionIncluded.Should().BeFalse();
        createdComment.Description.Should().Be(request.Description);
        createdComment.Question.Should().BeNull();
        createdComment.Response.Should().BeNull();
        createdComment.ProductExtensions.Should().NotBeNullOrEmpty();
        createdComment.ProductExtensions.Count.Should().Be(1);
        createdComment.ProductExtensions[0].Id.Should().Be(request.ProductExtensions[0].ProductExtensionId);
        createdComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Id));
        createdComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Name));
        createdComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(request.TagIds);
        createdComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(request.DrugSubstanceIds);
        createdComment.DrugSubstances.Select(x => x.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Name).Skip(1).Take(4).ToList());
        createdComment.DrugSubstances.Select(x => x.Code).Should().BeEquivalentTo(product1.DrugSubstances.Select(ds => ds.Code).Skip(1).Take(4).ToList());
        createdComment.BirdsLinkToBIResponse.Should().Be(request.BirdsLinkToBIResponse);
        createdComment.BirdsLinkToBISAMP.Should().Be(request.BirdsLinkToBISAMP);
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithoutDrugSubstance_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds()
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Drug Substance Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithInvalidDrugSubstance_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var invalidDrugSubstanceId = dbContext.DrugSubstances.Max(ds => ds.Id) + int.MaxValue;
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(invalidDrugSubstanceId)
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"DrugSubstance\\\" ({invalidDrugSubstanceId}) was not found.");
    }

    [Fact]
    public async Task CreateComment_NonGenericComment_WithDrugSubstance_FromDifferentProduct_ThrowsException()
    {
        //Arrange
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product1.ProductExtensions[0];
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id)
                    .ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        var invalidDrugSubstanceIds = string.Join(",", product2.DrugSubstances.Select(s => s.Id).ToArray());
        exception.And.Message.Should().Contain($"Entity \\\"DrugSubstance\\\" ({invalidDrugSubstanceIds}) was not found.");
    }

    [Fact]
    public async Task CreateComment_WithoutTags_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id,
                RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id)
                    .ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds().Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Tag Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateComment_WithInvalidTag_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var generatedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);
        var invalidTagId = generatedTags.Max(r => r.Id) + int.MaxValue;
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(invalidTagId).Build();

        //Act
        var commentResponse = () => commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await commentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Tag\\\" ({invalidTagId}) was not found.");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }
}
