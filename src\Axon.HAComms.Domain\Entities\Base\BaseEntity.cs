﻿namespace Axon.HAComms.Domain.Entities.Base;

public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string LastUpdatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsDeleted { get; set; }

    public virtual void Delete()
    {
        IsDeleted = true;
    }
}
