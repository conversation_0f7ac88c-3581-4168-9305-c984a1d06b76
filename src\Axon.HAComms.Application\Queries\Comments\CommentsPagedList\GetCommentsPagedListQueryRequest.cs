﻿using Axon.HAComms.Application.Models.Comments;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Comments.CommentsPagedList
{
    public class GetCommentsPagedListQueryRequest : IRequest<ApiPagedListResult<CommentDtoModel>>
    {
        public int CommunicationId { get; set; }
        public int? ProductId { get; }
        public int? ExcludedCommentId { get; }
        public int Skip { get; }
        public int Take { get; }

        public GetCommentsPagedListQueryRequest(int communicationId, int? productId, int? excludedCommentId, int skip, int take)
        {
            CommunicationId = communicationId;
            ProductId = productId;
            ExcludedCommentId = excludedCommentId;
            Skip = skip;
            Take = take;
        }
    }
}
