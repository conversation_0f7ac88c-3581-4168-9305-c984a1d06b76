﻿using Axon.HAComms.Application.Models.ProductExtensions;

namespace Axon.HAComms.Application.Models.Comments;

public class CommentRequestModel
{
    public int CommunicationId { get; set; }

    public string? Description { get; set; }

    public string? Question { get; set; }

    public string? Response { get; set; }

    public string? BIRDSLinkToBIResponse { get; set; }

    public string? BIRDSLinkToBISAMP { get; set; }

    public IList<ProductExtensionCommentModel>? ProductExtensions { get; set; }    

    public IList<int>? DrugSubstanceIds { get; set; }

    public IList<int> TagIds { get; set; } = [];

    public bool IsGeneralGuidance { get; set; }

    public bool IsQuestionIncluded { get; set; }
}
