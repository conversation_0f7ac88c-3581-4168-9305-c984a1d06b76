IF EXISTS(SELECT 1 FROM sys.views WHERE NAME='SearchInComments' and TYPE='v')
DROP VIEW SearchInComments;
GO
CREATE VIEW [dbo].[SearchInComments] AS
SELECT c.[Id],
    c.[Description],
    c.[Question],
    c.[Response],
    c.[IsGeneralGuidance],
    c.[IsQuestionIncluded],
    c.[BIRDSLinkToBIResponse],
    c.[BIRDSLinkToBISAMP],
    c.[Tenant],
    pe.[ProductId],
    pe.[PCID] AS ProductCode,
    dp.[Name] AS ProductName,
    dsra.[DrugSubstanceIds],
    dsra.[RouteOfAdministrationIds],
    dsra.[DrugSubstanceNames],
    dsra.[TagIds],
    dsra.[TagNames],
    dsra.[ProductTypeIds],
    dsra.[ProductTypeNames],
    pe.[DosageFormId],
    CommAppSub.[SubmissionTypeId],
    CommAppSub.[CommunicationId],
    CommAppSub.[DateOfCommunication],
    CommAppSub.[IsCompleted],
    CommAppSub.[CountryId] AS CountryId,
    CommAppSub.[Subject],
    country.[Name] AS CountryName,
    df.[Name] AS DosageFormName,
    dsra.[RouteOfAdministrationNames],
    st.[Name] AS SubmissionTypeName,
    CommAppSub.[ApplicationIds],
    CommAppSub.[SubmissionIds],
    LOWER(CommAppSub.[ApplicationNumbers]) AS ApplicationNumbers,
    LOWER(CommAppSub.[SubmissionNumbers]) AS SubmissionNumbers
FROM (
        SELECT ct.CommentId,
            gcds.DrugSubstanceIds,
            gra.RouteOfAdministrationIds,
            gpt.ProductTypeIds,
            gcds.DrugSubstanceNames,
            gra.RouteOfAdministrationNames,
            ct.TagIds,
            ct.TagNames,
            gpt.ProductTypeNames
        FROM (
                SELECT cds.CommentId,
                    '[' + STRING_AGG(ds.Id, ',') + ']' AS DrugSubstanceIds,
                    '[' + STRING_AGG('"' + ds.Name + '|' + ds.Code + '"', ', ') + ']' AS DrugSubstanceNames
                FROM [dbo].[DrugSubstances] ds
                    JOIN [dbo].[CommentDrugSubstances] cds ON cds.[DrugSubstanceId] = ds.[Id]
                GROUP BY cds.[CommentId]
            ) gcds
            JOIN (
                SELECT cra.CommentId,
                    '[' + STRING_AGG(cra.RouteOfAdministrationId, ',') + ']' AS RouteOfAdministrationIds,
                    STRING_AGG(ra.Name, ', ') AS RouteOfAdministrationNames
                FROM [dbo].[RouteOfAdministrations] ra
                    JOIN [dbo].[CommentRoutesOfAdministration] AS cra ON ra.Id = cra.RouteOfAdministrationId
                GROUP BY cra.[CommentId]
            ) AS gra ON gra.[CommentId] = gcds.[CommentId]
            JOIN (
                SELECT c.[Id],
                    '[' + STRING_AGG(pt.Id, ',') + ']' AS ProductTypeIds,
                    STRING_AGG(pt.Name, ', ') AS ProductTypeNames
                FROM [dbo].[ProductTypes] pt
                    JOIN [dbo].[ProductProductTypes] AS ppt ON pt.Id = ppt.ProductTypeId
                    JOIN [dbo].[ProductExtensions] as pe on pe.[ProductId] = ppt.[ProductId]
                    JOIN [dbo].[Comments] as c on c.[ProductExtensionId] = pe.[Id]
                GROUP BY c.[Id]
            ) AS gpt ON gpt.[Id] = gcds.[CommentId]
            JOIN (
                SELECT ct.CommentId,
                    '[' + STRING_AGG(ct.TagId, ',') + ']' AS TagIds,
                    STRING_AGG(t.Name, ', ') AS TagNames
                FROM [dbo].[Tags] as t
                    JOIN [dbo].[CommentTags] AS ct ON t.Id = ct.TagId
                GROUP BY ct.CommentId
            ) AS ct ON ct.[CommentId] = gcds.[CommentId]
    ) AS dsra
    RIGHT JOIN [dbo].[Comments] AS c ON c.[Id] = dsra.[CommentId]
    LEFT JOIN [dbo].[ProductExtensions] pe on pe.[Id] = c.[ProductExtensionId]
    LEFT JOIN [dbo].[DrugProducts] AS dp ON pe.[ProductId] = dp.[Id]
    LEFT JOIN [dbo].[DosageForms] AS df ON pe.[DosageFormId] = df.[Id]
    JOIN (
        SELECT comm.SubmissionTypeId,
            comm.Id AS CommunicationId,
            comm.DateOfCommunication,
            comm.IsCompleted,
            comm.CountryId AS CountryId,
            comm.Subject,
            '[' + STRING_AGG(a.Id, ',') + ']' AS ApplicationIds,
            '[' + STRING_AGG(sub.Id, ',') + ']' AS SubmissionIds,
            '["' + STRING_AGG(a.Number, '", "') + '"]' AS ApplicationNumbers,
            '["' + STRING_AGG(sub.Number, '", "') + '"]' AS SubmissionNumbers
        FROM [dbo].[Communications] comm
            LEFT JOIN [dbo].[Applications] AS a ON a.CommunicationId = comm.Id
            LEFT JOIN [dbo].[Submissions] AS sub ON sub.ApplicationId = a.Id
        GROUP BY comm.SubmissionTypeId,
            comm.Id,
            comm.DateOfCommunication,
            comm.IsCompleted,
            comm.CountryId,
            comm.Subject
    ) AS CommAppSub ON CommAppSub.CommunicationId = c.CommunicationId
    JOIN [dbo].[Countries] AS country ON CommAppSub.CountryId = country.Id
    JOIN [dbo].[SubmissionTypes] AS st ON st.Id = CommAppSub.SubmissionTypeId
