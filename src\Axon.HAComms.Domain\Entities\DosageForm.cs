﻿using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Interfaces;

namespace Axon.HAComms.Domain.Entities;

public class DosageForm : MultiTenantEntity, IEntityWithName
{
    public DosageForm()
    {
    }

    public DosageForm(int id)
    {
        this.Id = id;
    }

    public string Name { get; set; } = string.Empty;

    public int? ExternalId { get; set; }

    public ICollection<ProductExtension> ProductExtensions { get; set; } = new HashSet<ProductExtension>();
}
