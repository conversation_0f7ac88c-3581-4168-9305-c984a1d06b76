﻿using Axon.HAComms.Application.Common.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using ApplicationEntity = Axon.HAComms.Domain.Entities.Application;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class ApplicationsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<ApplicationsRepository> logger)
    : SqlServerRepository<ApplicationEntity>(context, tenant, logger), IApplicationsRepository
{
    public async Task<IEnumerable<string>> GetNumbersAsync()
    {
        return await context
            .Set<ApplicationEntity>()
            .OrderBy(x => x.Number)
            .Select(x => x.Number)
            .Distinct()
            .AsNoTracking()
            .ToListAsync();
    }
}
