﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Tags.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Tags;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Tags.ListQuery;

public class GetTagListQueryHandlerTests
{
    private readonly GetTagListQueryHandler handler;
    private readonly ITagRepository tagRepository;

    public GetTagListQueryHandlerTests()
    {
        tagRepository = Substitute.For<ITagRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new TagMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetTagListQueryHandler(tagRepository, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = new List<Tag>
        {
            new TagBuilder().Build(),
            new TagBuilder().Build(),
            new TagBuilder().Build()
        };

        tagRepository.GetItemsAsync().Returns(queryableItems);

        var request = new GetTagListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        var tagName1 = "test1";
        var tagName2 = "test2";
        var tagName3 = "test3";

        //Arrange
        var queryableItems = new List<Tag>
        {
            new TagBuilder().WithName(tagName3).Build(),
            new TagBuilder().WithName(tagName2).Build(),
            new TagBuilder().WithName(tagName1).Build()
        };

        tagRepository.GetItemsAsync().Returns(queryableItems);

        var request = new GetTagListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data[0].Name.Should().Be(tagName1);
        result.Data[1].Name.Should().Be(tagName2);
        result.Data[2].Name.Should().Be(tagName3);
    }
}
