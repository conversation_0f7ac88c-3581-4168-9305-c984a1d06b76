﻿using AutoMapper;
using Axon.HAComms.Application.Models.Application;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class ApplicationsMappingProfile : Profile
    {
        public ApplicationsMappingProfile()
        {
            CreateMap<ApplicationModel, Domain.Entities.Application>()
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.Communication, o => o.Ignore())
                .ForMember(dest => dest.CommunicationId, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<Domain.Entities.Application, ApplicationModel>();
        }
    }
}
