/* tslint:disable */
/* eslint-disable */
/**
 * Axon.HAComms.Api
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface ApplicationModel
 */
export interface ApplicationModel {
    /**
     * 
     * @type {number}
     * @memberof ApplicationModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ApplicationModel
     */
    'number'?: string | null;
    /**
     * 
     * @type {Array<SubmissionModel>}
     * @memberof ApplicationModel
     */
    'submissions'?: Array<SubmissionModel> | null;
}
/**
 * 
 * @export
 * @interface CommentDtoModel
 */
export interface CommentDtoModel {
    /**
     * 
     * @type {number}
     * @memberof CommentDtoModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'question'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'response'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'birdsLinkToBIResponse'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'birdsLinkToBISAMP'?: string | null;
    /**
     * 
     * @type {Array<ProductExtensionModel>}
     * @memberof CommentDtoModel
     */
    'productExtensions'?: Array<ProductExtensionModel> | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'productName'?: string | null;
    /**
     * 
     * @type {Array<DrugSubstanceModel>}
     * @memberof CommentDtoModel
     */
    'drugSubstances'?: Array<DrugSubstanceModel> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof CommentDtoModel
     */
    'productTypes'?: Array<string> | null;
    /**
     * 
     * @type {Array<TagModel>}
     * @memberof CommentDtoModel
     */
    'tags'?: Array<TagModel> | null;
    /**
     * 
     * @type {boolean}
     * @memberof CommentDtoModel
     */
    'isGeneralGuidance'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof CommentDtoModel
     */
    'isQuestionIncluded'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'createdDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'lastUpdatedDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommentDtoModel
     */
    'lastUpdatedBy'?: string | null;
}
/**
 * 
 * @export
 * @interface CommentDtoModelApiPagedListResult
 */
export interface CommentDtoModelApiPagedListResult {
    /**
     * 
     * @type {Array<CommentDtoModel>}
     * @memberof CommentDtoModelApiPagedListResult
     */
    'data'?: Array<CommentDtoModel> | null;
    /**
     * 
     * @type {CommentDtoModelPagingInfo}
     * @memberof CommentDtoModelApiPagedListResult
     */
    'paging'?: CommentDtoModelPagingInfo;
}
/**
 * 
 * @export
 * @interface CommentDtoModelPagingInfo
 */
export interface CommentDtoModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof CommentDtoModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommentDtoModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommentDtoModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface CommunicationModel
 */
export interface CommunicationModel {
    /**
     * 
     * @type {number}
     * @memberof CommunicationModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'subject'?: string | null;
    /**
     * 
     * @type {CountryModel}
     * @memberof CommunicationModel
     */
    'country'?: CountryModel;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'dateOfCommunication'?: string;
    /**
     * 
     * @type {SubmissionTypeModel}
     * @memberof CommunicationModel
     */
    'submissionType'?: SubmissionTypeModel;
    /**
     * 
     * @type {Array<ApplicationModel>}
     * @memberof CommunicationModel
     */
    'applications'?: Array<ApplicationModel> | null;
    /**
     * 
     * @type {boolean}
     * @memberof CommunicationModel
     */
    'isCompleted'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'lastUpdatedBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'createdDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'lastUpdatedDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommunicationModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CommunicationModel
     */
    'generalGuidanceCommentsCount'?: number;
    /**
     * 
     * @type {Array<ProductDtoModel>}
     * @memberof CommunicationModel
     */
    'products'?: Array<ProductDtoModel> | null;
}
/**
 * 
 * @export
 * @interface CommunicationPagedListModel
 */
export interface CommunicationPagedListModel {
    /**
     * 
     * @type {number}
     * @memberof CommunicationPagedListModel
     */
    'id': number;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'subject'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'productNames'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CommunicationPagedListModel
     */
    'countryId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'countryName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'dateOfCommunication'?: string;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'createdDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CommunicationPagedListModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof CommunicationPagedListModel
     */
    'isCompleted'?: boolean;
}
/**
 * 
 * @export
 * @interface CommunicationPagedListModelApiPagedListResult
 */
export interface CommunicationPagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<CommunicationPagedListModel>}
     * @memberof CommunicationPagedListModelApiPagedListResult
     */
    'data'?: Array<CommunicationPagedListModel> | null;
    /**
     * 
     * @type {CommunicationPagedListModelPagingInfo}
     * @memberof CommunicationPagedListModelApiPagedListResult
     */
    'paging'?: CommunicationPagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface CommunicationPagedListModelPagingInfo
 */
export interface CommunicationPagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof CommunicationPagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommunicationPagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof CommunicationPagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface CountryModel
 */
export interface CountryModel {
    /**
     * 
     * @type {number}
     * @memberof CountryModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CountryModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface CountryModelApiListResult
 */
export interface CountryModelApiListResult {
    /**
     * 
     * @type {Array<CountryModel>}
     * @memberof CountryModelApiListResult
     */
    'data'?: Array<CountryModel> | null;
}
/**
 * 
 * @export
 * @interface CreateCommentCommandRequest
 */
export interface CreateCommentCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof CreateCommentCommandRequest
     */
    'communicationId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandRequest
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandRequest
     */
    'question'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandRequest
     */
    'response'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandRequest
     */
    'birdsLinkToBIResponse'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandRequest
     */
    'birdsLinkToBISAMP'?: string | null;
    /**
     * 
     * @type {Array<ProductExtensionCommentModel>}
     * @memberof CreateCommentCommandRequest
     */
    'productExtensions'?: Array<ProductExtensionCommentModel> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof CreateCommentCommandRequest
     */
    'drugSubstanceIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof CreateCommentCommandRequest
     */
    'tagIds'?: Array<number> | null;
    /**
     * 
     * @type {boolean}
     * @memberof CreateCommentCommandRequest
     */
    'isGeneralGuidance'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof CreateCommentCommandRequest
     */
    'isQuestionIncluded'?: boolean;
}
/**
 * 
 * @export
 * @interface CreateCommentCommandResponse
 */
export interface CreateCommentCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateCommentCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandResponse
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandResponse
     */
    'createdDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandResponse
     */
    'lastUpdatedBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateCommentCommandResponse
     */
    'lastUpdatedDate'?: string;
}
/**
 * 
 * @export
 * @interface CreateCommunicationCommandRequest
 */
export interface CreateCommunicationCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateCommunicationCommandRequest
     */
    'subject'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof CreateCommunicationCommandRequest
     */
    'submissionTypeId'?: number;
    /**
     * 
     * @type {number}
     * @memberof CreateCommunicationCommandRequest
     */
    'countryId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateCommunicationCommandRequest
     */
    'dateOfCommunication'?: string;
    /**
     * 
     * @type {Array<ApplicationModel>}
     * @memberof CreateCommunicationCommandRequest
     */
    'applications'?: Array<ApplicationModel> | null;
    /**
     * 
     * @type {CreateCommentCommandRequest}
     * @memberof CreateCommunicationCommandRequest
     */
    'comment'?: CreateCommentCommandRequest;
}
/**
 * 
 * @export
 * @interface CreateCommunicationCommandResponse
 */
export interface CreateCommunicationCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateCommunicationCommandResponse
     */
    'id'?: number;
}
/**
 * 
 * @export
 * @interface CreateDosageFormCommandRequest
 */
export interface CreateDosageFormCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateDosageFormCommandRequest
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateDosageFormCommandResponse
 */
export interface CreateDosageFormCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateDosageFormCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateDosageFormCommandResponse
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateDrugSubstanceCommandRequest
 */
export interface CreateDrugSubstanceCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandRequest
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandRequest
     */
    'code': string;
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandRequest
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateDrugSubstanceCommandResponse
 */
export interface CreateDrugSubstanceCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateDrugSubstanceCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandResponse
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandResponse
     */
    'code'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateDrugSubstanceCommandResponse
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateProductCommandRequest
 */
export interface CreateProductCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateProductCommandRequest
     */
    'name': string;
    /**
     * 
     * @type {boolean}
     * @memberof CreateProductCommandRequest
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {Array<number>}
     * @memberof CreateProductCommandRequest
     */
    'productTypeIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof CreateProductCommandRequest
     */
    'drugSubstanceIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<ProductExtensionModel>}
     * @memberof CreateProductCommandRequest
     */
    'productExtensions'?: Array<ProductExtensionModel> | null;
    /**
     * 
     * @type {string}
     * @memberof CreateProductCommandRequest
     */
    'notAssigned'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateProductCommandResponse
 */
export interface CreateProductCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateProductCommandResponse
     */
    'id'?: number;
}
/**
 * 
 * @export
 * @interface CreateRouteOfAdministrationCommandRequest
 */
export interface CreateRouteOfAdministrationCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateRouteOfAdministrationCommandRequest
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface CreateRouteOfAdministrationCommandResponse
 */
export interface CreateRouteOfAdministrationCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateRouteOfAdministrationCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateRouteOfAdministrationCommandResponse
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateTagCommandRequest
 */
export interface CreateTagCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateTagCommandRequest
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateTagCommandRequest
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface CreateTagCommandResponse
 */
export interface CreateTagCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof CreateTagCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof CreateTagCommandResponse
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateTagCommandResponse
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface DeleteCommentsByProductCommandRequest
 */
export interface DeleteCommentsByProductCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof DeleteCommentsByProductCommandRequest
     */
    'communicationId'?: number;
    /**
     * 
     * @type {number}
     * @memberof DeleteCommentsByProductCommandRequest
     */
    'productId'?: number;
}
/**
 * 
 * @export
 * @interface DosageFormModel
 */
export interface DosageFormModel {
    /**
     * 
     * @type {number}
     * @memberof DosageFormModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DosageFormModel
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface DosageFormModelApiListResult
 */
export interface DosageFormModelApiListResult {
    /**
     * 
     * @type {Array<DosageFormModel>}
     * @memberof DosageFormModelApiListResult
     */
    'data'?: Array<DosageFormModel> | null;
}
/**
 * 
 * @export
 * @interface DosageFormPagedListModel
 */
export interface DosageFormPagedListModel {
    /**
     * 
     * @type {number}
     * @memberof DosageFormPagedListModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DosageFormPagedListModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof DosageFormPagedListModel
     */
    'isAssociatedToProduct'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DosageFormPagedListModel
     */
    'createdDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DosageFormPagedListModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DosageFormPagedListModel
     */
    'lastUpdatedDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DosageFormPagedListModel
     */
    'lastUpdatedBy'?: string | null;
}
/**
 * 
 * @export
 * @interface DosageFormPagedListModelApiPagedListResult
 */
export interface DosageFormPagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<DosageFormPagedListModel>}
     * @memberof DosageFormPagedListModelApiPagedListResult
     */
    'data'?: Array<DosageFormPagedListModel> | null;
    /**
     * 
     * @type {DosageFormPagedListModelPagingInfo}
     * @memberof DosageFormPagedListModelApiPagedListResult
     */
    'paging'?: DosageFormPagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface DosageFormPagedListModelPagingInfo
 */
export interface DosageFormPagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof DosageFormPagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof DosageFormPagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof DosageFormPagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface DrugSubstanceCodeAndName
 */
export interface DrugSubstanceCodeAndName {
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceCodeAndName
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceCodeAndName
     */
    'code'?: string | null;
}
/**
 * 
 * @export
 * @interface DrugSubstanceDtoModel
 */
export interface DrugSubstanceDtoModel {
    /**
     * 
     * @type {number}
     * @memberof DrugSubstanceDtoModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceDtoModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface DrugSubstanceModel
 */
export interface DrugSubstanceModel {
    /**
     * 
     * @type {number}
     * @memberof DrugSubstanceModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceModel
     */
    'code': string;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstanceModel
     */
    'description'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof DrugSubstanceModel
     */
    'isAssociatedToComment'?: boolean;
}
/**
 * 
 * @export
 * @interface DrugSubstanceModelApiListResult
 */
export interface DrugSubstanceModelApiListResult {
    /**
     * 
     * @type {Array<DrugSubstanceModel>}
     * @memberof DrugSubstanceModelApiListResult
     */
    'data'?: Array<DrugSubstanceModel> | null;
}
/**
 * 
 * @export
 * @interface DrugSubstancePagedListModel
 */
export interface DrugSubstancePagedListModel {
    /**
     * 
     * @type {number}
     * @memberof DrugSubstancePagedListModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'code'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'description'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof DrugSubstancePagedListModel
     */
    'isAssociatedToComment'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'createdDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'lastUpdatedDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof DrugSubstancePagedListModel
     */
    'lastUpdatedBy'?: string | null;
}
/**
 * 
 * @export
 * @interface DrugSubstancePagedListModelApiPagedListResult
 */
export interface DrugSubstancePagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<DrugSubstancePagedListModel>}
     * @memberof DrugSubstancePagedListModelApiPagedListResult
     */
    'data'?: Array<DrugSubstancePagedListModel> | null;
    /**
     * 
     * @type {DrugSubstancePagedListModelPagingInfo}
     * @memberof DrugSubstancePagedListModelApiPagedListResult
     */
    'paging'?: DrugSubstancePagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface DrugSubstancePagedListModelPagingInfo
 */
export interface DrugSubstancePagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof DrugSubstancePagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof DrugSubstancePagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof DrugSubstancePagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface ErrorResult
 */
export interface ErrorResult {
    /**
     * 
     * @type {string}
     * @memberof ErrorResult
     */
    'errorType'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ErrorResult
     */
    'message'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ErrorResult
     */
    'exception'?: string | null;
}
/**
 * 
 * @export
 * @interface GetEventConstantsQueryResponse
 */
export interface GetEventConstantsQueryResponse {
    /**
     * 
     * @type {Array<string>}
     * @memberof GetEventConstantsQueryResponse
     */
    'eventCategories'?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof GetEventConstantsQueryResponse
     */
    'eventTypes'?: Array<string> | null;
}
/**
 * 
 * @export
 * @interface ProductDtoModel
 */
export interface ProductDtoModel {
    /**
     * 
     * @type {number}
     * @memberof ProductDtoModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductDtoModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface ProductExtensionCommentModel
 */
export interface ProductExtensionCommentModel {
    /**
     * 
     * @type {number}
     * @memberof ProductExtensionCommentModel
     */
    'productExtensionId'?: number;
    /**
     * 
     * @type {Array<number>}
     * @memberof ProductExtensionCommentModel
     */
    'routeOfAdministrationIds'?: Array<number> | null;
}
/**
 * 
 * @export
 * @interface ProductExtensionModel
 */
export interface ProductExtensionModel {
    /**
     * 
     * @type {number}
     * @memberof ProductExtensionModel
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductExtensionModel
     */
    'productId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductExtensionModel
     */
    'pcid'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof ProductExtensionModel
     */
    'dosageFormId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductExtensionModel
     */
    'dosageFormName'?: string | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof ProductExtensionModel
     */
    'routeOfAdministrationIds'?: Array<number> | null;
    /**
     * 
     * @type {boolean}
     * @memberof ProductExtensionModel
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {Array<RouteOfAdministrationModel>}
     * @memberof ProductExtensionModel
     */
    'routeOfAdministrations'?: Array<RouteOfAdministrationModel> | null;
}
/**
 * 
 * @export
 * @interface ProductExtensionResponseModel
 */
export interface ProductExtensionResponseModel {
    /**
     * 
     * @type {number}
     * @memberof ProductExtensionResponseModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductExtensionResponseModel
     */
    'pcid'?: string | null;
    /**
     * 
     * @type {DosageFormModel}
     * @memberof ProductExtensionResponseModel
     */
    'dosageForm'?: DosageFormModel;
    /**
     * 
     * @type {Array<RouteOfAdministrationModel>}
     * @memberof ProductExtensionResponseModel
     */
    'routesOfAdministration'?: Array<RouteOfAdministrationModel> | null;
    /**
     * 
     * @type {boolean}
     * @memberof ProductExtensionResponseModel
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ProductExtensionResponseModel
     */
    'isAssociatedToComment'?: boolean;
}
/**
 * 
 * @export
 * @interface ProductModel
 */
export interface ProductModel {
    /**
     * 
     * @type {number}
     * @memberof ProductModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductModel
     */
    'name': string;
    /**
     * 
     * @type {boolean}
     * @memberof ProductModel
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {Array<DrugSubstanceModel>}
     * @memberof ProductModel
     */
    'drugSubstances'?: Array<DrugSubstanceModel> | null;
    /**
     * 
     * @type {Array<ProductTypeModel>}
     * @memberof ProductModel
     */
    'productTypes'?: Array<ProductTypeModel> | null;
    /**
     * 
     * @type {Array<ProductExtensionResponseModel>}
     * @memberof ProductModel
     */
    'productExtensions'?: Array<ProductExtensionResponseModel> | null;
}
/**
 * 
 * @export
 * @interface ProductModelApiListResult
 */
export interface ProductModelApiListResult {
    /**
     * 
     * @type {Array<ProductModel>}
     * @memberof ProductModelApiListResult
     */
    'data'?: Array<ProductModel> | null;
}
/**
 * 
 * @export
 * @interface ProductPagedListModel
 */
export interface ProductPagedListModel {
    /**
     * 
     * @type {number}
     * @memberof ProductPagedListModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductPagedListModel
     */
    'name': string;
    /**
     * 
     * @type {Array<DrugSubstanceCodeAndName>}
     * @memberof ProductPagedListModel
     */
    'substances'?: Array<DrugSubstanceCodeAndName> | null;
    /**
     * 
     * @type {boolean}
     * @memberof ProductPagedListModel
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {Array<ProductExtensionModel>}
     * @memberof ProductPagedListModel
     */
    'productExtensions'?: Array<ProductExtensionModel> | null;
}
/**
 * 
 * @export
 * @interface ProductPagedListModelApiPagedListResult
 */
export interface ProductPagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<ProductPagedListModel>}
     * @memberof ProductPagedListModelApiPagedListResult
     */
    'data'?: Array<ProductPagedListModel> | null;
    /**
     * 
     * @type {ProductPagedListModelPagingInfo}
     * @memberof ProductPagedListModelApiPagedListResult
     */
    'paging'?: ProductPagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface ProductPagedListModelPagingInfo
 */
export interface ProductPagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof ProductPagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductPagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof ProductPagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface ProductTypeModel
 */
export interface ProductTypeModel {
    /**
     * 
     * @type {number}
     * @memberof ProductTypeModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof ProductTypeModel
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface ProductTypeModelApiListResult
 */
export interface ProductTypeModelApiListResult {
    /**
     * 
     * @type {Array<ProductTypeModel>}
     * @memberof ProductTypeModelApiListResult
     */
    'data'?: Array<ProductTypeModel> | null;
}
/**
 * 
 * @export
 * @interface RouteOfAdministrationModel
 */
export interface RouteOfAdministrationModel {
    /**
     * 
     * @type {number}
     * @memberof RouteOfAdministrationModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationModel
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface RouteOfAdministrationModelApiListResult
 */
export interface RouteOfAdministrationModelApiListResult {
    /**
     * 
     * @type {Array<RouteOfAdministrationModel>}
     * @memberof RouteOfAdministrationModelApiListResult
     */
    'data'?: Array<RouteOfAdministrationModel> | null;
}
/**
 * 
 * @export
 * @interface RouteOfAdministrationPagedListModel
 */
export interface RouteOfAdministrationPagedListModel {
    /**
     * 
     * @type {number}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'isAssociatedToProduct'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'createdDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'lastUpdatedDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RouteOfAdministrationPagedListModel
     */
    'lastUpdatedBy'?: string | null;
}
/**
 * 
 * @export
 * @interface RouteOfAdministrationPagedListModelApiPagedListResult
 */
export interface RouteOfAdministrationPagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<RouteOfAdministrationPagedListModel>}
     * @memberof RouteOfAdministrationPagedListModelApiPagedListResult
     */
    'data'?: Array<RouteOfAdministrationPagedListModel> | null;
    /**
     * 
     * @type {RouteOfAdministrationPagedListModelPagingInfo}
     * @memberof RouteOfAdministrationPagedListModelApiPagedListResult
     */
    'paging'?: RouteOfAdministrationPagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface RouteOfAdministrationPagedListModelPagingInfo
 */
export interface RouteOfAdministrationPagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof RouteOfAdministrationPagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof RouteOfAdministrationPagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof RouteOfAdministrationPagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface SearchComment
 */
export interface SearchComment {
    /**
     * 
     * @type {number}
     * @memberof SearchComment
     */
    'commentId'?: number;
    /**
     * 
     * @type {number}
     * @memberof SearchComment
     */
    'communicationId'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'dateOfCommunication'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'productCodes'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'productName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'countryName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'drugSubstanceNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'dosageFormNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'routeOfAdministrationNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'submissionNumberNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'applicationNumberNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'submissionTypeName'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'tagNames'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'productTypeNames'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchComment
     */
    'isQuestionIncluded'?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchComment
     */
    'isGeneralGuidance'?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'birdsLinkToBIResponse'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'birdsLinkToBISAMP'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'question'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchComment
     */
    'response'?: string | null;
}
/**
 * 
 * @export
 * @interface SearchCommentCommandRequest
 */
export interface SearchCommentCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof SearchCommentCommandRequest
     */
    'startDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchCommentCommandRequest
     */
    'endDate'?: string | null;
    /**
     * 
     * @type {Array<ProductDtoModel>}
     * @memberof SearchCommentCommandRequest
     */
    'products'?: Array<ProductDtoModel> | null;
    /**
     * 
     * @type {Array<DosageFormModel>}
     * @memberof SearchCommentCommandRequest
     */
    'dosageForms'?: Array<DosageFormModel> | null;
    /**
     * 
     * @type {Array<RouteOfAdministrationModel>}
     * @memberof SearchCommentCommandRequest
     */
    'routesOfAdministration'?: Array<RouteOfAdministrationModel> | null;
    /**
     * 
     * @type {Array<DrugSubstanceDtoModel>}
     * @memberof SearchCommentCommandRequest
     */
    'drugSubstances'?: Array<DrugSubstanceDtoModel> | null;
    /**
     * 
     * @type {Array<CountryModel>}
     * @memberof SearchCommentCommandRequest
     */
    'countries'?: Array<CountryModel> | null;
    /**
     * 
     * @type {Array<SubmissionTypeModel>}
     * @memberof SearchCommentCommandRequest
     */
    'submissionTypes'?: Array<SubmissionTypeModel> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SearchCommentCommandRequest
     */
    'applicationNumbers'?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SearchCommentCommandRequest
     */
    'submissionNumbers'?: Array<string> | null;
    /**
     * 
     * @type {Array<TagModel>}
     * @memberof SearchCommentCommandRequest
     */
    'tags'?: Array<TagModel> | null;
    /**
     * 
     * @type {Array<ProductTypeModel>}
     * @memberof SearchCommentCommandRequest
     */
    'productTypes'?: Array<ProductTypeModel> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SearchCommentCommandRequest
     */
    'productCodes'?: Array<string> | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchCommentCommandRequest
     */
    'isGeneralGuidance'?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchCommentCommandRequest
     */
    'isQuestionIncluded'?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof SearchCommentCommandRequest
     */
    'searchText'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof SearchCommentCommandRequest
     */
    'skip'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof SearchCommentCommandRequest
     */
    'take'?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SearchCommentCommandRequest
     */
    'sort'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchCommentCommandRequest
     */
    'fuzzy'?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof SearchCommentCommandRequest
     */
    'requestType'?: string | null;
}
/**
 * 
 * @export
 * @interface SearchCommentCommandResponse
 */
export interface SearchCommentCommandResponse {
    /**
     * 
     * @type {Array<SearchComment>}
     * @memberof SearchCommentCommandResponse
     */
    'comments'?: Array<SearchComment> | null;
    /**
     * 
     * @type {number}
     * @memberof SearchCommentCommandResponse
     */
    'total'?: number | null;
}
/**
 * 
 * @export
 * @interface SearchDetailsModel
 */
export interface SearchDetailsModel {
    /**
     * 
     * @type {number}
     * @memberof SearchDetailsModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'subject'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'country'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'dateOfCommunication'?: string;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'submissionType'?: string | null;
    /**
     * 
     * @type {Array<ApplicationModel>}
     * @memberof SearchDetailsModel
     */
    'applications'?: Array<ApplicationModel> | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchDetailsModel
     */
    'isCompleted'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'lastUpdatedBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'createdDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'lastUpdatedDate'?: string;
    /**
     * 
     * @type {string}
     * @memberof SearchDetailsModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {CommentDtoModel}
     * @memberof SearchDetailsModel
     */
    'comment'?: CommentDtoModel;
    /**
     * 
     * @type {Array<ProductDtoModel>}
     * @memberof SearchDetailsModel
     */
    'allProducts'?: Array<ProductDtoModel> | null;
    /**
     * 
     * @type {boolean}
     * @memberof SearchDetailsModel
     */
    'containsGeneralGuidanceComments'?: boolean;
}
/**
 * 
 * @export
 * @interface StringApiListResult
 */
export interface StringApiListResult {
    /**
     * 
     * @type {Array<string>}
     * @memberof StringApiListResult
     */
    'data'?: Array<string> | null;
}
/**
 * 
 * @export
 * @interface SubmissionModel
 */
export interface SubmissionModel {
    /**
     * 
     * @type {number}
     * @memberof SubmissionModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubmissionModel
     */
    'number'?: string | null;
}
/**
 * 
 * @export
 * @interface SubmissionTypeModel
 */
export interface SubmissionTypeModel {
    /**
     * 
     * @type {number}
     * @memberof SubmissionTypeModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof SubmissionTypeModel
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface SubmissionTypeModelApiListResult
 */
export interface SubmissionTypeModelApiListResult {
    /**
     * 
     * @type {Array<SubmissionTypeModel>}
     * @memberof SubmissionTypeModelApiListResult
     */
    'data'?: Array<SubmissionTypeModel> | null;
}
/**
 * 
 * @export
 * @interface TagModel
 */
export interface TagModel {
    /**
     * 
     * @type {number}
     * @memberof TagModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof TagModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TagModel
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface TagModelApiListResult
 */
export interface TagModelApiListResult {
    /**
     * 
     * @type {Array<TagModel>}
     * @memberof TagModelApiListResult
     */
    'data'?: Array<TagModel> | null;
}
/**
 * 
 * @export
 * @interface TagPagedListModel
 */
export interface TagPagedListModel {
    /**
     * 
     * @type {number}
     * @memberof TagPagedListModel
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'description'?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof TagPagedListModel
     */
    'isAssociatedToComment'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'createdDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'createdBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'lastUpdatedDate'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TagPagedListModel
     */
    'lastUpdatedBy'?: string | null;
}
/**
 * 
 * @export
 * @interface TagPagedListModelApiPagedListResult
 */
export interface TagPagedListModelApiPagedListResult {
    /**
     * 
     * @type {Array<TagPagedListModel>}
     * @memberof TagPagedListModelApiPagedListResult
     */
    'data'?: Array<TagPagedListModel> | null;
    /**
     * 
     * @type {TagPagedListModelPagingInfo}
     * @memberof TagPagedListModelApiPagedListResult
     */
    'paging'?: TagPagedListModelPagingInfo;
}
/**
 * 
 * @export
 * @interface TagPagedListModelPagingInfo
 */
export interface TagPagedListModelPagingInfo {
    /**
     * 
     * @type {number}
     * @memberof TagPagedListModelPagingInfo
     */
    'offset'?: number;
    /**
     * 
     * @type {number}
     * @memberof TagPagedListModelPagingInfo
     */
    'limit'?: number;
    /**
     * 
     * @type {number}
     * @memberof TagPagedListModelPagingInfo
     */
    'totalItemCount'?: number;
}
/**
 * 
 * @export
 * @interface UpdateCommentCommandRequest
 */
export interface UpdateCommentCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof UpdateCommentCommandRequest
     */
    'communicationId'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandRequest
     */
    'description'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandRequest
     */
    'question'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandRequest
     */
    'response'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandRequest
     */
    'birdsLinkToBIResponse'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandRequest
     */
    'birdsLinkToBISAMP'?: string | null;
    /**
     * 
     * @type {Array<ProductExtensionCommentModel>}
     * @memberof UpdateCommentCommandRequest
     */
    'productExtensions'?: Array<ProductExtensionCommentModel> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof UpdateCommentCommandRequest
     */
    'drugSubstanceIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof UpdateCommentCommandRequest
     */
    'tagIds'?: Array<number> | null;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateCommentCommandRequest
     */
    'isGeneralGuidance'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateCommentCommandRequest
     */
    'isQuestionIncluded'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof UpdateCommentCommandRequest
     */
    'id'?: number;
}
/**
 * 
 * @export
 * @interface UpdateCommentCommandResponse
 */
export interface UpdateCommentCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateCommentCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandResponse
     */
    'lastUpdatedBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommentCommandResponse
     */
    'lastUpdatedDate'?: string;
}
/**
 * 
 * @export
 * @interface UpdateCommunicationCommandRequest
 */
export interface UpdateCommunicationCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof UpdateCommunicationCommandRequest
     */
    'subject'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof UpdateCommunicationCommandRequest
     */
    'submissionTypeId'?: number;
    /**
     * 
     * @type {number}
     * @memberof UpdateCommunicationCommandRequest
     */
    'countryId'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommunicationCommandRequest
     */
    'dateOfCommunication'?: string;
    /**
     * 
     * @type {Array<ApplicationModel>}
     * @memberof UpdateCommunicationCommandRequest
     */
    'applications'?: Array<ApplicationModel> | null;
    /**
     * 
     * @type {number}
     * @memberof UpdateCommunicationCommandRequest
     */
    'id'?: number;
}
/**
 * 
 * @export
 * @interface UpdateCommunicationCommandResponse
 */
export interface UpdateCommunicationCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateCommunicationCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommunicationCommandResponse
     */
    'lastUpdatedBy'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateCommunicationCommandResponse
     */
    'lastUpdatedDate'?: string;
}
/**
 * 
 * @export
 * @interface UpdateDosageFormCommandRequest
 */
export interface UpdateDosageFormCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof UpdateDosageFormCommandRequest
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateDosageFormCommandRequest
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateDosageFormCommandResponse
 */
export interface UpdateDosageFormCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateDosageFormCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateDosageFormCommandResponse
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateDrugSubstanceCommandRequest
 */
export interface UpdateDrugSubstanceCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof UpdateDrugSubstanceCommandRequest
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandRequest
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandRequest
     */
    'code': string;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandRequest
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateDrugSubstanceCommandResponse
 */
export interface UpdateDrugSubstanceCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateDrugSubstanceCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandResponse
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandResponse
     */
    'code'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateDrugSubstanceCommandResponse
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateProductCommandRequest
 */
export interface UpdateProductCommandRequest {
    /**
     * 
     * @type {string}
     * @memberof UpdateProductCommandRequest
     */
    'name': string;
    /**
     * 
     * @type {boolean}
     * @memberof UpdateProductCommandRequest
     */
    'isActive'?: boolean;
    /**
     * 
     * @type {Array<number>}
     * @memberof UpdateProductCommandRequest
     */
    'productTypeIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<number>}
     * @memberof UpdateProductCommandRequest
     */
    'drugSubstanceIds'?: Array<number> | null;
    /**
     * 
     * @type {Array<ProductExtensionModel>}
     * @memberof UpdateProductCommandRequest
     */
    'productExtensions'?: Array<ProductExtensionModel> | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateProductCommandRequest
     */
    'notAssigned'?: string | null;
    /**
     * 
     * @type {number}
     * @memberof UpdateProductCommandRequest
     */
    'id'?: number;
}
/**
 * 
 * @export
 * @interface UpdateProductCommandResponse
 */
export interface UpdateProductCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateProductCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof UpdateProductCommandResponse
     */
    'updatedCommentsCount'?: number;
}
/**
 * 
 * @export
 * @interface UpdateRouteOfAdministrationCommandRequest
 */
export interface UpdateRouteOfAdministrationCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof UpdateRouteOfAdministrationCommandRequest
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateRouteOfAdministrationCommandRequest
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface UpdateRouteOfAdministrationCommandResponse
 */
export interface UpdateRouteOfAdministrationCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateRouteOfAdministrationCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateRouteOfAdministrationCommandResponse
     */
    'name'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateTagCommandRequest
 */
export interface UpdateTagCommandRequest {
    /**
     * 
     * @type {number}
     * @memberof UpdateTagCommandRequest
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateTagCommandRequest
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateTagCommandRequest
     */
    'description'?: string | null;
}
/**
 * 
 * @export
 * @interface UpdateTagCommandResponse
 */
export interface UpdateTagCommandResponse {
    /**
     * 
     * @type {number}
     * @memberof UpdateTagCommandResponse
     */
    'id'?: number;
    /**
     * 
     * @type {string}
     * @memberof UpdateTagCommandResponse
     */
    'name'?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UpdateTagCommandResponse
     */
    'description'?: string | null;
}

/**
 * ApplicationsApi - axios parameter creator
 * @export
 */
export const ApplicationsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllApplicationNumbers: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getAllApplicationNumbers', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Applications/application-numbers`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ApplicationsApi - functional programming interface
 * @export
 */
export const ApplicationsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ApplicationsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllApplicationNumbers(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllApplicationNumbers(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ApplicationsApi.getAllApplicationNumbers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ApplicationsApi - factory interface
 * @export
 */
export const ApplicationsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ApplicationsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllApplicationNumbers(tenant: string, options?: any): AxiosPromise<StringApiListResult> {
            return localVarFp.getAllApplicationNumbers(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ApplicationsApi - object-oriented interface
 * @export
 * @class ApplicationsApi
 * @extends {BaseAPI}
 */
export class ApplicationsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ApplicationsApi
     */
    public getAllApplicationNumbers(tenant: string, options?: RawAxiosRequestConfig) {
        return ApplicationsApiFp(this.configuration).getAllApplicationNumbers(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * AuditsApi - axios parameter creator
 * @export
 */
export const AuditsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEventConstants: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getEventConstants', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/audits/eventConstants`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuditsApi - functional programming interface
 * @export
 */
export const AuditsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuditsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getEventConstants(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<GetEventConstantsQueryResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getEventConstants(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuditsApi.getEventConstants']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuditsApi - factory interface
 * @export
 */
export const AuditsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuditsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEventConstants(tenant: string, options?: any): AxiosPromise<GetEventConstantsQueryResponse> {
            return localVarFp.getEventConstants(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AuditsApi - object-oriented interface
 * @export
 * @class AuditsApi
 * @extends {BaseAPI}
 */
export class AuditsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuditsApi
     */
    public getEventConstants(tenant: string, options?: RawAxiosRequestConfig) {
        return AuditsApiFp(this.configuration).getEventConstants(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * CommentsApi - axios parameter creator
 * @export
 */
export const CommentsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommentCommandRequest} [createCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createComment: async (tenant: string, createCommentCommandRequest?: CreateCommentCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createComment', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createCommentCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteComment: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteComment', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteComment', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {DeleteCommentsByProductCommandRequest} [deleteCommentsByProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCommentsByCommunicationAndProduct: async (tenant: string, deleteCommentsByProductCommandRequest?: DeleteCommentsByProductCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteCommentsByCommunicationAndProduct', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(deleteCommentsByProductCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {string} tenant 
         * @param {number} [productId] 
         * @param {number} [excludedCommentId] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommentsByCommunicationId: async (communicationId: number, tenant: string, productId?: number, excludedCommentId?: number, skip?: number, take?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'communicationId' is not null or undefined
            assertParamExists('getCommentsByCommunicationId', 'communicationId', communicationId)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getCommentsByCommunicationId', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments/comments/{communicationId}`
                .replace(`{${"communicationId"}}`, encodeURIComponent(String(communicationId)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (productId !== undefined) {
                localVarQueryParameter['productId'] = productId;
            }

            if (excludedCommentId !== undefined) {
                localVarQueryParameter['excludedCommentId'] = excludedCommentId;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {SearchCommentCommandRequest} [searchCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchComments: async (tenant: string, searchCommentCommandRequest?: SearchCommentCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('searchComments', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments/search`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(searchCommentCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {number} commentId 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchDetails: async (communicationId: number, commentId: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'communicationId' is not null or undefined
            assertParamExists('searchDetails', 'communicationId', communicationId)
            // verify required parameter 'commentId' is not null or undefined
            assertParamExists('searchDetails', 'commentId', commentId)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('searchDetails', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments/search-details/{communicationId}/{commentId}`
                .replace(`{${"communicationId"}}`, encodeURIComponent(String(communicationId)))
                .replace(`{${"commentId"}}`, encodeURIComponent(String(commentId)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommentCommandRequest} [updateCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateComment: async (tenant: string, updateCommentCommandRequest?: UpdateCommentCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateComment', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Comments`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateCommentCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CommentsApi - functional programming interface
 * @export
 */
export const CommentsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CommentsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommentCommandRequest} [createCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createComment(tenant: string, createCommentCommandRequest?: CreateCommentCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateCommentCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createComment(tenant, createCommentCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.createComment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteComment(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteComment(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.deleteComment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {DeleteCommentsByProductCommandRequest} [deleteCommentsByProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCommentsByCommunicationAndProduct(tenant: string, deleteCommentsByProductCommandRequest?: DeleteCommentsByProductCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCommentsByCommunicationAndProduct(tenant, deleteCommentsByProductCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.deleteCommentsByCommunicationAndProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {string} tenant 
         * @param {number} [productId] 
         * @param {number} [excludedCommentId] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCommentsByCommunicationId(communicationId: number, tenant: string, productId?: number, excludedCommentId?: number, skip?: number, take?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommentDtoModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCommentsByCommunicationId(communicationId, tenant, productId, excludedCommentId, skip, take, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.getCommentsByCommunicationId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {SearchCommentCommandRequest} [searchCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchComments(tenant: string, searchCommentCommandRequest?: SearchCommentCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchCommentCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchComments(tenant, searchCommentCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.searchComments']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {number} commentId 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchDetails(communicationId: number, commentId: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SearchDetailsModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchDetails(communicationId, commentId, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.searchDetails']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommentCommandRequest} [updateCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateComment(tenant: string, updateCommentCommandRequest?: UpdateCommentCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateCommentCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateComment(tenant, updateCommentCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommentsApi.updateComment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CommentsApi - factory interface
 * @export
 */
export const CommentsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CommentsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommentCommandRequest} [createCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createComment(tenant: string, createCommentCommandRequest?: CreateCommentCommandRequest, options?: any): AxiosPromise<CreateCommentCommandResponse> {
            return localVarFp.createComment(tenant, createCommentCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteComment(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteComment(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {DeleteCommentsByProductCommandRequest} [deleteCommentsByProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCommentsByCommunicationAndProduct(tenant: string, deleteCommentsByProductCommandRequest?: DeleteCommentsByProductCommandRequest, options?: any): AxiosPromise<void> {
            return localVarFp.deleteCommentsByCommunicationAndProduct(tenant, deleteCommentsByProductCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {string} tenant 
         * @param {number} [productId] 
         * @param {number} [excludedCommentId] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommentsByCommunicationId(communicationId: number, tenant: string, productId?: number, excludedCommentId?: number, skip?: number, take?: number, options?: any): AxiosPromise<CommentDtoModelApiPagedListResult> {
            return localVarFp.getCommentsByCommunicationId(communicationId, tenant, productId, excludedCommentId, skip, take, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {SearchCommentCommandRequest} [searchCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchComments(tenant: string, searchCommentCommandRequest?: SearchCommentCommandRequest, options?: any): AxiosPromise<SearchCommentCommandResponse> {
            return localVarFp.searchComments(tenant, searchCommentCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} communicationId 
         * @param {number} commentId 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchDetails(communicationId: number, commentId: number, tenant: string, options?: any): AxiosPromise<SearchDetailsModel> {
            return localVarFp.searchDetails(communicationId, commentId, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommentCommandRequest} [updateCommentCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateComment(tenant: string, updateCommentCommandRequest?: UpdateCommentCommandRequest, options?: any): AxiosPromise<UpdateCommentCommandResponse> {
            return localVarFp.updateComment(tenant, updateCommentCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CommentsApi - object-oriented interface
 * @export
 * @class CommentsApi
 * @extends {BaseAPI}
 */
export class CommentsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateCommentCommandRequest} [createCommentCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public createComment(tenant: string, createCommentCommandRequest?: CreateCommentCommandRequest, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).createComment(tenant, createCommentCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public deleteComment(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).deleteComment(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {DeleteCommentsByProductCommandRequest} [deleteCommentsByProductCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public deleteCommentsByCommunicationAndProduct(tenant: string, deleteCommentsByProductCommandRequest?: DeleteCommentsByProductCommandRequest, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).deleteCommentsByCommunicationAndProduct(tenant, deleteCommentsByProductCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} communicationId 
     * @param {string} tenant 
     * @param {number} [productId] 
     * @param {number} [excludedCommentId] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public getCommentsByCommunicationId(communicationId: number, tenant: string, productId?: number, excludedCommentId?: number, skip?: number, take?: number, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).getCommentsByCommunicationId(communicationId, tenant, productId, excludedCommentId, skip, take, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {SearchCommentCommandRequest} [searchCommentCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public searchComments(tenant: string, searchCommentCommandRequest?: SearchCommentCommandRequest, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).searchComments(tenant, searchCommentCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} communicationId 
     * @param {number} commentId 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public searchDetails(communicationId: number, commentId: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).searchDetails(communicationId, commentId, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateCommentCommandRequest} [updateCommentCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommentsApi
     */
    public updateComment(tenant: string, updateCommentCommandRequest?: UpdateCommentCommandRequest, options?: RawAxiosRequestConfig) {
        return CommentsApiFp(this.configuration).updateComment(tenant, updateCommentCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * CommunicationsApi - axios parameter creator
 * @export
 */
export const CommunicationsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeCommunication: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('completeCommunication', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('completeCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications/{id}/complete`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommunicationCommandRequest} [createCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCommunication: async (tenant: string, createCommunicationCommandRequest?: CreateCommunicationCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createCommunicationCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCommunication: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteCommunication', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommunication: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getCommunication', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedCommunicationsList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedCommunicationsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reinstateCommunication: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('reinstateCommunication', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('reinstateCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications/{id}/reinstate`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommunicationCommandRequest} [updateCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCommunication: async (tenant: string, updateCommunicationCommandRequest?: UpdateCommunicationCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateCommunication', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Communications`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateCommunicationCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CommunicationsApi - functional programming interface
 * @export
 */
export const CommunicationsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CommunicationsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeCommunication(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.completeCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommunicationCommandRequest} [createCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCommunication(tenant: string, createCommunicationCommandRequest?: CreateCommunicationCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateCommunicationCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCommunication(tenant, createCommunicationCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.createCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCommunication(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.deleteCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommunicationModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCommunication(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.getCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedCommunicationsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CommunicationPagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedCommunicationsList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.getPagedCommunicationsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async reinstateCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.reinstateCommunication(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.reinstateCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommunicationCommandRequest} [updateCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCommunication(tenant: string, updateCommunicationCommandRequest?: UpdateCommunicationCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateCommunicationCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCommunication(tenant, updateCommunicationCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommunicationsApi.updateCommunication']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CommunicationsApi - factory interface
 * @export
 */
export const CommunicationsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CommunicationsApiFp(configuration)
    return {
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeCommunication(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.completeCommunication(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {CreateCommunicationCommandRequest} [createCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCommunication(tenant: string, createCommunicationCommandRequest?: CreateCommunicationCommandRequest, options?: any): AxiosPromise<CreateCommunicationCommandResponse> {
            return localVarFp.createCommunication(tenant, createCommunicationCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCommunication(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteCommunication(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommunication(id: number, tenant: string, options?: any): AxiosPromise<CommunicationModel> {
            return localVarFp.getCommunication(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedCommunicationsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<CommunicationPagedListModelApiPagedListResult> {
            return localVarFp.getPagedCommunicationsList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        reinstateCommunication(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.reinstateCommunication(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateCommunicationCommandRequest} [updateCommunicationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCommunication(tenant: string, updateCommunicationCommandRequest?: UpdateCommunicationCommandRequest, options?: any): AxiosPromise<UpdateCommunicationCommandResponse> {
            return localVarFp.updateCommunication(tenant, updateCommunicationCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CommunicationsApi - object-oriented interface
 * @export
 * @class CommunicationsApi
 * @extends {BaseAPI}
 */
export class CommunicationsApi extends BaseAPI {
    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public completeCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).completeCommunication(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {CreateCommunicationCommandRequest} [createCommunicationCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public createCommunication(tenant: string, createCommunicationCommandRequest?: CreateCommunicationCommandRequest, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).createCommunication(tenant, createCommunicationCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public deleteCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).deleteCommunication(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public getCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).getCommunication(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public getPagedCommunicationsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).getPagedCommunicationsList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public reinstateCommunication(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).reinstateCommunication(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateCommunicationCommandRequest} [updateCommunicationCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommunicationsApi
     */
    public updateCommunication(tenant: string, updateCommunicationCommandRequest?: UpdateCommunicationCommandRequest, options?: RawAxiosRequestConfig) {
        return CommunicationsApiFp(this.configuration).updateCommunication(tenant, updateCommunicationCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * CountriesApi - axios parameter creator
 * @export
 */
export const CountriesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCountryList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getCountryList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Countries`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CountriesApi - functional programming interface
 * @export
 */
export const CountriesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CountriesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCountryList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CountryModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCountryList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CountriesApi.getCountryList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CountriesApi - factory interface
 * @export
 */
export const CountriesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CountriesApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCountryList(tenant: string, options?: any): AxiosPromise<CountryModelApiListResult> {
            return localVarFp.getCountryList(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CountriesApi - object-oriented interface
 * @export
 * @class CountriesApi
 * @extends {BaseAPI}
 */
export class CountriesApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CountriesApi
     */
    public getCountryList(tenant: string, options?: RawAxiosRequestConfig) {
        return CountriesApiFp(this.configuration).getCountryList(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DosageFormsApi - axios parameter creator
 * @export
 */
export const DosageFormsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDosageFormCommandRequest} [createDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDosageForm: async (tenant: string, createDosageFormCommandRequest?: CreateDosageFormCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createDosageForm', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DosageForms`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createDosageFormCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDosageForm: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteDosageForm', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteDosageForm', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DosageForms/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDosageFormList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getDosageFormList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DosageForms/all`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedDosageFormsList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedDosageFormsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DosageForms`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDosageFormCommandRequest} [updateDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDosageForm: async (tenant: string, updateDosageFormCommandRequest?: UpdateDosageFormCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateDosageForm', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DosageForms`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateDosageFormCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DosageFormsApi - functional programming interface
 * @export
 */
export const DosageFormsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DosageFormsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDosageFormCommandRequest} [createDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDosageForm(tenant: string, createDosageFormCommandRequest?: CreateDosageFormCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateDosageFormCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDosageForm(tenant, createDosageFormCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DosageFormsApi.createDosageForm']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDosageForm(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDosageForm(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DosageFormsApi.deleteDosageForm']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDosageFormList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DosageFormModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDosageFormList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DosageFormsApi.getDosageFormList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedDosageFormsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DosageFormPagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedDosageFormsList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DosageFormsApi.getPagedDosageFormsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDosageFormCommandRequest} [updateDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDosageForm(tenant: string, updateDosageFormCommandRequest?: UpdateDosageFormCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateDosageFormCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDosageForm(tenant, updateDosageFormCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DosageFormsApi.updateDosageForm']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DosageFormsApi - factory interface
 * @export
 */
export const DosageFormsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DosageFormsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDosageFormCommandRequest} [createDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDosageForm(tenant: string, createDosageFormCommandRequest?: CreateDosageFormCommandRequest, options?: any): AxiosPromise<CreateDosageFormCommandResponse> {
            return localVarFp.createDosageForm(tenant, createDosageFormCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDosageForm(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteDosageForm(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDosageFormList(tenant: string, options?: any): AxiosPromise<DosageFormModelApiListResult> {
            return localVarFp.getDosageFormList(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedDosageFormsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<DosageFormPagedListModelApiPagedListResult> {
            return localVarFp.getPagedDosageFormsList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDosageFormCommandRequest} [updateDosageFormCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDosageForm(tenant: string, updateDosageFormCommandRequest?: UpdateDosageFormCommandRequest, options?: any): AxiosPromise<UpdateDosageFormCommandResponse> {
            return localVarFp.updateDosageForm(tenant, updateDosageFormCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DosageFormsApi - object-oriented interface
 * @export
 * @class DosageFormsApi
 * @extends {BaseAPI}
 */
export class DosageFormsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateDosageFormCommandRequest} [createDosageFormCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DosageFormsApi
     */
    public createDosageForm(tenant: string, createDosageFormCommandRequest?: CreateDosageFormCommandRequest, options?: RawAxiosRequestConfig) {
        return DosageFormsApiFp(this.configuration).createDosageForm(tenant, createDosageFormCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DosageFormsApi
     */
    public deleteDosageForm(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return DosageFormsApiFp(this.configuration).deleteDosageForm(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DosageFormsApi
     */
    public getDosageFormList(tenant: string, options?: RawAxiosRequestConfig) {
        return DosageFormsApiFp(this.configuration).getDosageFormList(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DosageFormsApi
     */
    public getPagedDosageFormsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return DosageFormsApiFp(this.configuration).getPagedDosageFormsList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateDosageFormCommandRequest} [updateDosageFormCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DosageFormsApi
     */
    public updateDosageForm(tenant: string, updateDosageFormCommandRequest?: UpdateDosageFormCommandRequest, options?: RawAxiosRequestConfig) {
        return DosageFormsApiFp(this.configuration).updateDosageForm(tenant, updateDosageFormCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DrugSubstancesApi - axios parameter creator
 * @export
 */
export const DrugSubstancesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDrugSubstanceCommandRequest} [createDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDrugSubstance: async (tenant: string, createDrugSubstanceCommandRequest?: CreateDrugSubstanceCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createDrugSubstance', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DrugSubstances`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createDrugSubstanceCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDrugSubstance: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteDrugSubstance', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteDrugSubstance', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DrugSubstances/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDrugSubstancesList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getDrugSubstancesList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DrugSubstances/all`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedDrugSubstancesList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedDrugSubstancesList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DrugSubstances`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDrugSubstanceCommandRequest} [updateDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDrugSubstance: async (tenant: string, updateDrugSubstanceCommandRequest?: UpdateDrugSubstanceCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateDrugSubstance', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/DrugSubstances`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateDrugSubstanceCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DrugSubstancesApi - functional programming interface
 * @export
 */
export const DrugSubstancesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DrugSubstancesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDrugSubstanceCommandRequest} [createDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDrugSubstance(tenant: string, createDrugSubstanceCommandRequest?: CreateDrugSubstanceCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateDrugSubstanceCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createDrugSubstance(tenant, createDrugSubstanceCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DrugSubstancesApi.createDrugSubstance']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteDrugSubstance(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteDrugSubstance(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DrugSubstancesApi.deleteDrugSubstance']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDrugSubstancesList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DrugSubstanceModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDrugSubstancesList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DrugSubstancesApi.getDrugSubstancesList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedDrugSubstancesList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DrugSubstancePagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedDrugSubstancesList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DrugSubstancesApi.getPagedDrugSubstancesList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDrugSubstanceCommandRequest} [updateDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDrugSubstance(tenant: string, updateDrugSubstanceCommandRequest?: UpdateDrugSubstanceCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateDrugSubstanceCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDrugSubstance(tenant, updateDrugSubstanceCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DrugSubstancesApi.updateDrugSubstance']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DrugSubstancesApi - factory interface
 * @export
 */
export const DrugSubstancesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DrugSubstancesApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateDrugSubstanceCommandRequest} [createDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDrugSubstance(tenant: string, createDrugSubstanceCommandRequest?: CreateDrugSubstanceCommandRequest, options?: any): AxiosPromise<CreateDrugSubstanceCommandResponse> {
            return localVarFp.createDrugSubstance(tenant, createDrugSubstanceCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteDrugSubstance(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteDrugSubstance(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDrugSubstancesList(tenant: string, options?: any): AxiosPromise<DrugSubstanceModelApiListResult> {
            return localVarFp.getDrugSubstancesList(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedDrugSubstancesList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<DrugSubstancePagedListModelApiPagedListResult> {
            return localVarFp.getPagedDrugSubstancesList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateDrugSubstanceCommandRequest} [updateDrugSubstanceCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDrugSubstance(tenant: string, updateDrugSubstanceCommandRequest?: UpdateDrugSubstanceCommandRequest, options?: any): AxiosPromise<UpdateDrugSubstanceCommandResponse> {
            return localVarFp.updateDrugSubstance(tenant, updateDrugSubstanceCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DrugSubstancesApi - object-oriented interface
 * @export
 * @class DrugSubstancesApi
 * @extends {BaseAPI}
 */
export class DrugSubstancesApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateDrugSubstanceCommandRequest} [createDrugSubstanceCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DrugSubstancesApi
     */
    public createDrugSubstance(tenant: string, createDrugSubstanceCommandRequest?: CreateDrugSubstanceCommandRequest, options?: RawAxiosRequestConfig) {
        return DrugSubstancesApiFp(this.configuration).createDrugSubstance(tenant, createDrugSubstanceCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DrugSubstancesApi
     */
    public deleteDrugSubstance(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return DrugSubstancesApiFp(this.configuration).deleteDrugSubstance(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DrugSubstancesApi
     */
    public getDrugSubstancesList(tenant: string, options?: RawAxiosRequestConfig) {
        return DrugSubstancesApiFp(this.configuration).getDrugSubstancesList(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DrugSubstancesApi
     */
    public getPagedDrugSubstancesList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return DrugSubstancesApiFp(this.configuration).getPagedDrugSubstancesList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateDrugSubstanceCommandRequest} [updateDrugSubstanceCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DrugSubstancesApi
     */
    public updateDrugSubstance(tenant: string, updateDrugSubstanceCommandRequest?: UpdateDrugSubstanceCommandRequest, options?: RawAxiosRequestConfig) {
        return DrugSubstancesApiFp(this.configuration).updateDrugSubstance(tenant, updateDrugSubstanceCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ProductTypesApi - axios parameter creator
 * @export
 */
export const ProductTypesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProductTypesList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getProductTypesList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/ProductTypes`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProductTypesApi - functional programming interface
 * @export
 */
export const ProductTypesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProductTypesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProductTypesList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProductTypeModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProductTypesList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductTypesApi.getProductTypesList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProductTypesApi - factory interface
 * @export
 */
export const ProductTypesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProductTypesApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProductTypesList(tenant: string, options?: any): AxiosPromise<ProductTypeModelApiListResult> {
            return localVarFp.getProductTypesList(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProductTypesApi - object-oriented interface
 * @export
 * @class ProductTypesApi
 * @extends {BaseAPI}
 */
export class ProductTypesApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductTypesApi
     */
    public getProductTypesList(tenant: string, options?: RawAxiosRequestConfig) {
        return ProductTypesApiFp(this.configuration).getProductTypesList(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * ProductsApi - axios parameter creator
 * @export
 */
export const ProductsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateProductCommandRequest} [createProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProduct: async (tenant: string, createProductCommandRequest?: CreateProductCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createProduct', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createProductCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProduct: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteProduct', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteProduct', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllProductCodes: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getAllProductCodes', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products/product-codes`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedProductsList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedProductsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProduct: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getProduct', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getProduct', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProductsList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getProductsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products/all`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateProductCommandRequest} [updateProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProduct: async (tenant: string, updateProductCommandRequest?: UpdateProductCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateProduct', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Products`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateProductCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProductsApi - functional programming interface
 * @export
 */
export const ProductsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProductsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateProductCommandRequest} [createProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createProduct(tenant: string, createProductCommandRequest?: CreateProductCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateProductCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createProduct(tenant, createProductCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.createProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteProduct(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteProduct(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.deleteProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllProductCodes(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllProductCodes(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.getAllProductCodes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedProductsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProductPagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedProductsList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.getPagedProductsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProduct(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProductModel>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProduct(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.getProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProductsList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ProductModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getProductsList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.getProductsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateProductCommandRequest} [updateProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateProduct(tenant: string, updateProductCommandRequest?: UpdateProductCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateProductCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateProduct(tenant, updateProductCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ProductsApi.updateProduct']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ProductsApi - factory interface
 * @export
 */
export const ProductsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProductsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateProductCommandRequest} [createProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createProduct(tenant: string, createProductCommandRequest?: CreateProductCommandRequest, options?: any): AxiosPromise<CreateProductCommandResponse> {
            return localVarFp.createProduct(tenant, createProductCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteProduct(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteProduct(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllProductCodes(tenant: string, options?: any): AxiosPromise<StringApiListResult> {
            return localVarFp.getAllProductCodes(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedProductsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<ProductPagedListModelApiPagedListResult> {
            return localVarFp.getPagedProductsList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProduct(id: number, tenant: string, options?: any): AxiosPromise<ProductModel> {
            return localVarFp.getProduct(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProductsList(tenant: string, options?: any): AxiosPromise<ProductModelApiListResult> {
            return localVarFp.getProductsList(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateProductCommandRequest} [updateProductCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateProduct(tenant: string, updateProductCommandRequest?: UpdateProductCommandRequest, options?: any): AxiosPromise<UpdateProductCommandResponse> {
            return localVarFp.updateProduct(tenant, updateProductCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProductsApi - object-oriented interface
 * @export
 * @class ProductsApi
 * @extends {BaseAPI}
 */
export class ProductsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateProductCommandRequest} [createProductCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public createProduct(tenant: string, createProductCommandRequest?: CreateProductCommandRequest, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).createProduct(tenant, createProductCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public deleteProduct(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).deleteProduct(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public getAllProductCodes(tenant: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).getAllProductCodes(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public getPagedProductsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).getPagedProductsList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public getProduct(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).getProduct(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public getProductsList(tenant: string, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).getProductsList(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateProductCommandRequest} [updateProductCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProductsApi
     */
    public updateProduct(tenant: string, updateProductCommandRequest?: UpdateProductCommandRequest, options?: RawAxiosRequestConfig) {
        return ProductsApiFp(this.configuration).updateProduct(tenant, updateProductCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * RouteOfAdministrationApi - axios parameter creator
 * @export
 */
export const RouteOfAdministrationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateRouteOfAdministrationCommandRequest} [createRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRouteOfAdministration: async (tenant: string, createRouteOfAdministrationCommandRequest?: CreateRouteOfAdministrationCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createRouteOfAdministration', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/RoutesOfAdministration`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createRouteOfAdministrationCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRouteOfAdministration: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteRouteOfAdministration', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteRouteOfAdministration', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/RoutesOfAdministration/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedRoutesOfAdministrationList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedRoutesOfAdministrationList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/RoutesOfAdministration`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getRouteOfAdministrationList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getRouteOfAdministrationList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/RoutesOfAdministration/all`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateRouteOfAdministrationCommandRequest} [updateRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRouteOfAdministration: async (tenant: string, updateRouteOfAdministrationCommandRequest?: UpdateRouteOfAdministrationCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateRouteOfAdministration', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/RoutesOfAdministration`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateRouteOfAdministrationCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RouteOfAdministrationApi - functional programming interface
 * @export
 */
export const RouteOfAdministrationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = RouteOfAdministrationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateRouteOfAdministrationCommandRequest} [createRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createRouteOfAdministration(tenant: string, createRouteOfAdministrationCommandRequest?: CreateRouteOfAdministrationCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateRouteOfAdministrationCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createRouteOfAdministration(tenant, createRouteOfAdministrationCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RouteOfAdministrationApi.createRouteOfAdministration']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteRouteOfAdministration(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteRouteOfAdministration(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RouteOfAdministrationApi.deleteRouteOfAdministration']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedRoutesOfAdministrationList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RouteOfAdministrationPagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedRoutesOfAdministrationList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RouteOfAdministrationApi.getPagedRoutesOfAdministrationList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getRouteOfAdministrationList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<RouteOfAdministrationModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getRouteOfAdministrationList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RouteOfAdministrationApi.getRouteOfAdministrationList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateRouteOfAdministrationCommandRequest} [updateRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateRouteOfAdministration(tenant: string, updateRouteOfAdministrationCommandRequest?: UpdateRouteOfAdministrationCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateRouteOfAdministrationCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateRouteOfAdministration(tenant, updateRouteOfAdministrationCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['RouteOfAdministrationApi.updateRouteOfAdministration']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * RouteOfAdministrationApi - factory interface
 * @export
 */
export const RouteOfAdministrationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = RouteOfAdministrationApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateRouteOfAdministrationCommandRequest} [createRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createRouteOfAdministration(tenant: string, createRouteOfAdministrationCommandRequest?: CreateRouteOfAdministrationCommandRequest, options?: any): AxiosPromise<CreateRouteOfAdministrationCommandResponse> {
            return localVarFp.createRouteOfAdministration(tenant, createRouteOfAdministrationCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteRouteOfAdministration(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteRouteOfAdministration(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedRoutesOfAdministrationList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<RouteOfAdministrationPagedListModelApiPagedListResult> {
            return localVarFp.getPagedRoutesOfAdministrationList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getRouteOfAdministrationList(tenant: string, options?: any): AxiosPromise<RouteOfAdministrationModelApiListResult> {
            return localVarFp.getRouteOfAdministrationList(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateRouteOfAdministrationCommandRequest} [updateRouteOfAdministrationCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateRouteOfAdministration(tenant: string, updateRouteOfAdministrationCommandRequest?: UpdateRouteOfAdministrationCommandRequest, options?: any): AxiosPromise<UpdateRouteOfAdministrationCommandResponse> {
            return localVarFp.updateRouteOfAdministration(tenant, updateRouteOfAdministrationCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RouteOfAdministrationApi - object-oriented interface
 * @export
 * @class RouteOfAdministrationApi
 * @extends {BaseAPI}
 */
export class RouteOfAdministrationApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateRouteOfAdministrationCommandRequest} [createRouteOfAdministrationCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RouteOfAdministrationApi
     */
    public createRouteOfAdministration(tenant: string, createRouteOfAdministrationCommandRequest?: CreateRouteOfAdministrationCommandRequest, options?: RawAxiosRequestConfig) {
        return RouteOfAdministrationApiFp(this.configuration).createRouteOfAdministration(tenant, createRouteOfAdministrationCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RouteOfAdministrationApi
     */
    public deleteRouteOfAdministration(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return RouteOfAdministrationApiFp(this.configuration).deleteRouteOfAdministration(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RouteOfAdministrationApi
     */
    public getPagedRoutesOfAdministrationList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return RouteOfAdministrationApiFp(this.configuration).getPagedRoutesOfAdministrationList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RouteOfAdministrationApi
     */
    public getRouteOfAdministrationList(tenant: string, options?: RawAxiosRequestConfig) {
        return RouteOfAdministrationApiFp(this.configuration).getRouteOfAdministrationList(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateRouteOfAdministrationCommandRequest} [updateRouteOfAdministrationCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RouteOfAdministrationApi
     */
    public updateRouteOfAdministration(tenant: string, updateRouteOfAdministrationCommandRequest?: UpdateRouteOfAdministrationCommandRequest, options?: RawAxiosRequestConfig) {
        return RouteOfAdministrationApiFp(this.configuration).updateRouteOfAdministration(tenant, updateRouteOfAdministrationCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * SubmissionTypesApi - axios parameter creator
 * @export
 */
export const SubmissionTypesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSubmissionTypeList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getSubmissionTypeList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/SubmissionTypes`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SubmissionTypesApi - functional programming interface
 * @export
 */
export const SubmissionTypesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SubmissionTypesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSubmissionTypeList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SubmissionTypeModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSubmissionTypeList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubmissionTypesApi.getSubmissionTypeList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SubmissionTypesApi - factory interface
 * @export
 */
export const SubmissionTypesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SubmissionTypesApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSubmissionTypeList(tenant: string, options?: any): AxiosPromise<SubmissionTypeModelApiListResult> {
            return localVarFp.getSubmissionTypeList(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SubmissionTypesApi - object-oriented interface
 * @export
 * @class SubmissionTypesApi
 * @extends {BaseAPI}
 */
export class SubmissionTypesApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubmissionTypesApi
     */
    public getSubmissionTypeList(tenant: string, options?: RawAxiosRequestConfig) {
        return SubmissionTypesApiFp(this.configuration).getSubmissionTypeList(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * SubmissionsApi - axios parameter creator
 * @export
 */
export const SubmissionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSubmissionNumbers: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getAllSubmissionNumbers', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Submissions/submission-numbers`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SubmissionsApi - functional programming interface
 * @export
 */
export const SubmissionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SubmissionsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAllSubmissionNumbers(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<StringApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAllSubmissionNumbers(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SubmissionsApi.getAllSubmissionNumbers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SubmissionsApi - factory interface
 * @export
 */
export const SubmissionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SubmissionsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAllSubmissionNumbers(tenant: string, options?: any): AxiosPromise<StringApiListResult> {
            return localVarFp.getAllSubmissionNumbers(tenant, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SubmissionsApi - object-oriented interface
 * @export
 * @class SubmissionsApi
 * @extends {BaseAPI}
 */
export class SubmissionsApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SubmissionsApi
     */
    public getAllSubmissionNumbers(tenant: string, options?: RawAxiosRequestConfig) {
        return SubmissionsApiFp(this.configuration).getAllSubmissionNumbers(tenant, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * TagApi - axios parameter creator
 * @export
 */
export const TagApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateTagCommandRequest} [createTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createTag: async (tenant: string, createTagCommandRequest?: CreateTagCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('createTag', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Tags`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createTagCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteTag: async (id: number, tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteTag', 'id', id)
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('deleteTag', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Tags/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedTagsList: async (tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getPagedTagsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Tags`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)

            if (filters) {
                localVarQueryParameter['filters'] = filters;
            }

            if (skip !== undefined) {
                localVarQueryParameter['skip'] = skip;
            }

            if (take !== undefined) {
                localVarQueryParameter['take'] = take;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTagsList: async (tenant: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('getTagsList', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Tags/all`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateTagCommandRequest} [updateTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateTag: async (tenant: string, updateTagCommandRequest?: UpdateTagCommandRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tenant' is not null or undefined
            assertParamExists('updateTag', 'tenant', tenant)
            const localVarPath = `/{tenant}/v1/Tags`
                .replace(`{${"tenant"}}`, encodeURIComponent(String(tenant)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            await setApiKeyToObject(localVarHeaderParameter, "Authorization", configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateTagCommandRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TagApi - functional programming interface
 * @export
 */
export const TagApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = TagApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateTagCommandRequest} [createTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createTag(tenant: string, createTagCommandRequest?: CreateTagCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CreateTagCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createTag(tenant, createTagCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TagApi.createTag']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteTag(id: number, tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteTag(id, tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TagApi.deleteTag']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPagedTagsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<TagPagedListModelApiPagedListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getPagedTagsList(tenant, filters, skip, take, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TagApi.getPagedTagsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTagsList(tenant: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<TagModelApiListResult>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getTagsList(tenant, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TagApi.getTagsList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateTagCommandRequest} [updateTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateTag(tenant: string, updateTagCommandRequest?: UpdateTagCommandRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UpdateTagCommandResponse>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateTag(tenant, updateTagCommandRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TagApi.updateTag']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * TagApi - factory interface
 * @export
 */
export const TagApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = TagApiFp(configuration)
    return {
        /**
         * 
         * @param {string} tenant 
         * @param {CreateTagCommandRequest} [createTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createTag(tenant: string, createTagCommandRequest?: CreateTagCommandRequest, options?: any): AxiosPromise<CreateTagCommandResponse> {
            return localVarFp.createTag(tenant, createTagCommandRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteTag(id: number, tenant: string, options?: any): AxiosPromise<void> {
            return localVarFp.deleteTag(id, tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {Array<string>} [filters] 
         * @param {number} [skip] 
         * @param {number} [take] 
         * @param {string} [order] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPagedTagsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: any): AxiosPromise<TagPagedListModelApiPagedListResult> {
            return localVarFp.getPagedTagsList(tenant, filters, skip, take, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTagsList(tenant: string, options?: any): AxiosPromise<TagModelApiListResult> {
            return localVarFp.getTagsList(tenant, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} tenant 
         * @param {UpdateTagCommandRequest} [updateTagCommandRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateTag(tenant: string, updateTagCommandRequest?: UpdateTagCommandRequest, options?: any): AxiosPromise<UpdateTagCommandResponse> {
            return localVarFp.updateTag(tenant, updateTagCommandRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TagApi - object-oriented interface
 * @export
 * @class TagApi
 * @extends {BaseAPI}
 */
export class TagApi extends BaseAPI {
    /**
     * 
     * @param {string} tenant 
     * @param {CreateTagCommandRequest} [createTagCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TagApi
     */
    public createTag(tenant: string, createTagCommandRequest?: CreateTagCommandRequest, options?: RawAxiosRequestConfig) {
        return TagApiFp(this.configuration).createTag(tenant, createTagCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TagApi
     */
    public deleteTag(id: number, tenant: string, options?: RawAxiosRequestConfig) {
        return TagApiFp(this.configuration).deleteTag(id, tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {Array<string>} [filters] 
     * @param {number} [skip] 
     * @param {number} [take] 
     * @param {string} [order] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TagApi
     */
    public getPagedTagsList(tenant: string, filters?: Array<string>, skip?: number, take?: number, order?: string, options?: RawAxiosRequestConfig) {
        return TagApiFp(this.configuration).getPagedTagsList(tenant, filters, skip, take, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TagApi
     */
    public getTagsList(tenant: string, options?: RawAxiosRequestConfig) {
        return TagApiFp(this.configuration).getTagsList(tenant, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} tenant 
     * @param {UpdateTagCommandRequest} [updateTagCommandRequest] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TagApi
     */
    public updateTag(tenant: string, updateTagCommandRequest?: UpdateTagCommandRequest, options?: RawAxiosRequestConfig) {
        return TagApiFp(this.configuration).updateTag(tenant, updateTagCommandRequest, options).then((request) => request(this.axios, this.basePath));
    }
}



