﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.DosageForm;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.DosageForms.ListQuery;

internal class GetDosageFormListQueryHandler(IDosageFormsRepository repo, IMapper mapper) : IRequestHandler<GetDosageFormListQueryRequest, ApiListResult<DosageFormModel>>
{
    public async Task<ApiListResult<DosageFormModel>> Handle(GetDosageFormListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync();
        return new ApiListResult<DosageFormModel>(mapper.Map<List<DosageFormModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
