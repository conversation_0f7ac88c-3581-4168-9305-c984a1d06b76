﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DosageForms;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetDosageFormListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DosageFormsApi dosageFormsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetDosageFormList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentDosageFormsCount = dbContext.DosageForms.Count();
        var dosageForm1 = CreateDosageFormsBuilder.Default().WithName(Fake.DosageForm.Name).Build();
        var dosageForm2 = CreateDosageFormsBuilder.Default().WithName(Fake.DosageForm.Name).Build();
        var dosageForm3 = CreateDosageFormsBuilder.Default().WithName(Fake.DosageForm.Name).Build();
        await dbContext.DosageForms.AddRangeAsync(dosageForm1, dosageForm2, dosageForm3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await dosageFormsApi.GetDosageFormListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentDosageFormsCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(dosageForm1.Name);
        responseNames.Should().Contain(dosageForm2.Name);
        responseNames.Should().Contain(dosageForm3.Name);

        dbContext.DosageForms.RemoveRange(dosageForm1, dosageForm2, dosageForm3);
        await dbContext.SaveChangesAsync();
    }


    [Fact]
    public async Task GetPagedDosageFormList_ValidRequest_ReturnsOk()
    {
        //Arrange
        var currentDosageFormCount = dbContext.DosageForms.Count();
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 56);

        //Act
        var responseObj = await dosageFormsApi.GetPagedDosageFormsListAsync(TenantConstants.DEFAULT_TENANT, null, currentDosageFormCount + 50, 10);

        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedDosageFormList_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 77);
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();

        //Act
        var responseObj = await dosageFormsApi.GetPagedDosageFormsListAsync(TenantConstants.DEFAULT_TENANT,
            new List<string> { $"name=>{dosageForm.Name}" }, 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Name.Equals(dosageForm.Name));
    }

    [Fact]
    public async Task GetPagedDosageFormList_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 25);

        //Act
        var responseObj = await dosageFormsApi.GetPagedDosageFormsListAsync(TenantConstants.DEFAULT_TENANT,
            null, 0, 10, "name=>desc");

        //Assert
        responseObj.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task GetPagedDosageFormList_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 55);

        //Act
        var responseObj = await dosageFormsApi.GetPagedDosageFormsListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15);

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.DosageForms.Clear();

        await dbContext.SaveChangesAsync();
    }
}
