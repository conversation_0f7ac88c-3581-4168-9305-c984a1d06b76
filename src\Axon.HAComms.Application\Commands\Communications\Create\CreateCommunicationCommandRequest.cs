using Axon.HAComms.Application.Commands.Comments.Create;
using MediatR;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Models.Application;

namespace Axon.HAComms.Application.Commands.Communications.Create;

public class CreateCommunicationCommandRequest(
    string subject,
    DateTime dateOfCommunication,
    int submissionTypeId,
    int countryId,
    ICollection<ApplicationModel> applications,
    CreateCommentCommandRequest comment) : CommunicationRequestModel(subject, dateOfCommunication, submissionTypeId, countryId, applications),
        IRequest<CreateCommunicationCommandResponse>
{
    public CreateCommentCommandRequest Comment { get; set; } = comment;
}
