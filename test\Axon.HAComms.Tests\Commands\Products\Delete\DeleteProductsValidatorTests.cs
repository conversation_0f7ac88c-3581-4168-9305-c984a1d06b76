﻿using Axon.HAComms.Application.Commands.Products.Delete;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Delete
{
    public class DeleteProductsValidatorTests
    {
        private readonly DeleteProductCommandValidator validator;

        public DeleteProductsValidatorTests()
        {
            validator = new DeleteProductCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var result = validator.TestValidate(new DeleteProductCommandRequest(0));
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var result = validator.TestValidate(new DeleteProductCommandRequest(1));
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
