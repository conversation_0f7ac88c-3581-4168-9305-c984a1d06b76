﻿using Axon.HAComms.Application.Queries.Products.ListQuery;
using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Tests.Builders.Products;
using FluentAssertions;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Products.ListQuery;

public class GetProductsListQueryHandlerTests
{
    private readonly GetProductsHandler handler;
    private readonly IProductsRepository productsRepo;

    public GetProductsListQueryHandlerTests()
    {
        productsRepo = Substitute.For<IProductsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new DrugSubstancesMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
            cfg.AddProfile(new DosageFormsMappingProfile());
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetProductsHandler(productsRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var isActive = true;
        var product1 = new ProductsBuilder().WithIsActive(isActive).Build();
        var product2 = new ProductsBuilder().WithIsActive(isActive).Build();
        var product3 = new ProductsBuilder().WithIsActive(isActive).Build();
        var products = new List<Domain.Entities.Product>
        {
            product1,
            product2,
            product3
        };

        productsRepo.GetItemsAsync(Arg.Any<Expression<Func<Domain.Entities.Product, object>>>()).Returns(products);

        var request = new GetProductsListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
    }
}
