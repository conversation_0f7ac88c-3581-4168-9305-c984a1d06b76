﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Extensions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class DrugSubstanceConfiguration : BaseEntityConfiguration<DrugSubstance>
{
    protected override void ConfigureEntity(EntityTypeBuilder<DrugSubstance> builder)
    {
        builder.HasIndex(d => new { d.Code, d.Tenant }).IsUnique();

        builder.Property(e => e.Name)
            .HasMaxLength(200);

        builder.Property(e => e.Code)
            .ConfigureCodeField();

        builder.Property(e => e.Description)
            .ConfigureDescriptionField();
    }
}
