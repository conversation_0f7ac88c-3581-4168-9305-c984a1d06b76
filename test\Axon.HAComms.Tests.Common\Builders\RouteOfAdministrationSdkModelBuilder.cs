﻿using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Tests.Common.Builders
{
    public class RouteOfAdministrationSdkModelBuilder
    {
        private int id = Fake.RouteOfAdministration.Id;
        private string name = Fake.RouteOfAdministration.Name;

        public static RouteOfAdministrationSdkModelBuilder Default() => new();

        public RouteOfAdministrationSdkModelBuilder WithName(string routeOfAdminName)
        {
            name = routeOfAdminName;
            return this;
        }

        public RouteOfAdministrationSdkModelBuilder WithId(int routeOfAdminId)
        {
            id = routeOfAdminId;
            return this;
        }

        public RouteOfAdministrationModel Build()
        {
            return new(id, name);
        }
    }
}
