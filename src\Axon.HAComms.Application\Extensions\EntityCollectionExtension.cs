﻿using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Exceptions;

namespace Axon.HAComms.Application.Extensions;

public static class EntityCollectionExtension
{
    public static void ValidateAllIdsExist<T>(this int[] requestIds, params int[] sourceIds) where T : BaseEntity
    {
        var missingIds = requestIds.Where(id => sourceIds.ToList().TrueForAll(x => x != id)).ToArray();

        if (missingIds.Length != 0)
        {
            throw new EntityNotFoundException(typeof(T).Name, string.Join(",", missingIds));
        }
    }
}
