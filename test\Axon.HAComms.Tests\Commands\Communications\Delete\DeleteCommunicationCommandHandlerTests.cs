﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Communications.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Communications;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Delete;

public class DeleteCommunicationCommandHandlerTests
{
    private readonly DeleteCommunicationCommandHandler handler;
    private readonly ICommentsRepository commentsRepo;
    private readonly ICommunicationsRepository communicationRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IUserProvider userProvider;
    private readonly IAuditService auditService;

    public DeleteCommunicationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");
        var logger = Substitute.For<ILogger<DeleteCommunicationCommandHandler>>();
        communicationRepo = Substitute.For<ICommunicationsRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_DELETED, AuditEventCategory.COMMUNICATIONS,
            AuditEventDescription.COMMUNICATION_DELETE, Arg.Any<Communication>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        handler = new DeleteCommunicationCommandHandler(commentsRepo, communicationRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider,
            auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var entity = new CommunicationsBuilder().WithIsCompleted(false).Build();
        communicationRepo.GetCommunicationAsync(Arg.Any<int>()).ReturnsForAnyArgs(entity);

        var request = new DeleteCommunicationCommandRequest(entity.Id);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_DELETED, AuditEventCategory.COMMUNICATIONS,
            AuditEventDescription.COMMUNICATION_DELETE, entity, Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidId_ReturnsSuccessResult()
    {
        // Arrange
        var entity = new CommunicationsBuilder().WithIsCompleted(false).Build();
        communicationRepo.GetCommunicationAsync(Arg.Any<int>()).ReturnsForAnyArgs(entity);
        
        var request = new DeleteCommunicationCommandRequest(entity.Id);
        var comments = new List<Comment> { new() };
        commentsRepo.GetFilteredComments(filter: c => c.CommunicationId == request.Id).ReturnsForAnyArgs(comments.BuildMock());

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        await communicationRepo.Received().GetCommunicationAsync(request.Id);
        commentsRepo.Received().GetFilteredComments(filter: Arg.Any<Expression<Func<Comment, bool>>>());
        communicationRepo.Received().UpdateItem(entity);
        await communicationRepo.Received(1).SaveChangesAsync(userProvider);
    }

    [Fact]
    public async Task Handle_CompletedEntity_ThrowsException()
    {
        // Arrange
        var entity = new CommunicationsBuilder().WithIsCompleted(true).Build();
        communicationRepo.GetCommunicationAsync(Arg.Any<int>()).ReturnsForAnyArgs(entity);

        await communicationRepo.GetItemAsync(Arg.Any<int>());

        var request = new DeleteCommunicationCommandRequest(entity.Id);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        var exception = await result.Should().ThrowAsync<Exception>();
        exception.And.Message.Should().Contain("Cannot delete completed communication!");
    }
}
