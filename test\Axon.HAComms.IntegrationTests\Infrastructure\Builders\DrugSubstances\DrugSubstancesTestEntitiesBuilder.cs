using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;

public static class DrugSubstancesTestEntitiesBuilder
{
    public static async Task<List<DrugSubstance>> Build(HACommsContext dbContext, int entries = 100)
    {
        var list = new List<DrugSubstance>();

        for (var i = 0; i < entries; i++)
        {
            list.Add(CreateDrugSubstancesBuilder.Default().Build());
        }

        dbContext.DrugSubstances.AddRange(list);
        await dbContext.SaveChangesAsync();

        return list;
    }
}
