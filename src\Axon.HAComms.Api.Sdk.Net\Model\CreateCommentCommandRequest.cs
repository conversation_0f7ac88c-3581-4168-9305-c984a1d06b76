/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CreateCommentCommandRequest
    /// </summary>
    [DataContract(Name = "CreateCommentCommandRequest")]
    public partial class CreateCommentCommandRequest : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateCommentCommandRequest" /> class.
        /// </summary>
        /// <param name="communicationId">communicationId.</param>
        /// <param name="description">description.</param>
        /// <param name="question">question.</param>
        /// <param name="response">response.</param>
        /// <param name="birdsLinkToBIResponse">birdsLinkToBIResponse.</param>
        /// <param name="birdsLinkToBISAMP">birdsLinkToBISAMP.</param>
        /// <param name="productExtensions">productExtensions.</param>
        /// <param name="drugSubstanceIds">drugSubstanceIds.</param>
        /// <param name="tagIds">tagIds.</param>
        /// <param name="isGeneralGuidance">isGeneralGuidance.</param>
        /// <param name="isQuestionIncluded">isQuestionIncluded.</param>
        public CreateCommentCommandRequest(int communicationId = default(int), string description = default(string), string question = default(string), string response = default(string), string birdsLinkToBIResponse = default(string), string birdsLinkToBISAMP = default(string), List<ProductExtensionCommentModel> productExtensions = default(List<ProductExtensionCommentModel>), List<int> drugSubstanceIds = default(List<int>), List<int> tagIds = default(List<int>), bool isGeneralGuidance = default(bool), bool isQuestionIncluded = default(bool))
        {
            this.CommunicationId = communicationId;
            this.Description = description;
            this.Question = question;
            this.Response = response;
            this.BirdsLinkToBIResponse = birdsLinkToBIResponse;
            this.BirdsLinkToBISAMP = birdsLinkToBISAMP;
            this.ProductExtensions = productExtensions;
            this.DrugSubstanceIds = drugSubstanceIds;
            this.TagIds = tagIds;
            this.IsGeneralGuidance = isGeneralGuidance;
            this.IsQuestionIncluded = isQuestionIncluded;
        }

        /// <summary>
        /// Gets or Sets CommunicationId
        /// </summary>
        [DataMember(Name = "communicationId", EmitDefaultValue = false)]
        public int CommunicationId { get; set; }

        /// <summary>
        /// Gets or Sets Description
        /// </summary>
        [DataMember(Name = "description", EmitDefaultValue = true)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets Question
        /// </summary>
        [DataMember(Name = "question", EmitDefaultValue = true)]
        public string Question { get; set; }

        /// <summary>
        /// Gets or Sets Response
        /// </summary>
        [DataMember(Name = "response", EmitDefaultValue = true)]
        public string Response { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBIResponse
        /// </summary>
        [DataMember(Name = "birdsLinkToBIResponse", EmitDefaultValue = true)]
        public string BirdsLinkToBIResponse { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBISAMP
        /// </summary>
        [DataMember(Name = "birdsLinkToBISAMP", EmitDefaultValue = true)]
        public string BirdsLinkToBISAMP { get; set; }

        /// <summary>
        /// Gets or Sets ProductExtensions
        /// </summary>
        [DataMember(Name = "productExtensions", EmitDefaultValue = true)]
        public List<ProductExtensionCommentModel> ProductExtensions { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstanceIds
        /// </summary>
        [DataMember(Name = "drugSubstanceIds", EmitDefaultValue = true)]
        public List<int> DrugSubstanceIds { get; set; }

        /// <summary>
        /// Gets or Sets TagIds
        /// </summary>
        [DataMember(Name = "tagIds", EmitDefaultValue = true)]
        public List<int> TagIds { get; set; }

        /// <summary>
        /// Gets or Sets IsGeneralGuidance
        /// </summary>
        [DataMember(Name = "isGeneralGuidance", EmitDefaultValue = true)]
        public bool IsGeneralGuidance { get; set; }

        /// <summary>
        /// Gets or Sets IsQuestionIncluded
        /// </summary>
        [DataMember(Name = "isQuestionIncluded", EmitDefaultValue = true)]
        public bool IsQuestionIncluded { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CreateCommentCommandRequest {\n");
            sb.Append("  CommunicationId: ").Append(CommunicationId).Append("\n");
            sb.Append("  Description: ").Append(Description).Append("\n");
            sb.Append("  Question: ").Append(Question).Append("\n");
            sb.Append("  Response: ").Append(Response).Append("\n");
            sb.Append("  BirdsLinkToBIResponse: ").Append(BirdsLinkToBIResponse).Append("\n");
            sb.Append("  BirdsLinkToBISAMP: ").Append(BirdsLinkToBISAMP).Append("\n");
            sb.Append("  ProductExtensions: ").Append(ProductExtensions).Append("\n");
            sb.Append("  DrugSubstanceIds: ").Append(DrugSubstanceIds).Append("\n");
            sb.Append("  TagIds: ").Append(TagIds).Append("\n");
            sb.Append("  IsGeneralGuidance: ").Append(IsGeneralGuidance).Append("\n");
            sb.Append("  IsQuestionIncluded: ").Append(IsQuestionIncluded).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
