﻿using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Infrastructure.Auth;
using Microsoft.AspNetCore.TestHost;
using NLog;
using LogLevel = NLog.LogLevel;

namespace Axon.HAComms.IntegrationTests.Infrastructure
{
    public class ApiTestFixture : IDisposable
    {
        private const string EMAIL_HEADER = HeaderOverrides.EMAIL_OVERRIDE_HEADER_KEY;
        private const string ROLE_HEADER = HeaderOverrides.ROLE_OVER_HEADER_KEY;
        private const string OBJECT_ID_HEADER = HeaderOverrides.OBJECT_ID_OVERRIDE_HEADER_KEY;

        private readonly TestServer server;
        private bool disposed;
        public IServiceProvider Services { get; private set; }
        public HttpClient ApiClient { get; private set; }
        public HACommsContext DbContext { get; private set; }

        public ApiTestFixture()
        {
            var logger = LogManager.GetCurrentClassLogger();
            try
            {
                logger.Log(LogLevel.Info, $"{nameof(ApiTestFixture)} init");
                logger.Log(LogLevel.Debug, "Creating Web Application Factory...");
                var factory = new ApiWebApplicationFactory();
                server = factory.Server;
                Services = server.Services;
                DbContext = server.Services.GetRequiredService<HACommsContext>();
                logger.Log(LogLevel.Debug, "Setting up API http client...");
                ApiClient = factory.CreateClient();
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Fatal, e, $"{nameof(ApiTestFixture)} init error - (exceptions are ignored by the xunit test runner at this startup point so crashing the process :O)");
                Environment.Exit(-1);
                throw;
            }
            logger.Log(LogLevel.Info, $"{nameof(ApiTestFixture)} init completed successfully");
        }

        public void AddHeaders(string value, Guid? objectId = null)
        {
            RemoveHeaders();

            ApiClient.DefaultRequestHeaders.Add(EMAIL_HEADER, value);
            ApiClient.DefaultRequestHeaders.Add(ROLE_HEADER, value);
            ApiClient.DefaultRequestHeaders.Add(OBJECT_ID_HEADER, (objectId ?? Guid.NewGuid()).ToString());

        }

        public void RemoveHeaders()
        {
            ApiClient.DefaultRequestHeaders.Remove(EMAIL_HEADER);
            ApiClient.DefaultRequestHeaders.Remove(ROLE_HEADER);
            ApiClient.DefaultRequestHeaders.Remove(OBJECT_ID_HEADER);
        }

        public void Dispose()
        {
            // Place tear down code here
            server.Dispose();
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
            {
                return;
            }

            if (disposing)
            {
                DbContext.Database.EnsureDeleted();
            }

            disposed = true;
        }
    }
}
