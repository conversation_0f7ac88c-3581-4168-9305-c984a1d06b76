/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Api.Sdk.Net.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ITagApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <returns>CreateTagCommandResponse</returns>
        CreateTagCommandResponse CreateTag(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateTagCommandResponse</returns>
        ApiResponse<CreateTagCommandResponse> CreateTagWithHttpInfo(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void DeleteTag(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteTagWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>TagPagedListModelApiPagedListResult</returns>
        TagPagedListModelApiPagedListResult GetPagedTagsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of TagPagedListModelApiPagedListResult</returns>
        ApiResponse<TagPagedListModelApiPagedListResult> GetPagedTagsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>TagModelApiListResult</returns>
        TagModelApiListResult GetTagsList(string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of TagModelApiListResult</returns>
        ApiResponse<TagModelApiListResult> GetTagsListWithHttpInfo(string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <returns>UpdateTagCommandResponse</returns>
        UpdateTagCommandResponse UpdateTag(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateTagCommandResponse</returns>
        ApiResponse<UpdateTagCommandResponse> UpdateTagWithHttpInfo(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ITagApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateTagCommandResponse</returns>
        System.Threading.Tasks.Task<CreateTagCommandResponse> CreateTagAsync(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateTagCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CreateTagCommandResponse>> CreateTagWithHttpInfoAsync(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteTagAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteTagWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TagPagedListModelApiPagedListResult</returns>
        System.Threading.Tasks.Task<TagPagedListModelApiPagedListResult> GetPagedTagsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TagPagedListModelApiPagedListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<TagPagedListModelApiPagedListResult>> GetPagedTagsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TagModelApiListResult</returns>
        System.Threading.Tasks.Task<TagModelApiListResult> GetTagsListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TagModelApiListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<TagModelApiListResult>> GetTagsListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateTagCommandResponse</returns>
        System.Threading.Tasks.Task<UpdateTagCommandResponse> UpdateTagAsync(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateTagCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UpdateTagCommandResponse>> UpdateTagWithHttpInfoAsync(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ITagApi : ITagApiSync, ITagApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class TagApi : IDisposable, ITagApi
    {
        private Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public TagApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public TagApi(string basePath)
        {
            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public TagApi(Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public TagApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public TagApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public TagApi(HttpClient client, Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TagApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public TagApi(Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient client, Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient asyncClient, Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <returns>CreateTagCommandResponse</returns>
        public CreateTagCommandResponse CreateTag(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateTagCommandResponse> localVarResponse = CreateTagWithHttpInfo(tenant, createTagCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateTagCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateTagCommandResponse> CreateTagWithHttpInfo(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->CreateTag");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createTagCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CreateTagCommandResponse>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateTagCommandResponse</returns>
        public async System.Threading.Tasks.Task<CreateTagCommandResponse> CreateTagAsync(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateTagCommandResponse> localVarResponse = await CreateTagWithHttpInfoAsync(tenant, createTagCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateTagCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateTagCommandResponse>> CreateTagWithHttpInfoAsync(string tenant, CreateTagCommandRequest? createTagCommandRequest = default(CreateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->CreateTag");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createTagCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CreateTagCommandResponse>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void DeleteTag(int id, string tenant)
        {
            DeleteTagWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> DeleteTagWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->DeleteTag");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/{tenant}/v1/Tags/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteTagAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteTagWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> DeleteTagWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->DeleteTag");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/{tenant}/v1/Tags/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>TagPagedListModelApiPagedListResult</returns>
        public TagPagedListModelApiPagedListResult GetPagedTagsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagPagedListModelApiPagedListResult> localVarResponse = GetPagedTagsListWithHttpInfo(tenant, filters, skip, take, order);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of TagPagedListModelApiPagedListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagPagedListModelApiPagedListResult> GetPagedTagsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->GetPagedTagsList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<TagPagedListModelApiPagedListResult>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedTagsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TagPagedListModelApiPagedListResult</returns>
        public async System.Threading.Tasks.Task<TagPagedListModelApiPagedListResult> GetPagedTagsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagPagedListModelApiPagedListResult> localVarResponse = await GetPagedTagsListWithHttpInfoAsync(tenant, filters, skip, take, order, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TagPagedListModelApiPagedListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagPagedListModelApiPagedListResult>> GetPagedTagsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->GetPagedTagsList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<TagPagedListModelApiPagedListResult>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedTagsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>TagModelApiListResult</returns>
        public TagModelApiListResult GetTagsList(string tenant)
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagModelApiListResult> localVarResponse = GetTagsListWithHttpInfo(tenant);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of TagModelApiListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagModelApiListResult> GetTagsListWithHttpInfo(string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->GetTagsList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<TagModelApiListResult>("/{tenant}/v1/Tags/all", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTagsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TagModelApiListResult</returns>
        public async System.Threading.Tasks.Task<TagModelApiListResult> GetTagsListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagModelApiListResult> localVarResponse = await GetTagsListWithHttpInfoAsync(tenant, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TagModelApiListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<TagModelApiListResult>> GetTagsListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->GetTagsList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<TagModelApiListResult>("/{tenant}/v1/Tags/all", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTagsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <returns>UpdateTagCommandResponse</returns>
        public UpdateTagCommandResponse UpdateTag(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateTagCommandResponse> localVarResponse = UpdateTagWithHttpInfo(tenant, updateTagCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateTagCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateTagCommandResponse> UpdateTagWithHttpInfo(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->UpdateTag");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateTagCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<UpdateTagCommandResponse>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateTagCommandResponse</returns>
        public async System.Threading.Tasks.Task<UpdateTagCommandResponse> UpdateTagAsync(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateTagCommandResponse> localVarResponse = await UpdateTagWithHttpInfoAsync(tenant, updateTagCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateTagCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateTagCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateTagCommandResponse>> UpdateTagWithHttpInfoAsync(string tenant, UpdateTagCommandRequest? updateTagCommandRequest = default(UpdateTagCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling TagApi->UpdateTag");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateTagCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<UpdateTagCommandResponse>("/{tenant}/v1/Tags", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateTag", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
