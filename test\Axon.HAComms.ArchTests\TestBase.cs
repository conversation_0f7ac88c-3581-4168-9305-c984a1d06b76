﻿using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Infrastructure.Persistance.Repository;
using NetArchTest.Rules;
using System.Reflection;
using FluentAssertions;

namespace Axon.HAComms.ArchTests
{
    public abstract class TestBase
    {
        protected static Assembly ApplicationAssembly => typeof(Program).Assembly;

        protected static Assembly DomainAssembly => typeof(MultiTenantEntity).Assembly;

        protected static Assembly InfrastructureAssembly => typeof(CommentsRepository).Assembly;

        protected static void AssertAreImmutable(IEnumerable<Type> types)
        {
            var failingTypes = new List<Type>();
            foreach (var type in types)
            {
                if (type.GetFields().Any(x => !x.IsInitOnly) || type.GetProperties().Any(x => x.CanWrite))
                {
                    failingTypes.Add(type);
                }
            }

            AssertFailingTypes(failingTypes);
        }

        protected static void AssertFailingTypes(IEnumerable<Type>? types)
        {
            if (types is null)
            {
                types.Should().BeNull();
            }
            else
            {
                types.Should().BeEmpty();
            }
        }

        protected static void AssertArchTestResult(TestResult result)
        {
            AssertFailingTypes(result.FailingTypes);
        }
    }
}
