﻿using Shouldly;
using System.Reflection;

namespace Axon.HAComms.ArchTests.Extensions;

public static class ReflectionExtensions
{
    public static void ShouldHaveAttribute(this IEnumerable<MethodInfo> methods, IEnumerable<Type> attributeTypes)
    {
        foreach (var method in methods)
        {
            method
                .GetCustomAttributes().Where(x => attributeTypes.Contains(x.GetType()))
                .ShouldNotBeEmpty(
                    $"Controller action: {method.DeclaringType}.{method.Name} is missing attribute from set '{string.Join(", ", attributeTypes.Select(x => x.Name))}'");
        }
    }
}
