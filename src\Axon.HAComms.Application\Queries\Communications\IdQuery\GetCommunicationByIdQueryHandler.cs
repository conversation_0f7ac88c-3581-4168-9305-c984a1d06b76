using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Models.Products;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Application.Queries.Communications.IdQuery;

internal class GetCommunicationByIdQueryHandler(ICommunicationsRepository repo, ICommentsRepository commentsRepository, IMapper mapper)
    : IRequestHandler<GetCommunicationByIdQueryRequest, CommunicationModel>
{
    public async Task<CommunicationModel> Handle(GetCommunicationByIdQueryRequest request, CancellationToken cancellationToken)
    {
        var entity = await repo.GetItemAsync(request.Id);
        var result = mapper.Map<CommunicationModel>(entity);

        var communicationProducts = await repo.GetCommunicationProducts(request.Id);
        var uniqueProducts = communicationProducts.GroupBy(p => p?.Id ?? 0)
            .Where(g => g.Key != 0)
            .Select(g => new ProductDtoModel(g.Key, g.<PERSON>Or<PERSON>efault()?.Name ?? string.Empty)).ToArray();
        result.Products = uniqueProducts;

        result.GeneralGuidanceCommentsCount = await commentsRepository.GetFilteredComments(filter: c => c.CommunicationId == entity.Id && c.IsGeneralGuidance).CountAsync(cancellationToken);

        return result;
    }
}
