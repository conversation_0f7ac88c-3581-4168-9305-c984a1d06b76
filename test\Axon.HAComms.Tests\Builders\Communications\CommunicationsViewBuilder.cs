﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Communications
{
    public class CommunicationsViewBuilder : IBuilder<CommunicationsView>
    {
        private string subject;
        private int countryId;
        private string countryName;
        private string productNames;
        private DateTime dateOfCommunication;
        private DateTime createdDate;
        private string createdBy;
        private bool isCompleted;

        public CommunicationsViewBuilder()
        {
            subject = Fake.Communication.Subject;
            countryId = Fake.Country.Id;
            countryName = Fake.Country.Name;
            productNames = Fake.Product.Name;
            dateOfCommunication = DateTime.Now;
            createdDate = DateTime.Now;
            createdBy = Fake.Communication.CreatedBy;
            isCompleted = Fake.Communication.IsCompleted;
        }

        public CommunicationsView Build()
        {
            return new()
            {
                Subject = subject,
                CountryId = countryId,
                CountryName = countryName,
                ProductNames = productNames,
                DateOfCommunication = dateOfCommunication,
                CreatedDate = createdDate,
                CreatedBy = createdBy,
                IsCompleted = isCompleted
            };
        }

        public CommunicationsViewBuilder WithSubject(string subject)
        {
            this.subject = subject;
            return this;
        }

        public CommunicationsViewBuilder WithCountryId(int countryId)
        {
            this.countryId = countryId;
            return this;
        }

        public CommunicationsViewBuilder WithCountryName(string countryName)
        {
            this.countryName = countryName;
            return this;
        }

        public CommunicationsViewBuilder WithProductNames(string productNames)
        {
            this.productNames = productNames;
            return this;
        }

        public CommunicationsViewBuilder WithIsCompleted(bool isCompleted)
        {
            this.isCompleted = isCompleted;
            return this;
        }

        public CommunicationsViewBuilder WithDateOfCommunication(DateTime dateOfCommunication)
        {
            this.dateOfCommunication = dateOfCommunication;
            return this;
        }

        public CommunicationsViewBuilder WithCreatedDate(DateTime createdDate)
        {
            this.createdDate = createdDate;
            return this;
        }
        public CommunicationsViewBuilder WithCreatedBy(string createdBy)
        {
            this.createdBy = createdBy;
            return this;
        }

    }
}
