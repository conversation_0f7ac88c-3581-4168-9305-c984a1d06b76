﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IRouteOfAdministrationRepository : IRepository<RouteOfAdministration>
{
    Task<IEnumerable<RouteOfAdministration>> GetItemsAsync();

    Task<RouteOfAdministration> GetItemAsync(int id);

    IQueryable<RouteOfAdministration> GetQueryableItems();

    Task<RouteOfAdministration[]> GetAllByIdsAsync(params int[] ids);

    Task<bool> ExistsAsync(Expression<Func<RouteOfAdministration, bool>> filter);
}
