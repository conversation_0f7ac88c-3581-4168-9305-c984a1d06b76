﻿using Axon.HAComms.Application.Models.Application;

namespace Axon.HAComms.Application.Models.Communications;

public class CommunicationRequestModel(
    string subject,
    DateTime dateOfCommunication,
    int submissionTypeId,
    int countryId,
    ICollection<ApplicationModel> applications)
{
    public string Subject { get; } = subject;

    public int SubmissionTypeId { get; } = submissionTypeId;

    public int CountryId { get; } = countryId;

    public DateTime DateOfCommunication { get; } = dateOfCommunication;

    public ICollection<ApplicationModel> Applications { get; } = applications;
}
