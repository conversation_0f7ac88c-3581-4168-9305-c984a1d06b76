﻿namespace Axon.HAComms.Application.Extensions;

public static class StringExtensions
{
    public static int[] ToIntList(this string str)
    {
        return string.IsNullOrEmpty(str) ? Array.Empty<int>() : str.Split(',').Select(int.Parse).ToArray();
    }

    public static string EscapeSpecialCharacters(this string str)
    {
        var specialCharacters = new char[] { '!', ':', '(', ')', '{', '}', '[', ']', '^', '"', '\\', '/' };

        var resultString = str;
        foreach (var character in specialCharacters.Where(ch => str.LastIndexOf(ch) > -1))
        {
            resultString = resultString.Replace(character.ToString(), $"\\{character}");
        }

        return resultString;
    }

    public static (string, string) SplitTableFilter(this string str)
    {
        var splitFilter = str.Split("=>");
        var filterName = splitFilter[0];
        var filterValue = splitFilter[^1];
        return (filterName, filterValue);
    }
}
