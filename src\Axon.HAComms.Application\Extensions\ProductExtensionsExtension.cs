﻿using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Extensions;

public static class ProductExtensionsExtension
{
    public static ProductExtension[] ToProductExtensionEntity(
        this ProductExtensionModel[] productExtensionModels,
        DosageForm[] dosageForms,
        RouteOfAdministration[] routeOfAdministrationEntities,
        Product product)
    {
        var dosageFormMap = dosageForms.ToDictionary(x => x.Id, x => x);
        var extensions = productExtensionModels.Select(dto =>
        {
            var dosageForm = dosageFormMap[dto.DosageFormId];
            var routeOfAdminIdsForExtension = dto.RouteOfAdministrationIds.Distinct().ToArray();
            var routeOfAdministrationsForExtension = routeOfAdministrationEntities.Where(x => routeOfAdminIdsForExtension.Contains(x.Id)).ToList();
            var result = new ProductExtension { PCID = dto.PCID, DosageFormId = dosageForm.Id, IsActive = dto.IsActive, ProductId = product.Id };
            result.SetRouteOfAdministrations(routeOfAdministrationsForExtension);
            return result;
        }).ToArray();

        return extensions;
    }
}
