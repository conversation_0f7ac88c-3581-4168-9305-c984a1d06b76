using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Interfaces;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Builders;

public static class ExpressionBuilder
{
    public static Expression<Func<DrugSubstance, bool>>? BuildDrugSubstance(string[] filters)
    {
        Expression<Func<DrugSubstance, bool>>? expression = null;

        foreach (var filter in filters)
        {
            var (filterName, filterValue) = filter.SplitTableFilter();

            if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
            {
                switch (filterName.ToLower())
                {
                    case TableFilterConstants.Name:
                        expression = expression.AndAlso(x => (x.Name != null && x.Name.Contains(filterValue)));
                        break;
                    case TableFilterConstants.Code:
                        expression = expression.AndAlso(x => x.Code.Contains(filterValue));
                        break;
                    case TableFilterConstants.CreatedDate:
                        expression = expression.BuildWithCreatedDate(filterValue);
                        break;
                    case TableFilterConstants.LastUpdatedDate:
                        expression = expression.BuildWithLastUpdatedDate(filterValue);
                        break;
                    case TableFilterConstants.LastUpdatedBy:
                        expression = expression.BuildWithLastUpdatedBy(filterValue);
                        break;
                }
            }
        }

        return expression;
    }

    public static Expression<Func<ProductExtension, bool>>? BuildProductExtensions(string[] filters)
    {
        Expression<Func<ProductExtension, bool>>? expression = null;

        foreach (var filter in filters)
        {
            var (filterName, filterValue) = filter.SplitTableFilter();

            if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
            {
                switch (filterName.ToLower())
                {
                    case TableFilterConstants.Name:
                        expression = expression.AndAlso(x => x.Product.Name.Contains(filterValue));
                        break;
                    case TableFilterConstants.IsActive:
                        expression = expression.AndAlso(x => x.Product.IsActive.Equals(Convert.ToBoolean(filterValue)));
                        break;
                    case TableFilterConstants.Substances:
                        expression = expression.AndAlso(pe => pe.Product.DrugSubstances.Any(s => s.Code.Contains(filterValue)));
                        break;
                    case TableFilterConstants.ProductCode:
                        expression = expression.AndAlso(x => x.PCID.Contains(filterValue));
                        break;
                    case TableFilterConstants.RouteOfAdministration:
                    {
                        var filterRouteOfAdminIds = filterValue.ToIntList();
                        expression = expression.AndAlso(x => x.RouteOfAdministrations.Select(r => r.Id).Any(r => filterRouteOfAdminIds.Contains(r)));
                    }
                        break;
                    case TableFilterConstants.DosageForm: 
                    {
                        var filterDosageFormIds = filterValue.ToIntList();
                        expression = expression.AndAlso(x => filterDosageFormIds.Length != 0 && filterDosageFormIds.Contains(x.DosageForm.Id));
                    }
                        break;
                }
            }
        }

        return expression;
    }

    public static Expression<Func<CommunicationsView, bool>>? BuildCommunications(string[] filters)
    {
        Expression<Func<CommunicationsView, bool>>? expression = null;

        foreach (var filter in filters)
        {
            var (filterName, filterValue) = filter.SplitTableFilter();

            if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
            {
                switch (filterName.ToLower())
                {
                    case TableFilterConstants.Subject:
                        expression = expression.AndAlso(x => x.Subject!.Contains(filterValue));
                        break;
                    case TableFilterConstants.CountryId:
                        expression = expression.AndAlso(x => x.CountryId == int.Parse(filterValue));
                        break;
                    case TableFilterConstants.DateOfCommunication:
                        expression = expression.AndAlso(x => x.DateOfCommunication.Date.ToString().Contains(filterValue));
                        break;
                    case TableFilterConstants.ProductNames:
                        expression = expression.AndAlso(x => x.ProductNames!.Contains(filterValue));
                        break;
                    case TableFilterConstants.CreatedDate:
                        expression = expression.BuildWithCreatedDate(filterValue);
                        break;
                    case TableFilterConstants.CreatedBy:
                        expression = expression.BuildWithCreatedBy(filterValue);
                        break;
                    case TableFilterConstants.IsCompleted:
                        expression = expression.AndAlso(x => x.IsCompleted.Equals(Convert.ToBoolean(filterValue)));
                        break;
                }
            }
        }

        return expression;
    }

    public static Expression<Func<RouteOfAdministration, bool>>? BuildRouteOfAdministration(string[] filters)
    {
        Expression<Func<RouteOfAdministration, bool>>? expression = null;

        expression = BuildExpressionWithName(filters, expression);

        return expression;
    }

    public static Expression<Func<DosageForm, bool>>? BuildDosageForm(string[] filters)
    {
        Expression<Func<DosageForm, bool>>? expression = null;

        expression = BuildExpressionWithName(filters, expression);

        return expression;
    }

    public static Expression<Func<Tag, bool>>? BuildTags(string[] filters)
    {
        Expression<Func<Tag, bool>>? expression = null;

        expression = BuildExpressionWithName(filters, expression);

        return expression;
    }

    private static Expression<Func<T, bool>>? BuildExpressionWithName<T>(string[] filters, Expression<Func<T, bool>>? expression)
        where T : BaseEntity, IEntityWithName
    {
        foreach (var filter in filters)
        {
            var (filterName, filterValue) = filter.SplitTableFilter();

            if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
            {
                switch (filterName.ToLower())
                {
                    case TableFilterConstants.Name:
                        expression = expression.BuildWithName(filterValue);
                        break;
                    case TableFilterConstants.CreatedDate:
                        expression = expression.BuildWithCreatedDate(filterValue);
                        break;
                    case TableFilterConstants.CreatedBy:
                        expression = expression.BuildWithCreatedBy(filterValue);
                        break;
                    case TableFilterConstants.LastUpdatedDate:
                        expression = expression.BuildWithLastUpdatedDate(filterValue);
                        break;
                    case TableFilterConstants.LastUpdatedBy:
                        expression = expression.BuildWithLastUpdatedBy(filterValue);
                        break;
                }
            }
        }

        return expression;
    }
}
