﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Comments.Update;

internal class UpdateCommentCommandHandler(
    ICommunicationService communicationService,
    ICommentsRepository commentsRepo,
    IMapper mapper,
    ILogger<UpdateCommentCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<UpdateCommentCommandRequest, UpdateCommentCommandResponse>
{
    public async Task<UpdateCommentCommandResponse> Handle(UpdateCommentCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await commentsRepo.GetItemWithAllIncludesAsync(request.Id);
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.COMMENT_UPDATED, AuditEventCategory.COMMENTS, AuditEventDescription.COMMENT_UPDATE, entity,
            async () =>
            {
                await communicationService.ValidateAndCreateCommentAsync(request, entity);
                commentsRepo.UpdateItem(entity);
                await commentsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Comment {Id} updated successfully.", request.Id);
            });
       
        var response = mapper.Map<UpdateCommentCommandResponse>(entity);
        return response;
    }
}
