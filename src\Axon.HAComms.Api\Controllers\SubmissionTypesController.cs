﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Models.SubmissionType;
using Axon.HAComms.Application.Queries.SubmissionTypes.ListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/SubmissionTypes")]
public class SubmissionTypesController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all submission types
    /// </summary>
    /// <returns>All submission types</returns>
    [HttpGet(Name = "GetSubmissionTypeList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewSubmissionType))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<SubmissionTypeModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetSubmissionTypeListAsync()
        => await Send(new GetSubmissionTypeListQueryRequest());
}
