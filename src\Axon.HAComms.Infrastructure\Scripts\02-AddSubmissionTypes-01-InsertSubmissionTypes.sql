DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

INSERT INTO [dbo].[SubmissionTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
VALUES      
(N'CEP', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Change to CTAA', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Change to MAA', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'DMF', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Original CTAA', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Original MAA', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Renewal', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'None', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted)