using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations.Extensions;

public static class PropertyBuilderExtensions
{
    public static PropertyBuilder<string?> ConfigureAuditField(this PropertyBuilder<string?> builder, int maxLength = 256)
    {
        return builder
            .IsRequired(false)
            .HasMaxLength(maxLength);
    }

    public static PropertyBuilder<DateTime> ConfigureDateTime2(this PropertyBuilder<DateTime> builder)
    {
        return builder.HasColumnType("datetime2");
    }

    public static PropertyBuilder<string> ConfigureNameField(this PropertyBuilder<string> builder, int maxLength = 200)
    {
        return builder
            .IsRequired()
            .HasMaxLength(maxLength);
    }

    public static PropertyBuilder<string?> ConfigureDescriptionField(this PropertyBuilder<string?> builder, int maxLength = 500)
    {
        return builder.HasMaxLength(maxLength);
    }

    public static PropertyBuilder<string> ConfigureCodeField(this PropertyBuilder<string> builder, int maxLength = 200)
    {
        return builder
            .IsRequired()
            .HasMaxLength(maxLength);
    }
}
