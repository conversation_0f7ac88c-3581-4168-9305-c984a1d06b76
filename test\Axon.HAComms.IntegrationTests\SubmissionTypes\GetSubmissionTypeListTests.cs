﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.SubmissionTypes;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetSubmissionTypeListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly SubmissionTypesApi api = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetSubmissionTypeList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentSubmissionTypesCount = dbContext.SubmissionTypes.Count();
        var submissionType1 = new SubmissionType() { Name = Fake.SubmissionType.Name };
        var submissionType2 = new SubmissionType() { Name = Fake.SubmissionType.Name };
        var submissionType3 = new SubmissionType() { Name = Fake.SubmissionType.Name };
        await dbContext.SubmissionTypes.AddRangeAsync(submissionType1, submissionType2, submissionType3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await api.GetSubmissionTypeListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentSubmissionTypesCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(submissionType1.Name);
        responseNames.Should().Contain(submissionType2.Name);
        responseNames.Should().Contain(submissionType3.Name);

        dbContext.SubmissionTypes.RemoveRange(submissionType1, submissionType2, submissionType3);
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public Task DisposeAsync() => Task.CompletedTask;
}
