﻿using MediatR;
using NetArchTest.Rules;
using Xunit;

namespace Axon.HAComms.ArchTests
{
    public class ApplicationTests : TestBase
    {
        [Fact]
        public void Command_Should_Be_Immutable()
        {
            var types = Types.InAssembly(ApplicationAssembly)
                .That()
                .ImplementInterface(typeof(IRequest))
                .Or()
                .ImplementInterface(typeof(IRequest<>))
                .GetTypes();

            AssertAreImmutable(types);
        }

        [Fact]
        public void Handler_Should_Have_Name_EndingWith_Handler()
        {
            var result = Types.InAssembly(ApplicationAssembly)
                .That()
                .ImplementInterface(typeof(IRequestHandler<>))
                .Should()
                .HaveNameEndingWith("Handler")
                .GetResult();

            AssertArchTestResult(result);
        }

        [Fact]
        public void Command_And_Query_Handlers_Should_Not_Be_Public()
        {
            var types = Types.InAssembly(ApplicationAssembly)
                .That()
                .ImplementInterface(typeof(IRequestHandler<,>))
                .Or()
                .ImplementInterface(typeof(IRequestHandler<>))
                .Should().NotBePublic().GetResult().FailingTypes;

            AssertFailingTypes(types);
        }
    }
}
