﻿using Axon.HAComms.Application.Commands.Communications.Delete;
using Axon.HAComms.Application.Commands.DrugSubstances.Delete;
using Axon.HAComms.Application.Commands.Products.Delete;
using Axon.HAComms.Tests.Common;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Delete
{
    public class DeleteCommunicationValidatorTests
    {
        private readonly DeleteCommunicationCommandValidator validator;

        public DeleteCommunicationValidatorTests()
        {
            validator = new DeleteCommunicationCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            // Arrange
            var request = new DeleteCommunicationCommandRequest(default);

            // Act
            var result = validator.TestValidate(request);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            // Arrange
            var id = Fake.Communication.Id;

            var request = new DeleteCommunicationCommandRequest(id);

            // Act
            var result = validator.TestValidate(request);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
