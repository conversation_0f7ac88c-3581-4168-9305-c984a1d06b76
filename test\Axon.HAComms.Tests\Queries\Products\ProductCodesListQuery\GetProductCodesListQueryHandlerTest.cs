﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Queries.ProductCodes.ListQuery;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Products.ProductCodesListQuery;

public class GetProductCodesListQueryHandlerTest
{
    private readonly GetProductCodeListQueryHandler handler;
    private readonly IProductExtensionsRepository productExtensionsRepository;

    public GetProductCodesListQueryHandlerTest()
    {
        productExtensionsRepository = Substitute.For<IProductExtensionsRepository>();
        handler = new GetProductCodeListQueryHandler(productExtensionsRepository);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = new List<string>
        {
            Fake.ProductExtension.PCID,
            Fake.ProductExtension.PCID,
            Fake.ProductExtension.PCID
        };

        productExtensionsRepository.GetFilteredListAsync(
            Arg.Any<Expression<Func<Domain.Entities.ProductExtension, bool>>>(),
            Arg.Any<Expression<Func<Domain.Entities.ProductExtension, string>>>()
        ).Returns(queryableItems);

        var request = new GetProductCodeListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().Contain(queryableItems[0]);
        result.Data.Should().Contain(queryableItems[1]);
        result.Data.Should().Contain(queryableItems[2]);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = new List<string>
        {
            "test3",
            "test2",
            "test1",
        };

        productExtensionsRepository.GetFilteredListAsync(
            Arg.Any<Expression<Func<Domain.Entities.ProductExtension, bool>>>(),
            Arg.Any<Expression<Func<Domain.Entities.ProductExtension, string>>>()
        ).Returns(queryableItems);

        var request = new GetProductCodeListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data[0].Should().Be(queryableItems[2]);
        result.Data[1].Should().Be(queryableItems[1]);
        result.Data[2].Should().Be(queryableItems[0]);
    }
}
