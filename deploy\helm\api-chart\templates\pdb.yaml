{{ if .Values.minAvailability }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "axon-hacomms-api.fullname" . }}
  labels:
    {{- include "axon-hacomms-api.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_21=Default namespace should not be used
spec:
  minAvailable: {{ .Values.minAvailability }}
  selector:
    matchLabels:
      {{- include "axon-hacomms-api.selectorLabels" . | nindent 6 }}
{{ end }}