﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.Products.Create;
using Axon.HAComms.Application.Commands.Products.Delete;
using Axon.HAComms.Application.Commands.Products.Update;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Application.Queries.ProductCodes.ListQuery;
using Axon.HAComms.Application.Queries.Products.IdQuery;
using Axon.HAComms.Application.Queries.Products.ListQuery;
using Axon.HAComms.Application.Queries.Products.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Products")]
public class ProductsController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get paged products
    /// </summary>
    /// <returns>All products</returns>
    [HttpGet(Name = "GetPagedProductsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewProduct))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<ProductPagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
        => await Send(new GetProductsPagedListQueryRequest(filters, skip, take, order));

    /// <summary>
    /// Get product
    /// </summary>
    /// <returns>Product</returns>
    [HttpGet("{id}", Name = "GetProduct")]
    //[HasPermissions(nameof(HacommsPermissions.ViewProduct))]
    [ProducesResponseType(200, Type = typeof(ProductModel))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetByIdAsync(int id) => await Send(new GetProductByIdQueryRequest(id));

    /// <summary>
    /// Get all products
    /// </summary>
    /// <returns>All products</returns>
    [HttpGet("all", Name = "GetProductsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewProductList))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<ProductModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetProductsAsync() =>
        await Send(new GetProductsListQueryRequest());

    /// <summary>
    /// Get all available product codes
    /// </summary>
    [HttpGet("product-codes", Name = "GetAllProductCodes")]
    //[HasPermissions(nameof(HacommsPermissions.ViewProductCode))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<string>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllProductCodesAsync() =>
        await Send(new GetProductCodeListQueryRequest());

    /// <summary>
    /// Create product
    /// </summary>
    /// <returns>Product</returns>
    [HttpPost(Name = "CreateProduct")]
    //[HasPermissions(nameof(HacommsPermissions.CreateProduct))]
    [ProducesResponseType(200, Type = typeof(CreateProductCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateProductAsync([FromBody] CreateProductCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update product
    /// </summary>
    /// <returns>Product</returns>
    [HttpPut(Name = "UpdateProduct")]
    //[HasPermissions(nameof(HacommsPermissions.EditProduct))]
    [ProducesResponseType(200, Type = typeof(UpdateProductCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateProductAsync([FromBody] UpdateProductCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Delete product
    /// </summary>
    /// <returns>Deleted product reference</returns>
    [HttpDelete("{id}", Name = "DeleteProduct")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteProduct))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteProductAsync(int id) => await Send(new DeleteProductCommandRequest(id));
}
