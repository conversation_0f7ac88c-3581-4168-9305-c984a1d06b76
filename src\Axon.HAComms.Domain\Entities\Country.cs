﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class Country : BaseEntity
{
    /// <summary>
    /// Name
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Region
    /// </summary>
    public string? Region { get; set; }

    public int? ExternalId { get; set; }

    public ICollection<Communication>? Communications { get; set; }
}
