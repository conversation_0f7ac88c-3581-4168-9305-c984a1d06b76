﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Create;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Delete;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Application.Queries.RoutesOfAdministration.ListQuery;
using Axon.HAComms.Application.Queries.RoutesOfAdministration.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/RoutesOfAdministration")]
public class RouteOfAdministrationController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all routes of administration
    /// </summary>
    /// <returns>All routes of administration</returns>
    [HttpGet("all", Name = "GetRouteOfAdministrationList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewRouteOfAdministrationList))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<RouteOfAdministrationModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetRouteOfAdministrationListAsync()
        => await Send(new GetRouteOfAdministrationListQueryRequest());

    /// <summary>
    /// Get routes of administration paged list
    /// </summary>
    /// <returns>Routes of administration paged list</returns>
    [HttpGet(Name = "GetPagedRoutesOfAdministrationList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewRouteOfAdministration))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<RouteOfAdministrationPagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
        => await Send(new GetRoutesOfAdministrationPagedListQueryRequest(filters, skip, take, order));

    /// <summary>
    /// Create route of administration
    /// </summary>
    /// <returns>Route Of Administration</returns>
    [HttpPost(Name = "CreateRouteOfAdministration")]
    //[HasPermissions(nameof(HacommsPermissions.CreateRouteOfAdministration))]
    [ProducesResponseType(200, Type = typeof(CreateRouteOfAdministrationCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateRouteOfAdministrationAsync([FromBody] CreateRouteOfAdministrationCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update route of administration
    /// </summary>
    /// <returns>Updated route of administration reference</returns>
    [HttpPut(Name = "UpdateRouteOfAdministration")]
    //[HasPermissions(nameof(HacommsPermissions.EditRouteOfAdministration))]
    [ProducesResponseType(200, Type = typeof(UpdateRouteOfAdministrationCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateRouteOfAdministrationAsync([FromBody] UpdateRouteOfAdministrationCommandRequest command) => await Send(command);


    /// <summary>
    /// Delete route of administration
    /// </summary>
    /// <returns>Deleted route of administration reference</returns>
    [HttpDelete("{id}", Name = "DeleteRouteOfAdministration")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteRouteOfAdministration))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteRouteOfAdministrationAsync(int id) => await Send(new DeleteRouteOfAdministrationCommandRequest(id));
}
