﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <Nullable>enable</Nullable>
	  <ImplicitUsings>enable</ImplicitUsings>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

	<ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="MassTransit" Version="8.3.4" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
		<PackageReference Include="NetArchTest.Rules" Version="1.3.2" />
		<PackageReference Include="Shouldly" Version="4.2.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.7">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="3.1.20" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Axon.HAComms.Api\Axon.HAComms.Api.csproj" />
    <ProjectReference Include="..\..\src\Axon.HAComms.Domain\Axon.HAComms.Domain.csproj" />
    <ProjectReference Include="..\..\src\Axon.HAComms.Infrastructure\Axon.HAComms.Infrastructure.csproj" />
    <ProjectReference Include="..\Axon.HAComms.Tests.Common\Axon.HAComms.Tests.Common.csproj" />

  </ItemGroup>

</Project>
