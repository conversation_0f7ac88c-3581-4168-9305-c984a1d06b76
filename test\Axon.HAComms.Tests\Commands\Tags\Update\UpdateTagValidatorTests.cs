﻿using Axon.HAComms.Application.Commands.Tags.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Update;

public class UpdateTagValidatorTests
{
    private readonly UpdateTagCommandValidator sut;

    public UpdateTagValidatorTests()
    {
        var tagsRepository = Substitute.For<ITagRepository>();
        sut = new UpdateTagCommandValidator(tagsRepository);
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(0, Fake.Tag.Name, Fake.Tag.Description));
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_IdIsNotEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, Fake.Tag.Name, Fake.Tag.Description));
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void Validate_NameIsEmpty_ThrowsException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, string.Empty, Fake.Tag.Description));
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionIsEmpty_DoesNotThrowException()
    {
        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, Fake.Tag.Name, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_NameExceedsMaxLength_ThrowsException()
    {
        var longName = Fake.GetRandomString(201);
        var tagDescription = Fake.Tag.Description;

        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, longName, tagDescription));
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot exceed 200 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NameDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validName = Fake.GetRandomString(100);
        var tagDescription = Fake.Tag.Description;

        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, validName, tagDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_DescriptionExceedsMaxLength_ThrowsException()
    {
        var longDescription = Fake.GetRandomString(501);
        var tagName = Fake.Tag.Name;

        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, tagName, longDescription));
        result.ShouldHaveValidationErrorFor(x => x.Description);
        Assert.Contains("Description cannot exceed 500 characters", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DescriptionDoesNotExceedMaxLength_DoesNotThrowException()
    {
        var validDescription = Fake.GetRandomString(500);
        var tagName = Fake.Tag.Name;

        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, tagName, validDescription));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void Validate_DescriptionIsEmptyString_DoesNotThrowException()
    {
        var tagName = Fake.Tag.Name;

        var result = sut.TestValidate(new UpdateTagCommandRequest(Fake.Tag.Id, tagName, string.Empty));
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }
}
