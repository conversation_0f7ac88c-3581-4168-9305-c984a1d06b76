﻿using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.RoutesOfAdministration
{
    public class RouteOfAdministrationModelBuilder: IBuilder<RouteOfAdministrationModel>
    {
        private int id = Fake.RouteOfAdministration.Id;
        private string name = Fake.RouteOfAdministration.Name;

        public static RouteOfAdministrationModelBuilder Default() => new();

        public RouteOfAdministrationModelBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public RouteOfAdministrationModelBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public RouteOfAdministrationModel Build()
        {
            return new(name)
            {
                Id = id,
            };
        }
    }
}
