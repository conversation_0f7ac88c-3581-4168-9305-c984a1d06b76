﻿using Axon.HAComms.Application.Models.Tags;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Tags;

public class TagModelBuilder : IBuilder<TagModel>
{
    private int id = Fake.Tag.Id;
    private string name = Fake.Tag.Name;

    public static TagModelBuilder Default() => new();

    public TagModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public TagModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public TagModel Build()
    {
        return new(id, name)
        {
            Id = id,
            Name = name
        };
    }
}
