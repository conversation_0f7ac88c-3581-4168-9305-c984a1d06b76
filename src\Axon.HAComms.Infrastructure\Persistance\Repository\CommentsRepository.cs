﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class CommentsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<CommentsRepository> logger)
    : SqlServerRepository<Comment>(context, tenant, logger), ICommentsRepository
{
    public async Task<Comment> GetItemWithAllIncludesAsync(int id)
    {
#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        var entity = await context.Set<Comment>()
            .Include(comment => comment.Communication)
            .Include(comment => comment.DrugSubstances)
            .Include(comment => comment.Tags)
            .Include(cpe => cpe.CommentProductExtensions)
                .ThenInclude(pe => pe.ProductExtension)
                .ThenInclude(df => df!.DosageForm)
            .Include(cpe => cpe.CommentProductExtensions)
                 .ThenInclude(pe => pe.CommentProductExtensionRoutesOfAdministrations)
                 .ThenInclude(routes => routes.RouteOfAdministration)
            .Include(pe => pe.CommentProductExtensions)
                .ThenInclude(pe => pe.ProductExtension)
                .ThenInclude(p => p!.Product)
                    .ThenInclude(pt => pt.ProductTypes)
            .SingleOrDefaultAsync(comment => comment.Id == id);
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.

        return entity ?? throw new EntityNotFoundException(nameof(Comment), id);
    }
    
    public async Task<Comment> GetItemByIdAsync(int id)
    {
        var entity = await context.Set<Comment>()
                            .SingleOrDefaultAsync(comment => comment.Id == id);
        return entity ?? throw new EntityNotFoundException(nameof(Comment), id);
    }

    public async Task<IEnumerable<Comment>> GetItemsWithProductExtensionIdsAsync(int[] productExtensionIds)
    {
        return await context.Set<Comment>().Include(c => c.CommentProductExtensions)
           .Where(x => x.CommentProductExtensions != null && x.CommentProductExtensions.Any(cpe => productExtensionIds.Contains(cpe.ProductExtensionId)))
           .ToListAsync();
    }

    public async Task<bool> ExistsAsync(Expression<Func<Comment, bool>> filter)
    {
        return await context.Set<Comment>()
                            .Include(x => x.CommentTags)
                            .Include(x => x.CommentProductExtensions)
                    .AnyAsync(filter);
    }

    public IQueryable<Comment> GetFilteredComments(Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>? include = null, Expression<Func<Comment, bool>>? filter = null)
    {
        var query = context.Set<Comment>().AsQueryable();
        if (include != null)
        {
            query = include(query);
        }

        if (filter != null)
        {
            query = query.Where(filter);
        }

        return query;
    }

    public void DeleteItems(Comment[] comments)
    {
        foreach (var comment in comments)
        {
            comment.Delete();
            UpdateItem(comment);
        }
    }
}
