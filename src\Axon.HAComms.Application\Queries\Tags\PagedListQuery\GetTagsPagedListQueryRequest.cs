﻿using Axon.HAComms.Application.Models.Tags;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Tags.PagedListQuery;

public class GetTagsPagedListQueryRequest(
    string[]? filters,
    int skip,
    int take,
    string? order)
    : IRequest<ApiPagedListResult<TagPagedListModel>>
{
    public string[]? Filters { get; } = filters;
    public int Skip { get; } = skip;
    public int Take { get; } = take;
    public string? Order { get; } = order;
}
