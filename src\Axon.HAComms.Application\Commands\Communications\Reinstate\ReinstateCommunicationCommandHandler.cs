﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Communications.Reinstate;

internal class ReinstateCommunicationCommandHandler(
    ICommunicationsRepository communicationRepo,
    IUserProvider userProvider,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    ILogger<ReinstateCommunicationCommandHandler> logger) : IRequestHandler<ReinstateCommunicationCommandRequest, Result>
{
    public async Task<Result> Handle(ReinstateCommunicationCommandRequest request, CancellationToken cancellationToken)
    {
        var correlationId = correlationIdProvider.Provide();
        var communication = await communicationRepo.GetCompletedCommunicationByIdAsync(request.Id, cancellationToken);

        await auditService.LogAsync(
           correlationId, clientDetailsProvider.Provide(),
           AuditEventType.COMMUNICATION_REINSTATED, AuditEventCategory.COMMUNICATIONS, AuditEventDescription.COMMUNICATION_REINSTATED, communication,
           async () =>
           {
               communication.Reinstate();
               communicationRepo.UpdateItem(communication);
               await communicationRepo.SaveChangesAsync(userProvider);
               logger.LogInformation("Communication {Subject} reinstated successfully.", communication.Subject);
           });

        return Result.Success();
    }
}
