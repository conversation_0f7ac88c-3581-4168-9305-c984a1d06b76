using Asp.Versioning.ApiExplorer;
using Autofac;
using Axon.Core.Shared.Extensions;
using Axon.HAComms.Api.Extensions;
using Axon.HAComms.Api.Infrastructure;
using Axon.HAComms.Api.Infrastructure.IoC;
using Axon.HAComms.Api.Infrastructure.OData;
using Axon.HAComms.Api.Infrastructure.OpenApi;
using Axon.HAComms.Api.JsonHandling;
using Axon.HAComms.Api.Middleware;
using Axon.HAComms.Application.AzureSearch;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Helpers;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Application.Models.Cors;
using Axon.HAComms.Application.Services;
using Axon.HAComms.Infrastructure.Extensions;
using Axon.HAComms.Infrastructure.HostedServices;
using Axon.HAComms.Infrastructure.IoC;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using FluentValidation;
using FluentValidation.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using NLog;
using NLog.Web;
using Phlex.Core.Caching.Memory;
using Phlex.Core.Multitenancy.Extensions;
using Phlex.Core.Multitenancy.Providers;
using Phlex.Core.Multitenancy.Settings;
using Phlex.Core.Multitenancy.Strategies;
using SendGrid;
using SendGrid.Extensions.DependencyInjection;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.IO.Compression;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Services.Configure<HostOptions>(hostOptions =>
    {
        hostOptions.BackgroundServiceExceptionBehavior = BackgroundServiceExceptionBehavior.Ignore;
    });

    // NLog: Setup NLog for Dependency injection
    builder.Logging.ClearProviders();
    builder.Logging.SetMinimumLevel(LogLevel.Trace);
    builder.Host.UseNLog();

    //Required for logs in context
    var nrAgent = new Lazy<NewRelic.Api.Agent.IAgent>(NewRelic.Api.Agent.NewRelic.GetAgent());
    foreach (var (key, value) in nrAgent.Value.GetLinkingMetadata())
    {
        GlobalDiagnosticsContext.Set(key, value);
    }

    builder.Services.AddOptions();
    builder.Services.AddOData();
    builder.Services.AddInfrastructure(builder.Configuration);
    builder.Services.AddApplication(builder.Environment);
    builder.Services.AddValidatorsFromAssemblyContaining<Program>();
    builder.Services.AddFluentValidationAutoValidation();
    builder.Services.AddFluentValidationClientsideAdapters();
    builder.Services.AddControllers().AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new DateTimeConverter());
        options.JsonSerializerOptions.Converters.Add(new IntConverter());
        options.JsonSerializerOptions.Converters.Add(new BooleanConverter());
    });
    builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));

    var settingsSection = builder.Configuration.GetSection("AzureSearch");
    builder.Services.Configure<SearchSettings>(settingsSection);
    var azureSearchSettings = settingsSection.Get<SearchSettings>();
    logger.Info($"SearchSettings Service Name: {azureSearchSettings.ServiceName} enabled {azureSearchSettings.IsEnabled}");
    builder.Services.AddAzureAiSearch(azureSearchSettings);

    builder.Host
        .UseMultiTenantServiceProviderFactory(MultitenantStartup.ConfigureServices, MultitenantStartup.ConfigureContainer)
        .ConfigureAppConfiguration((_, config) =>
        {
            if (builder.Configuration["KeyVaultName"] != null)
            {
                var builtConfig = config.Build();
                var secretClient = new SecretClient(
                    new Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
                    new DefaultAzureCredential());
                config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
            }
        });

    // Multitenancy setup
    builder.Services.Configure<TenantSettings>(tenantOptions => builder.Configuration.GetSection("TenantSettings").Bind(tenantOptions));
    if (builder.Environment.IsDevelopment() || builder.Environment.IsIntegrationTest())
    {
        builder.Services.AddSingleton<ITenantProvider, ConfigurationTenantProvider>();
    }
    else
    {
        builder.Services.AddOrganisationTenantProvider(builder.Configuration);
    }
    builder.Services.AddMultitenancy<BasePathTenantIdentificationStrategy>(builder.Configuration);

    builder.Services.AddCorrelation();
    builder.Services.AddUser();

    builder.Services.AddHealthChecks(builder.Configuration);


    builder.Services.AddSendGrid(sendGridClientOptions =>
    {
        sendGridClientOptions.ApiKey = builder.Configuration.GetConnectionString("SendGrid");
    });

    builder.Services.AddTransient<IEmailService>(provider =>
    {
        var sendGridClient = provider.GetRequiredService<ISendGridClient>();
        return new EmailService(builder.Configuration, sendGridClient);
    });

    // Add API Versioning to the Project
    builder.Services.AddApiVersioning(VersionConfiguration.VersioningOptions).AddApiExplorer(VersionConfiguration.ExplorerOptions);
    // Add Open API generation
    builder.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>>(s =>
        new ConfigureSwaggerOptions(s.GetService<IApiVersionDescriptionProvider>(), AppConstants.API_TITLE, AppConstants.API_DESCRIPTION, string.Empty));
    builder.Services.AddSwaggerGen(
        c =>
        {
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey

            });
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });

    builder.Services.AddGenericMemoryCaches();
    builder.Services.AddAxonAuthentication(builder.Configuration, builder.Environment);

    // To compress the response data before sending back to the client
    // This reduces the amount of data transferred and helps faster data transfer
    builder.Services.Configure<GzipCompressionProviderOptions>(gzipOptions =>
        gzipOptions.Level = CompressionLevel.Fastest);
    builder.Services.AddResponseCompression();

    builder.Host.ConfigureContainer<ContainerBuilder>(b => b.RegisterModule(new HaCommsModule(builder.Configuration)));

    builder.Services.Configure<DbSettings>(builder.Configuration.GetSection("ConnectionStrings"));
    builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

    ConfigureCleanupService(builder);

    var app = builder.Build();
    var swaggerBasePath = builder.Configuration["Swagger:BasePath"];
    app.UseSwagger(c =>
    {
        if (!string.IsNullOrEmpty(swaggerBasePath))
        {
            c.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
            {
                swaggerDoc.Servers = new List<OpenApiServer> { new OpenApiServer { Url = $"https://{httpReq.Host.Value}{swaggerBasePath}/" } };
            });
        }
    });

    if (builder.Environment.IsDevelopment() || builder.Environment.IsIntegrationTest())
    {
        app.UseDeveloperExceptionPage();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint($"{swaggerBasePath}/swagger/v{VersionConfiguration.ApiVersion.MajorVersion}/swagger.json", $"{AppConstants.API_TITLE} v{VersionConfiguration.ApiVersion.MajorVersion}");
        });
    }
    else
    {
        app.UseHsts();
    }

    app.Use(async (context, next) =>
    {
        if (!context.Response.Headers.ContainsKey("X-Frame-Options"))
        {
            context.Response.Headers.Append("X-Frame-Options", "DENY");
        }

        if (!context.Response.Headers.ContainsKey("Content-Security-Policy"))
        {
            context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; "
                                                                       + "connect-src 'self' " + (builder.Environment.IsDevelopment() ? "wss: ws: http:" : "") + "; "
                                                                       + "frame-ancestors 'none'; "
                                                                       + "object-src 'none';");
        }

        await next();
    });

    // Create Azure cognitive search index and indexer
    if (azureSearchSettings.IsEnabled && !builder.Environment.IsIntegrationTest())
    {
        var azureServiceHelper = app.Services.GetService<IAzureSearchHelper>();
        await azureServiceHelper.DeleteIndexer();
        await azureServiceHelper.DeleteIndex();
        await azureServiceHelper.DeleteDataSource();
        await azureServiceHelper.CreateDataSource();
        await azureServiceHelper.CreateIndexAsync<IndexedCommentModel>();
        await azureServiceHelper.CreateIndexer();
    }

    app.UseHttpsRedirection();
    app.UseMiddleware<ExceptionMiddleware>();
    app.UseRouting();
    app.UseCors(c =>
    {
        DefineCorsPolicy(c, builder);
    });
    app.UseResponseCompression();
    app.UseAuthentication();
    app.UseAuthorization();

#pragma warning disable ASP0014
    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllers();
        HealthCheckExtensions.MapHealthChecks(endpoints);
    });
#pragma warning restore ASP0014

    await app.RunAsync();
}
catch (Exception ex)
{
    //NLog: catch setup errors
    logger.Error(ex, "Stopped program because of exception");
}
finally
{
    // Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
    LogManager.Shutdown();
}

return;

static void DefineCorsPolicy(CorsPolicyBuilder c, WebApplicationBuilder builder)
{
    List<CorsOrigin> origins = new();
    builder.Configuration.GetSection("cors:origins").Bind(origins);
    foreach (var o in origins)
    {
        c.WithOrigins(o.Uri);
    }

    c.AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials();
}

static void ConfigureCleanupService(WebApplicationBuilder builder)
{
    if (!builder.Environment.IsDevelopment())
    {
        builder.Services.AddHostedService(serviceProvider =>
            new SoftDeleteCleanupService(
                serviceProvider,
                TimeSpan.FromDays(30),
                builder.Configuration["SoftDeleteHostedService:receivers"] ?? string.Empty,
                builder.Environment.EnvironmentName
            )
        );
    }
}

public partial class Program
{
    protected Program() { }
}
