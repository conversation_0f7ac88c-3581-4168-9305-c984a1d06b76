﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;
using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Models.ProductType;
using Axon.HAComms.Application.Queries.ProductTypes.ListQuery;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/ProductTypes")]
public class ProductTypesController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all product types
    /// </summary>
    /// <returns>All product types</returns>
    [HttpGet(Name = "GetProductTypesList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewProductType))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<ProductTypeModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllAsync() => await Send(new GetProductTypesListQueryRequest());
}
