﻿IF EXISTS(SELECT 1 FROM sys.views WHERE NAME='CommunicationsView' and TYPE='v')
DROP VIEW CommunicationsView;
GO
CREATE VIEW [dbo].[CommunicationsView] AS
SELECT 
    CQ.Id, 
    CQ.Subject,
    STRING_AGG(CQ.ProductName, ', ') AS ProductNames,
    CQ.CountryId,
    CQ.CountryName,
    CQ.DateOfCommunication, 
    CQ.CreatedDate, 
    CQ.CreatedBy,
    CQ.IsCompleted,
    CQ.LastUpdatedBy,
    CQ.LastUpdatedDate, 
    CQ.IsDeleted,
    CQ.Tenant
    FROM
    	(SELECT
    		DISTINCT 
    		c.Id, 
    		c.Subject,
    		dp.Name AS ProductName,
    		con.Id as CountryId,
    		con.Name AS CountryName,
    		c.DateOfCommunication, 
    		c.<PERSON>ate, 
    		c.CreatedBy,
    		c.IsCompleted,
    		c.LastUpdatedBy,
    		c.LastUpdated<PERSON>ate,
    		c.<PERSON>,
    		c.Tenant
    	FROM Communications c
    	JOIN Countries con on con.Id = c.CountryId
    	JOIN Comments com ON com.CommunicationId = c.Id
    	LEFT OUTER JOIN dbo.CommentProductExtension AS cpe ON cpe.CommentId = com.Id 
        LEFT OUTER JOIN dbo.ProductExtensions AS pe ON pe.Id = cpe.ProductExtensionId
        LEFT JOIN DrugProducts dp ON dp.Id = pe.ProductId) as CQ
GROUP BY CQ.Id, CQ.Subject, CQ.CountryId, CQ.CountryName, CQ.DateOfCommunication, CQ.CreatedDate, CQ.CreatedBy, CQ.IsCompleted, CQ.LastUpdatedBy, CQ.LastUpdatedDate, CQ.IsDeleted, CQ.Tenant
