﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.DrugSubstances
{
	public class DrugSubstancesBuilder : IBuilder<DrugSubstance>
	{
		private int id;
		private string name;
		private string code;
		private readonly string description;
		private DateTime createdDate;
		private DateTime lastUpdatedDate;
		private string lastUpdatedBy;

		public DrugSubstancesBuilder()
		{
			this.id = Fake.DrugSubstance.Id;
			this.name = Fake.DrugSubstance.Name;
			this.description = Fake.DrugSubstance.Description;
			this.code = Fake.DrugSubstance.Code;
			this.createdDate = DateTime.Now;
			this.lastUpdatedDate = DateTime.Now;
			this.lastUpdatedBy = Fake.DrugSubstance.LastUpdatedBy;
		}
		public DrugSubstance Build()
		{
			return new(this.id)
			{
				Name = name,
				Code = code,
				Description = description,
				CreatedDate = createdDate,
				LastUpdatedDate = lastUpdatedDate,
				LastUpdatedBy = lastUpdatedBy
			};
		}

        public DrugSubstancesBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public DrugSubstancesBuilder WithName(string name)
		{
			this.name = name;
			return this;
		}

		public DrugSubstancesBuilder WithCode(string code)
		{
			this.code = code;
			return this;
		}

		public DrugSubstancesBuilder WithCreatedDate(DateTime createdDate)
		{
			this.createdDate = createdDate;
			return this;
		}

		public DrugSubstancesBuilder WithLastUpdatedDate(DateTime lastUpdatedDate)
		{
			this.lastUpdatedDate = lastUpdatedDate;
			return this;
		}

		public DrugSubstancesBuilder WithLastUpdatedBy(string lastUpdatedBy)
		{
			this.lastUpdatedBy = lastUpdatedBy;
			return this;
		}

	}
}
