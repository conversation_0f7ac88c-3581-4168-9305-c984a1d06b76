﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.Tags.Create;
using Axon.HAComms.Application.Commands.Tags.Delete;
using Axon.HAComms.Application.Commands.Tags.Update;
using Axon.HAComms.Application.Models.Tags;
using Axon.HAComms.Application.Queries.Tags.ListQuery;
using Axon.HAComms.Application.Queries.Tags.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Tags")]
public class TagController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all tags
    /// </summary>
    /// <returns>All tags</returns>
    [HttpGet("all", Name = "GetTagsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewTagList))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<TagModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetTagsListAsync()
        => await Send(new GetTagListQueryRequest());


    /// <summary>
    /// Get tags paged list
    /// </summary>
    /// <returns>Tags paged list</returns>
    [HttpGet(Name = "GetPagedTagsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewTag))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<TagPagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
        => await Send(new GetTagsPagedListQueryRequest(filters, skip, take, order));

    /// <summary>
    /// Create tag
    /// </summary>
    /// <returns>Tag</returns>
    [HttpPost(Name = "CreateTag")]
    //[HasPermissions(nameof(HacommsPermissions.CreateTag))]
    [ProducesResponseType(200, Type = typeof(CreateTagCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateTagAsync([FromBody] CreateTagCommandRequest command)
        => await Send(command);

    /// <summary>
    /// Update tag
    /// </summary>
    /// <returns>Updated tag</returns>
    [HttpPut(Name = "UpdateTag")]
    //[HasPermissions(nameof(HacommsPermissions.EditTag))]
    [ProducesResponseType(200, Type = typeof(UpdateTagCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateTagAsync([FromBody] UpdateTagCommandRequest command) => await Send(command);


    /// <summary>
    /// Delete tag
    /// </summary>
    /// <returns>Deleted tag</returns>
    [HttpDelete("{id}", Name = "DeleteTag")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteTag))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteTagAsync(int id) => await Send(new DeleteTagCommandRequest(id));
}
