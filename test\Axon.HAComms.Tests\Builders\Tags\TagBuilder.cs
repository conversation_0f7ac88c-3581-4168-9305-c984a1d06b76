﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Tags
{
    public class TagBuilder : IBuilder<Tag>
    {
        private int id = Fake.Tag.Id;
        private string name = Fake.Tag.Name;
        private string description = Fake.Tag.Description;
        private DateTime createdDate = DateTime.Now;
        private DateTime lastUpdatedDate = DateTime.Now;
        private string lastUpdatedBy = Fake.Tag.LastUpdatedBy;
        private string createdBy = Fake.Tag.CreatedBy;

        public TagBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public TagBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public TagBuilder WithDescription(string description)
        {
            this.description = description;
            return this;
        }

        public TagBuilder WithCreatedDate(DateTime createdDate)
        {
            this.createdDate = createdDate;
            return this;
        }

        public TagBuilder WithCreatedBy(string createdBy)
        {
            this.createdBy = createdBy;
            return this;
        }

        public TagBuilder WithLastUpdatedDate(DateTime lastUpdatedDate)
        {
            this.lastUpdatedDate = lastUpdatedDate;
            return this;
        }

        public TagBuilder WithLastUpdatedBy(string lastUpdatedBy)
        {
            this.lastUpdatedBy = lastUpdatedBy;
            return this;
        }

        public Tag Build()
        {
            return new(id, name)
            {
                Description = description,
                CreatedDate = createdDate,
                CreatedBy = createdBy,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy
            };
        }
    }
}
