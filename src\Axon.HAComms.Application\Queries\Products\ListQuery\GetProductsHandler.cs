﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Products;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Products.ListQuery;

internal class GetProductsHandler(IProductsRepository repo, IMapper mapper) : IRequestHandler<GetProductsListQueryRequest, ApiListResult<ProductModel>>
{
    public async Task<ApiListResult<ProductModel>> Handle(GetProductsListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync(p => p.Name);
        return new ApiListResult<ProductModel>(entities.Select(mapper.Map<ProductModel>));
    }
}
