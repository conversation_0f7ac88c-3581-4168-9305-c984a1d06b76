﻿using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.DrugSubstances
{
	public class DrugSubstancesModelBuilder : IBuilder<DrugSubstanceModel>
	{
		private int id;
		private string name;
		private string code;
		private readonly string description;

		public DrugSubstancesModelBuilder()
		{
			this.id = Fake.DrugSubstance.Id;
			this.name = Fake.DrugSubstance.Name;
			this.description = Fake.DrugSubstance.Description;
			this.code = Fake.DrugSubstance.Code;
		}
		public DrugSubstanceModel Build()
		{
			return new(id, code, name, description);
		}

        public DrugSubstancesModelBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public DrugSubstancesModelBuilder WithName(string name)
		{
			this.name = name;
			return this;
		}

		public DrugSubstancesModelBuilder WithCode(string code)
		{
			this.code = code;
			return this;
		}
	}
}
