﻿using Axon.HAComms.Application.Models.Application;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Application.Models.Products;

namespace Axon.HAComms.Application.Models.Search;

public class SearchDetailsModel
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public DateTime DateOfCommunication { get; set; }
    public string SubmissionType { get; set; } = string.Empty;
    public List<ApplicationModel>? Applications { get; set; }
    public bool IsCompleted { get; set; }
    public string? LastUpdatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string? CreatedBy { get; set; }
    public CommentDtoModel? Comment { get; set; }
    public List<ProductDtoModel>? AllProducts { get; set; }
    public bool ContainsGeneralGuidanceComments { get; set; }
}
