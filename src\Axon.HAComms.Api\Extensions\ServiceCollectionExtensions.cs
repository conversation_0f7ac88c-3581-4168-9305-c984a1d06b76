﻿using Axon.HAComms.Application.AzureSearch;
using Axon.HAComms.Application.Commands.Comments.Search;
using Axon.HAComms.Application.Commands.Communications.Complete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Helpers;
using Azure.Identity;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using FluentValidation;

namespace Axon.HAComms.Api.Extensions;

public static class ServicesCollectionExtension
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IWebHostEnvironment env)
    {
        if (env.IsIntegrationTest())
        {
            services.AddScoped<IAzureSearchHelper, AzureSearchIntTestsHelper>();
        }
        else
        {
            services.AddScoped<IAzureSearchHelper, AzureSearchHelper>();
        }

        return services
            .AddAutoMapper(typeof(ApplicationsMappingProfile).Assembly)
            .AddValidatorsFromAssembly(typeof(CompleteCommunicationCommandValidator).Assembly)
            .AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(typeof(SearchCommentCommandHandler).Assembly));
    }

    public static IServiceCollection AddAzureAiSearch(this IServiceCollection services, SearchSettings azureSearchSettings)
    {
        var searchEndpointUri = new Uri($"https://{azureSearchSettings.ServiceName}.search.windows.net/");
        return services.AddScoped(_ =>
            {
                var searchClient = new SearchClient(searchEndpointUri, azureSearchSettings.IndexName, new DefaultAzureCredential());
                return searchClient;
            })
            .AddScoped(_ =>
            {
                var searchIndexClient = new SearchIndexClient(searchEndpointUri, new DefaultAzureCredential());
                return searchIndexClient;
            })
            .AddScoped(_ =>
            {
                var searchIndexerClient = new SearchIndexerClient(searchEndpointUri, new DefaultAzureCredential());
                return searchIndexerClient;
            });
    }
}
