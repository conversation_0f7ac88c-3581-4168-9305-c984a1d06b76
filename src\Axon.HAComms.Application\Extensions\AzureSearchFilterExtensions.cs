﻿namespace Axon.HAComms.Application.Extensions
{
    public static class AzureSearchFilterExtensions
    {
        public static string AddSearchInFilter(this string filter, List<int> collection, string filterField)
        {
            if (collection.Count != 0 && collection.TrueForAll(x => x > 0))
            {
                var condition = $"search.in({filterField}, '{string.Join(",", collection)}')";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }

        public static string AddSearchInFilter(this string filter, List<string> collection, string filterField)
        {
            if (collection.Count != 0 && collection.TrueForAll(x => x is not "-1"))
            {
                var condition = $"search.in({filterField}, '{string.Join(",", collection)}', ',')";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }

        public static string AddAnySearchInFilter(this string filter, List<string> collection, string filterField)
        {
            if (collection.Count != 0 && collection.TrueForAll(x => x is not "-1"))
            {
                var condition = $"{filterField}/any({filterField.Remove(filterField.Length - 1, 1)}: search.in({filterField.Remove(filterField.Length - 1, 1)}, '{string.Join(",", collection.Select(x => x.ToLower()))}', ','))";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }


        public static string AddAnySearchInFilter(this string filter, List<int> collection, string filterField)
        {
            if (collection.Count != 0)
            {
                var condition = $"{filterField}/any({filterField.Remove(filterField.Length - 1, 1)}: search.in({filterField.Remove(filterField.Length - 1, 1)}, '{string.Join(",", collection)}', ','))";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }

        public static string AddGreaterThanFilter(this string filter, DateTime? inputDate, string filterField)
        {
            if (inputDate is not null)
            {
                var condition = $"{filterField} ge {inputDate:O}Z";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }

        public static string AddLessThanFilter(this string filter, DateTime? inputDate, string filterField)
        {
            if (inputDate is not null)
            {
                var condition = $"{filterField} le {inputDate:O}Z";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }

        public static string AddEqualFilter(this string filter, object? inputValue, string filterField)
        {
            if (inputValue is not null)
            {
                var condition = inputValue is string ? $"{filterField} eq '{inputValue.ToString()?.ToLower()}'" : $"{filterField} eq {inputValue.ToString()?.ToLower()}";
                return $"{filter} {(string.IsNullOrEmpty(filter) ? $"{condition}" : $"and {condition}")}".Trim();
            }

            return filter;
        }
    }
}
