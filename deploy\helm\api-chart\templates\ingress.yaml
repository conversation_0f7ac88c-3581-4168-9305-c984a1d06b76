{{- $fullName := include "axon-hacomms-api.fullname" . -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "axon-hacomms-api.fullname" . }}
  labels:
    {{- include "axon-hacomms-api.labels" . | nindent 4 }}
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    checkov.io/skip1: CKV_K8S_21=Default namespace should not be used
spec:
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .tlsSecretName }}
    {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path | default "/axon-hacomms-api/(.*)" }}    
            pathType: {{ .pathType | default "ImplementationSpecific" }}
            backend:
              service:
                name: {{ .svcName | default $fullName }}
                port:
                   {{- if .portNumber }}
                  number: {{ .portNumber }}
                  {{- else }}
                  name: {{ .portName | default "http"}}
                  {{- end }}
          {{- end }}
    {{- end }}