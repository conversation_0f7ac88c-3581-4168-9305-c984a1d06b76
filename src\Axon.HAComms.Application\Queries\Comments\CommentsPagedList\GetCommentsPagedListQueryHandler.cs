﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.Comments;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Comments.CommentsPagedList;

internal class GetCommentsPagedListQueryHandler(ICommentsRepository commentsRepository, IMapper mapper)
    : IRequestHandler<GetCommentsPagedListQueryRequest, ApiPagedListResult<CommentDtoModel>>
{
    /// <summary>
    /// If productId is null - general comments for the specified communicationId will be returned
    /// If productId is not null - all comments for the specified communicationId and productId will be returned
    /// If excludedCommentId is null - all comments for the specified communicationId and productId will be returned
    /// If excludedCommentId is not null - the comment with excludedCommentId will not be excluded from the results
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<ApiPagedListResult<CommentDtoModel>> Handle(GetCommentsPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        // We exclude the comment because of the fixed number of the results we retrieve.
        // For example, if there are 10 results in the first page and we exclude 1 on the FE, then 9 results will be shown which is not correct.
#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        var query = commentsRepository.GetFilteredComments(x => x.Include(cpe => cpe.CommentProductExtensions)
                                            .ThenInclude(pe => pe.ProductExtension)
                                            .ThenInclude(df => df!.DosageForm)
                                          .Include(pe => pe.CommentProductExtensions)
                                            .ThenInclude(df => df.CommentProductExtensionRoutesOfAdministrations)
                                            .ThenInclude(roa => roa.RouteOfAdministration)
                                          .Include(pe => pe.CommentProductExtensions)
                                            .ThenInclude(pe => pe.ProductExtension)
                                            .ThenInclude(p => p!.Product)
                                                .ThenInclude(pt => pt.ProductTypes)
                                          .Include(ds => ds.DrugSubstances)
                                          .Include(t => t.Tags)
                                          .Include(pe => pe.ProductExtensions)
                                            .ThenInclude(pe => pe.RouteOfAdministrations),
            x => x.CommunicationId == request.CommunicationId
                 && (request.ProductId == null ? x.IsGeneralGuidance : x.CommentProductExtensions != null && x.CommentProductExtensions.First().ProductExtension!.ProductId == request.ProductId)
                 && (request.ExcludedCommentId == null || x.Id != request.ExcludedCommentId)
        );
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        var comments = await query.FilterItems(null, c => c.OrderByDescending(x => x.CreatedDate), request.Skip, request.Take).AsNoTracking().ToListAsync(cancellationToken: cancellationToken);

        var result = mapper.Map<List<CommentDtoModel>>(comments);

        return new ApiPagedListResult<CommentDtoModel>(
            result,
            new ApiPagedListResult<CommentDtoModel>.PagingInfo
            {
                TotalItemCount = await query.CountAsync(cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }
}
