﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities
{
    public class Application : MultiTenantEntity
    {
        public string Number { get; set; } = string.Empty;
        public Communication Communication { get; set; } = null!;
        public int CommunicationId { get; set; }

        public ICollection<Submission> Submissions { get; set; } = new HashSet<Submission>();
    }
}
