﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications
{
    public class ApplicationModelBuilder
    {
        private string number;
        private IList<SubmissionModel> submissions;

        public ApplicationModelBuilder()
        {
            number = Fake.Application.Number;
            submissions = new List<SubmissionModel>();
        }

        public static ApplicationModelBuilder Default() => new();

        public ApplicationModel Build()
        {
            return new()
            {
                Number = number,
                Submissions = submissions != null ? submissions.ToList() : new List<SubmissionModel>(),
            };
        }

        public ApplicationModelBuilder WithNumber(string number)
        {
            this.number = number;
            return this;
        }

        public ApplicationModelBuilder WithSubmissions(params SubmissionModel[] submissions)
        {
            this.submissions = submissions;
            return this;
        }
    }
}
