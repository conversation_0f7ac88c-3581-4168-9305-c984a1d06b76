﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Communications.Reinstate;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Communications;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Reinstate;

public class ReinstateCommunicationCommandHandlerTests
{
    private readonly ReinstateCommunicationCommandHandler handler;
    private readonly ICommunicationsRepository communicationRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public ReinstateCommunicationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var logger = Substitute.For<ILogger<ReinstateCommunicationCommandHandler>>();
        communicationRepo = Substitute.For<ICommunicationsRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();
        handler = new ReinstateCommunicationCommandHandler(communicationRepo, userProvider, auditService, correlationIdProvider, clientDetailsProvider, logger);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var communication = new CommunicationsBuilder().Build();
        communicationRepo.GetCompletedCommunicationByIdAsync(communication.Id, CancellationToken.None).Returns(communication);

        var request = new ReinstateCommunicationCommandRequest(communication.Id);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_REINSTATED, AuditEventCategory.COMMUNICATIONS,
            AuditEventDescription.COMMUNICATION_REINSTATED, Arg.Any<Communication>(), Arg.Any<Func<Task>>());
    }
}
