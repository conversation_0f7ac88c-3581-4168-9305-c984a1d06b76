﻿using Axon.HAComms.Application.Models.DosageForm;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.DosageForms.PagedListQuery;

public class GetDosageFormPagedListQueryRequest(
    string[]? filters,
    int skip,
    int take,
    string? order)
    : IRequest<ApiPagedListResult<DosageFormPagedListModel>>
{
    public string[]? Filters { get; } = filters;
    public int Skip { get; } = skip;
    public int Take { get; } = take;
    public string? Order { get; } = order;
}
