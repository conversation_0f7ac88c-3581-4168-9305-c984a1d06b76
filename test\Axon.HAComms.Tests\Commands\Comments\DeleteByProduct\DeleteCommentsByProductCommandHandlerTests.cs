﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.HAComms.Application.Commands.Comments.DeleteByProduct;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Domain.Constants;
using Microsoft.EntityFrameworkCore.Query;

namespace Axon.HAComms.Tests.Commands.Comments.DeleteByProduct;

public class DeleteCommentsByProductCommandHandlerTests
{
    private readonly DeleteCommentsByProductCommandHandler handler;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteCommentsByProductCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var logger = Substitute.For<ILogger<DeleteCommentsByProductCommandHandler>>();
        commentsRepo = Substitute.For<ICommentsRepository>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        var userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS,
            AuditEventDescription.COMMENT_DELETE, Arg.Any<Comment>(), Arg.Any<Func<Task>>())).Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        handler = new DeleteCommentsByProductCommandHandler(commentsRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);

    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var communicationId = Fake.Communication.Id;
        var comment1 = new Comment()
        {
            CommunicationId = communicationId,
            CommentProductExtensions = new List<CommentProductExtension>()
                        {
                            new CommentProductExtension()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                ProductExtension = new ProductExtension() { Product = new Product() },
                                CommentProductExtensionRoutesOfAdministrations = new List<CommentProductExtensionRoutesOfAdministration>()
                                {
                                 new () {  RouteOfAdministrationId =  Fake.RouteOfAdministration.Id },
                                 new () {  RouteOfAdministrationId =  Fake.RouteOfAdministration.Id }
                                }
                            }
                        },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };

        var comment2 = new Comment()
        {
            CommunicationId = communicationId,
            CommentProductExtensions = new List<CommentProductExtension>()
                        {
                            new CommentProductExtension()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                ProductExtension = new ProductExtension() { Product = new Product() },
                                CommentProductExtensionRoutesOfAdministrations = new List<CommentProductExtensionRoutesOfAdministration>()
                                {
                                 new () {  RouteOfAdministrationId =  Fake.RouteOfAdministration.Id }
                                }
                            }
                        },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };

        var comments = new List<Comment> { comment1, comment2 };

        var request = new DeleteCommentsByProductCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            ProductId = Fake.Product.Id
        };

        commentsRepo.GetFilteredComments(include: Arg.Any<Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>>(), filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(comments.BuildMock());

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        await auditService
          .ReceivedWithAnyArgs(2)
          .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(2)
            .LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS, AuditEventDescription.COMMENT_DELETE,
                Arg.Any<Comment>(), Arg.Any<Func<Task>>());
    }

}
