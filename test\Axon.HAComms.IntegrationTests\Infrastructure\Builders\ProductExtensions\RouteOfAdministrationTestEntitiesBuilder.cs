﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.ProductExtensions;

public static class RouteOfAdministrationTestEntitiesBuilder
{
    public static async Task<RouteOfAdministration[]> Build(HACommsContext dbContext, int numberOfItems = 1)
    {
        var routeOfAdminList = new List<RouteOfAdministration>();
        for (var i = 0; i < numberOfItems; i++)
        {
            routeOfAdminList.Add(CreateRoutesOfAdministrationBuilder.Default().WithName(Fake.RouteOfAdministration.Name).Build());
        }

        await dbContext.RouteOfAdministrations.AddRangeAsync(routeOfAdminList);
        await dbContext.SaveChangesAsync();

        return routeOfAdminList.ToArray();
    }
}
