﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Domain.Constants;
using MediatR;

namespace Axon.HAComms.Application.Queries.Products.IdQuery;

internal class GetProductByIdQueryHandler(IProductsRepository repo,
    ICommentsRepository repoComments,
    IMapper mapper,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IAuditService auditService) : IRequestHandler<GetProductByIdQueryRequest, ProductModel>
{
    public async Task<ProductModel> Handle(GetProductByIdQueryRequest request, CancellationToken cancellationToken)
    {
        var entity = await repo.GetItemAsync(request.Id);

        var returnModel = mapper.Map<ProductModel>(entity);
        var associatedComments = (await repoComments.GetItemsWithProductExtensionIdsAsync(entity.ProductExtensions.Select(x => x.Id).ToArray())).ToArray();

        foreach (var productExtension in returnModel.ProductExtensions)
        {
            productExtension.IsAssociatedToComment = Array.Exists(associatedComments,
                x => x.CommentProductExtensions != null && x.CommentProductExtensions.Select(pe => pe.ProductExtensionId).Contains(productExtension.Id));
        }

        auditService.Log(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DRUG_PRODUCT_VIEWED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_VIEW, returnModel);


        return returnModel;
    }
}
