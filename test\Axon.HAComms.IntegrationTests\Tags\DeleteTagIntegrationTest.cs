﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Tags;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteTagIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly TagApi tagApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteTag_ValidRequest_ReturnsOk()
    {
        //Arrange
        var request = new CreateTagCommandRequest(Fake.Tag.Name, Fake.Tag.Description);

        //Act
        var response = await tagApi.CreateTagAsync(TenantConstants.DEFAULT_TENANT, request);

        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        await tagApi.DeleteTagAsync(response.Id, TenantConstants.DEFAULT_TENANT);
        var responseObj = await tagApi.GetTagsListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        Assert.DoesNotContain(responseObj.Data, x => x.Id == response.Id);
    }

    [Fact]
    public async Task DeleteTag_WithAssociatedComments_ThrowsAssociationException()
    {
        //Arrange
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 3);
        var tagIds = tags.Select(x => x.Id).ToList();
        var dosageForms = await DosageFormsTestEntitiesBuilder.Build(dbContext, 1);
        var dosageFormId = dosageForms[0].Id;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var routesOfAdministration = await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 1);
        var routeOfAdministrationId = routesOfAdministration[0].Id;
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtension = new ProductExtensionModel(
            pcid: Fake.ProductExtension.PCID,
            dosageFormId: dosageFormId, routeOfAdministrationIds: [routeOfAdministrationId]);

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithProductExtensions(productExtension)
            .WithDrugSubstances(drugSubstances.Select(d => d.Id).ToList())
            .WithProductTypes(productTypeIds)
            .Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productResponse.ProductExtensions[0].Id, RouteOfAdministrationIds = productExtension.RouteOfAdministrationIds })
            .WithDrugSubstanceIds(productResponse.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds([.. tagIds]).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Act
        var tagResponse = () => tagApi.DeleteTagAsync(tags[0].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await tagResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task DeleteTag_InvalidTagId_ThrowsEntityNotFoundExceptionException()
    {
        //Arrange
        var tagId = Fake.Tag.Id;

        //Act
        var response = () => tagApi.DeleteTagAsync(tagId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"Tag\\\" ({tagId}) was not found.", exception.And.Message);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.Comments.Clear();
        dbContext.Communications.Clear();
        dbContext.Tags.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DrugSubstances.Clear();
        await dbContext.SaveChangesAsync();
    }
}
