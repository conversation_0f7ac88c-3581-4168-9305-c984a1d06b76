DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @tenant varchar(30) = N'pharmalex';

INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'NBE', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'NCE', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false',@tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'ATMP', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'Oligonucleotides', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'Synthetic peptides', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'ADC', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)
INSERT INTO [dbo].[ProductTypes] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted, Tenant) VALUES (N'Not Categorized', getdate(), @hacommsuser, getdate(), @hacommsuser, 'false', @tenant)