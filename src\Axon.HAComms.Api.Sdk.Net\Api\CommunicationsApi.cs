/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Api.Sdk.Net.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ICommunicationsApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void CompleteCommunication(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> CompleteCommunicationWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <returns>CreateCommunicationCommandResponse</returns>
        CreateCommunicationCommandResponse CreateCommunication(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateCommunicationCommandResponse</returns>
        ApiResponse<CreateCommunicationCommandResponse> CreateCommunicationWithHttpInfo(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void DeleteCommunication(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteCommunicationWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>CommunicationModel</returns>
        CommunicationModel GetCommunication(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of CommunicationModel</returns>
        ApiResponse<CommunicationModel> GetCommunicationWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>CommunicationPagedListModelApiPagedListResult</returns>
        CommunicationPagedListModelApiPagedListResult GetPagedCommunicationsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of CommunicationPagedListModelApiPagedListResult</returns>
        ApiResponse<CommunicationPagedListModelApiPagedListResult> GetPagedCommunicationsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void ReinstateCommunication(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> ReinstateCommunicationWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <returns>UpdateCommunicationCommandResponse</returns>
        UpdateCommunicationCommandResponse UpdateCommunication(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateCommunicationCommandResponse</returns>
        ApiResponse<UpdateCommunicationCommandResponse> UpdateCommunicationWithHttpInfo(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ICommunicationsApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task CompleteCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> CompleteCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateCommunicationCommandResponse</returns>
        System.Threading.Tasks.Task<CreateCommunicationCommandResponse> CreateCommunicationAsync(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateCommunicationCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CreateCommunicationCommandResponse>> CreateCommunicationWithHttpInfoAsync(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommunicationModel</returns>
        System.Threading.Tasks.Task<CommunicationModel> GetCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommunicationModel)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommunicationModel>> GetCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommunicationPagedListModelApiPagedListResult</returns>
        System.Threading.Tasks.Task<CommunicationPagedListModelApiPagedListResult> GetPagedCommunicationsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommunicationPagedListModelApiPagedListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<CommunicationPagedListModelApiPagedListResult>> GetPagedCommunicationsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task ReinstateCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ReinstateCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateCommunicationCommandResponse</returns>
        System.Threading.Tasks.Task<UpdateCommunicationCommandResponse> UpdateCommunicationAsync(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateCommunicationCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UpdateCommunicationCommandResponse>> UpdateCommunicationWithHttpInfoAsync(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface ICommunicationsApi : ICommunicationsApiSync, ICommunicationsApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class CommunicationsApi : IDisposable, ICommunicationsApi
    {
        private Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public CommunicationsApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public CommunicationsApi(string basePath)
        {
            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public CommunicationsApi(Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public CommunicationsApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public CommunicationsApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public CommunicationsApi(HttpClient client, Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationsApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public CommunicationsApi(Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient client, Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient asyncClient, Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void CompleteCommunication(int id, string tenant)
        {
            CompleteCommunicationWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> CompleteCommunicationWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->CompleteCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<Object>("/{tenant}/v1/Communications/{id}/complete", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompleteCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task CompleteCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await CompleteCommunicationWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> CompleteCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->CompleteCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<Object>("/{tenant}/v1/Communications/{id}/complete", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompleteCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <returns>CreateCommunicationCommandResponse</returns>
        public CreateCommunicationCommandResponse CreateCommunication(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateCommunicationCommandResponse> localVarResponse = CreateCommunicationWithHttpInfo(tenant, createCommunicationCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateCommunicationCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateCommunicationCommandResponse> CreateCommunicationWithHttpInfo(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->CreateCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createCommunicationCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CreateCommunicationCommandResponse>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateCommunicationCommandResponse</returns>
        public async System.Threading.Tasks.Task<CreateCommunicationCommandResponse> CreateCommunicationAsync(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateCommunicationCommandResponse> localVarResponse = await CreateCommunicationWithHttpInfoAsync(tenant, createCommunicationCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateCommunicationCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateCommunicationCommandResponse>> CreateCommunicationWithHttpInfoAsync(string tenant, CreateCommunicationCommandRequest? createCommunicationCommandRequest = default(CreateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->CreateCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createCommunicationCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CreateCommunicationCommandResponse>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void DeleteCommunication(int id, string tenant)
        {
            DeleteCommunicationWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> DeleteCommunicationWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->DeleteCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/{tenant}/v1/Communications/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteCommunicationWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> DeleteCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->DeleteCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/{tenant}/v1/Communications/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>CommunicationModel</returns>
        public CommunicationModel GetCommunication(int id, string tenant)
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationModel> localVarResponse = GetCommunicationWithHttpInfo(id, tenant);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of CommunicationModel</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationModel> GetCommunicationWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->GetCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<CommunicationModel>("/{tenant}/v1/Communications/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommunicationModel</returns>
        public async System.Threading.Tasks.Task<CommunicationModel> GetCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationModel> localVarResponse = await GetCommunicationWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommunicationModel)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationModel>> GetCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->GetCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<CommunicationModel>("/{tenant}/v1/Communications/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>CommunicationPagedListModelApiPagedListResult</returns>
        public CommunicationPagedListModelApiPagedListResult GetPagedCommunicationsList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationPagedListModelApiPagedListResult> localVarResponse = GetPagedCommunicationsListWithHttpInfo(tenant, filters, skip, take, order);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of CommunicationPagedListModelApiPagedListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationPagedListModelApiPagedListResult> GetPagedCommunicationsListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->GetPagedCommunicationsList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<CommunicationPagedListModelApiPagedListResult>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedCommunicationsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CommunicationPagedListModelApiPagedListResult</returns>
        public async System.Threading.Tasks.Task<CommunicationPagedListModelApiPagedListResult> GetPagedCommunicationsListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationPagedListModelApiPagedListResult> localVarResponse = await GetPagedCommunicationsListWithHttpInfoAsync(tenant, filters, skip, take, order, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CommunicationPagedListModelApiPagedListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CommunicationPagedListModelApiPagedListResult>> GetPagedCommunicationsListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->GetPagedCommunicationsList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<CommunicationPagedListModelApiPagedListResult>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedCommunicationsList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void ReinstateCommunication(int id, string tenant)
        {
            ReinstateCommunicationWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> ReinstateCommunicationWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->ReinstateCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<Object>("/{tenant}/v1/Communications/{id}/reinstate", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ReinstateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task ReinstateCommunicationAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await ReinstateCommunicationWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> ReinstateCommunicationWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->ReinstateCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<Object>("/{tenant}/v1/Communications/{id}/reinstate", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ReinstateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <returns>UpdateCommunicationCommandResponse</returns>
        public UpdateCommunicationCommandResponse UpdateCommunication(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateCommunicationCommandResponse> localVarResponse = UpdateCommunicationWithHttpInfo(tenant, updateCommunicationCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateCommunicationCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateCommunicationCommandResponse> UpdateCommunicationWithHttpInfo(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->UpdateCommunication");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateCommunicationCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<UpdateCommunicationCommandResponse>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateCommunicationCommandResponse</returns>
        public async System.Threading.Tasks.Task<UpdateCommunicationCommandResponse> UpdateCommunicationAsync(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateCommunicationCommandResponse> localVarResponse = await UpdateCommunicationWithHttpInfoAsync(tenant, updateCommunicationCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateCommunicationCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateCommunicationCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateCommunicationCommandResponse>> UpdateCommunicationWithHttpInfoAsync(string tenant, UpdateCommunicationCommandRequest? updateCommunicationCommandRequest = default(UpdateCommunicationCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling CommunicationsApi->UpdateCommunication");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateCommunicationCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<UpdateCommunicationCommandResponse>("/{tenant}/v1/Communications", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateCommunication", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
