﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Tags;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateTagIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly TagApi tagApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateTag_ValidRequest_ReturnsOk()
    {
        //Arrange
        var createTagName = Fake.Tag.Name; 
        var updateTagName = Fake.Tag.Name;
        var createTagDescription = Fake.Tag.Description;
        var updateTagDescription = Fake.Tag.Description;
        var requestObj1 = new CreateTagCommandRequest(createTagName, createTagDescription);

        //Act
        var responseObj1 = await tagApi.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();
        var requestObj2 = new UpdateTagCommandRequest(responseObj1.Id, updateTagName, updateTagDescription);

        await tagApi.UpdateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj2);
        var responseObj = await tagApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        var sut = responseObj.Data.SingleOrDefault(s => s.Id == responseObj1.Id);
        sut.Should().NotBeNull();
        sut?.Name.Should().Be(requestObj2.Name);
        sut?.Description.Should().Be(requestObj2.Description);
    }

    [Fact]
    public async Task UpdateTag_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var tagName1 = Fake.Tag.Name;
        var tagName2 = Fake.Tag.Name;

        var requestObj1 = new CreateTagCommandRequest(tagName1, Fake.Tag.Description);
        var requestObj2 = new CreateTagCommandRequest(tagName2, Fake.Tag.Description);

        //Act
        var responseObj1 = await tagApi.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj1);
        var responseObj2 = await tagApi.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        responseObj1.Should().NotBeNull();
        responseObj2.Should().NotBeNull();

        //Act
        var requestObj3 = new UpdateTagCommandRequest(responseObj1.Id, tagName2);

        var tagResponse = () => tagApi.UpdateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj3);

        //Assert
        var exception = await tagResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Tag with name '{tagName2}' already exists.");
    }

    [Fact]
    public async Task UpdateTag_InvalidTagId_ThrowsEntityNotFoundExceptionException()
    {
        //Arrange
        var tagId = Fake.Tag.Id;
        var tagName = Fake.Tag.Name;
        var request = new UpdateTagCommandRequest(tagId, tagName);

        //Act
        var response = () => tagApi.UpdateTagAsync(TenantConstants.DEFAULT_TENANT, request);

        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"Tag\\\" ({tagId}) was not found.", exception.And.Message);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.Tags.Clear();
        await dbContext.SaveChangesAsync();
    }
}
