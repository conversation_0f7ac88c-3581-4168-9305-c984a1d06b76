﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Tags.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Update;

public class UpdateTagCommandHandlerTests
{
    private readonly UpdateTagCommandHandler sut;
    private readonly ITagRepository tagsRepository;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public UpdateTagCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new TagMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        tagsRepository = Substitute.For<ITagRepository>();
        auditService = Substitute.For<IAuditService>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<UpdateTagCommandHandler>>();
        sut = new UpdateTagCommandHandler(tagsRepository, mapper, logger, auditService, correlationIdProvider, clientDetailsProvider, userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;

        var entity = new Tag(1, tagName) { Description = tagDescription };
        tagsRepository.GetItemAsync(1).Returns(entity);

        var request = new UpdateTagCommandRequest(1, entity.Name, entity.Description);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(1);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;

        var entity = new Tag(1, tagName)
        {
            Description = tagDescription
        };
        tagsRepository.GetItemAsync(1).Returns(entity);

        var request = new UpdateTagCommandRequest(1, entity.Name, entity.Description);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.TAG_UPDATED, AuditEventCategory.TAGS, AuditEventDescription.TAG_UPDATE,
                Arg.Any<Tag>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new Tag { Name = Fake.Tag.Name, Description = Fake.Tag.Description };
        tagsRepository.GetItemAsync(entity.Id).ReturnsNull();

        var request = new UpdateTagCommandRequest(Fake.Tag.Id, entity.Name, entity.Description);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
