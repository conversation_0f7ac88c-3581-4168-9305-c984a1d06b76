﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class DrugProductConfiguration : BaseEntityConfiguration<Product>
{
    protected override void ConfigureEntity(EntityTypeBuilder<Product> builder)
    {
        builder.HasIndex(d => new { d.Name, d.Tenant }).IsUnique();

        builder.HasMany(p => p.DrugSubstances)
            .WithMany(ds => ds.Products)
            .UsingEntity<DrugSubstanceDrugProduct>(
                l => l.<PERSON>(dsp => dsp.DrugSubstance).WithMany(p => p.DrugSubstanceProducts).HasForeignKey(dsp => dsp.DrugSubstancesId),
                r => r.<PERSON>(dsp => dsp.Product).WithMany(ds => ds.DrugSubstanceProducts).HasF<PERSON><PERSON><PERSON>(dsp => dsp.ProductsId),
                b => b.To<PERSON>able("DrugSubstanceProducts"));
    }
}
