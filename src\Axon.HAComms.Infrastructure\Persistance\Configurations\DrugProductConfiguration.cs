﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class DrugProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.HasIndex(d => new { d.Name, d.Tenant }).IsUnique();

        builder.HasMany(p => p.DrugSubstances)
            .WithMany(ds => ds.Products)
            .UsingEntity<DrugSubstanceDrugProduct>(
                l => l.<PERSON>ne(dsp => dsp.DrugSubstance).WithMany(p => p.DrugSubstanceProducts).HasForeignKey(dsp => dsp.DrugSubstancesId),
                r => r.<PERSON>ne(dsp => dsp.Product).WithMany(ds => ds.DrugSubstanceProducts).HasForeignKey(dsp => dsp.ProductsId),
                b => b.ToTable("DrugSubstanceProducts"));

        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
