using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;

public class CreateTagsBuilder
{
    private string name = Fake.Tag.Name;
    private string description = Fake.Tag.Description;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static CreateTagsBuilder Default() => new();

    public Tag Build()
    {
        return new Tag()
        {
            Name = name,
            Description = description,
            Tenant = tenant
        };
    }

    public CreateTagsBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CreateTagsBuilder WithDescription(string description)
    {
        this.description = description;
        return this;
    }

    public CreateTagsBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
