﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface IProductExtensionsRepository : IRepository<ProductExtension>
{
    IQueryable<ProductExtension> GetQueryableItems();

    IQueryable<ProductExtension> GetQueryableItemsWithIncludes();

    void DeleteItems(params ProductExtension[] productExtensions);

    Task<List<string>> GetFilteredListAsync(
        Expression<Func<ProductExtension, bool>> filter,
        Expression<Func<ProductExtension, string>> selector);

    public Task<bool> ExistsAsync(Expression<Func<ProductExtension, bool>> filter);
}
