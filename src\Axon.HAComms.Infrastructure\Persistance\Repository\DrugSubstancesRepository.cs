﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class DrugSubstancesRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<DrugSubstancesRepository> logger)
    : SqlServerRepository<DrugSubstance>(context, tenant, logger), IDrugSubstancesRepository
{
    public async Task<IEnumerable<DrugSubstance>> GetItemsAsync()
    {
        return await context
            .Set<DrugSubstance>()
            .AsNoTracking()
            .Where(x => !x.IsDeleted)
            .ToListAsync();
    }

    public IQueryable<DrugSubstance> GetQueryableItems()
    {
        return context.Set<DrugSubstance>()
            .Include(x => x.Comments)
            .Include(x => x.Products)
            .AsQueryable();
    }

    public async Task<DrugSubstance> GetItemAsync(int id)
    {
        var entity = await context.Set<DrugSubstance>().SingleOrDefaultAsync(d => d.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(DrugSubstance), id);
    }

    public async Task<bool> ExistsAsync(Expression<Func<DrugSubstance, bool>> filter)
    {
        return await context.Set<DrugSubstance>().AsNoTracking().AnyAsync(filter);
    }

    public async Task<List<DrugSubstance>> GetAllByIdsAsync(params int[] ids)
    {
        var items = await context.Set<DrugSubstance>().Where(x => ids.Contains(x.Id)).ToListAsync();
        return items;
    }
}
