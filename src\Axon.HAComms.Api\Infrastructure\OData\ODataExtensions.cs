﻿using Axon.Core.Shared.Audit;
using Axon.HAComms.Api.JsonHandling;
using Axon.HAComms.Application.Common;
using Microsoft.AspNetCore.OData;
using Microsoft.AspNetCore.OData.Formatter.Serialization;
using Microsoft.OData.ModelBuilder;
using System.Text.Json;

namespace Axon.HAComms.Api.Infrastructure.OData;

public static class ODataExtensions
{
    public static void AddOData(this IServiceCollection services)
    {
        var odataModelBuilder = new ODataConventionModelBuilder();
        odataModelBuilder.EntitySet<ApplicationAudit>("Audits");
        odataModelBuilder.EntityType<ApplicationAudit>().ComplexProperty(a => a.Environment);
        odataModelBuilder.EntityType<ApplicationAudit>().ComplexProperty(a => a.Target);

        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new DateTimeConverter());
                options.JsonSerializerOptions.Converters.Add(new IntConverter());
                options.JsonSerializerOptions.Converters.Add(new BooleanConverter());
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .AddOData(
                options => options.Filter().OrderBy().Count().SetMaxTop(Constants.O_DATA_MAX_TOP).AddRouteComponents(
                    "{tenant}/v1/odata",
                    odataModelBuilder.GetEdmModel(),
                    service => service.AddSingleton<IODataSerializerProvider, CustomODataSerializerProvider>()));
    }
}
