using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Api.Controllers;

[Authorize]
public abstract class ApiControllerBase(IMediator mediator) : ControllerBase
{
    protected readonly IMediator Mediator = mediator;

    /// <summary>
    /// Send a request without specified result model.
    /// </summary>
    /// <param name="request">Command or query model</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns></returns>
    protected async Task<IActionResult> Send(IRequest<Result> request, CancellationToken ct = default)
    {
        var commandResult = await Mediator.Send(request, ct);
        return Ok(commandResult);
    }

    /// <summary>
    /// Send a request with the specified result model.
    /// </summary>
    /// <param name="request">Command or query model</param>
    /// <param name="ct">Cancellation token</param>
    protected async Task<IActionResult> Send<TResponse>(IRequest<TResponse> request, CancellationToken ct = default)
    {
        var commandResult = await Mediator.Send(request, ct);
        return Ok(commandResult);
    }
}
