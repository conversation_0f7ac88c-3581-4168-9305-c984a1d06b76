﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class CommentDrugSubstancesConfiguration : IEntityTypeConfiguration<CommentDrugSubstances>
{
    public void Configure(EntityTypeBuilder<CommentDrugSubstances> builder)
    {
        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
