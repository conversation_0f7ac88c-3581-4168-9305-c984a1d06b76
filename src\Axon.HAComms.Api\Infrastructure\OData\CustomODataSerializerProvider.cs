﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Formatter.Serialization;
using Microsoft.OData;

namespace Axon.HAComms.Api.Infrastructure.OData;

public class CustomODataSerializerProvider(IServiceProvider sp) : ODataSerializerProvider(sp)
{
    public override IODataSerializer GetODataPayloadSerializer(Type type, HttpRequest request)
    {
        if (type == typeof(ODataError) || type == typeof(SerializableError))
        {
            return new CustomODataErrorSerializer();
        }

        return base.GetODataPayloadSerializer(type, request);
    }
}
