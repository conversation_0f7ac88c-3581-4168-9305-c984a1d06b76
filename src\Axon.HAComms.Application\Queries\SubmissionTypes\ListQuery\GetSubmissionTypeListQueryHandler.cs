﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.SubmissionType;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.SubmissionTypes.ListQuery;

internal class GetSubmissionTypeListQueryHandler(ISubmissionTypesRepository repo, IMapper mapper)
    : IRequestHandler<GetSubmissionTypeListQueryRequest, ApiListResult<SubmissionTypeModel>>
{
    public async Task<ApiListResult<SubmissionTypeModel>> Handle(GetSubmissionTypeListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync();
        return new ApiListResult<SubmissionTypeModel>(mapper.Map<List<SubmissionTypeModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
