﻿using System.Diagnostics.CodeAnalysis;
using Axon.HAComms.Application.Common.Interfaces;
using Azure.Search.Documents.Models;

namespace Axon.HAComms.Application.AzureSearch;

// Used to substitute real azure search helper class in Integration tests
[ExcludeFromCodeCoverage]
public class AzureSearchIntTestsHelper : IAzureSearchHelper
{
    public Task CreateDataSource()
    {
        return Task.CompletedTask;
    }

    public Task CreateIndexAsync<T>()
    {
        return Task.CompletedTask;
    }

    public Task CreateIndexer()
    {
        return Task.CompletedTask;
    }

    public Task DeleteDataSource()
    {
        return Task.CompletedTask;
    }

    public Task DeleteIndex()
    {
        return Task.CompletedTask;
    }

    public Task DeleteIndexer()
    {
        return Task.CompletedTask;
    }

    public Task<SearchResults<T>> TextSearchAsync<T>(
        string searchText,
        int? skip,
        int? take,
        string? sort,
        string[]? selectFields = null,
        string? filter = null,
        bool fuzzy = false)
    {
        throw new NotImplementedException();
    }
}
