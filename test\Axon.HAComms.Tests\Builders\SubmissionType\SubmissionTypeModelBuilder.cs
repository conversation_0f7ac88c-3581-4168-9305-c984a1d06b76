﻿using Axon.HAComms.Application.Models.SubmissionType;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.SubmissionType;

public class SubmissionTypeModelBuilder : IBuilder<SubmissionTypeModel>
{
    private int id = Fake.SubmissionType.Id;
    private string name = Fake.SubmissionType.Name;

    public static SubmissionTypeModelBuilder Default() => new();

    public SubmissionTypeModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public SubmissionTypeModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public SubmissionTypeModel Build()
    {
        return new(name)
        {
            Id = id,
            Name = name
        };
    }
}
