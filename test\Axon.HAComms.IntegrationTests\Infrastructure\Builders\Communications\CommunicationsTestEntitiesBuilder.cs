using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;

public static class CommunicationsTestEntitiesBuilder
{
    public static async Task<List<Communication>> Build(
        HACommsContext dbContext,
        List<Product> products,
        int entries = 100)
    {
        var submissionTypes = await dbContext.Set<SubmissionType>().ToListAsync();
        var countries = await dbContext.Set<Country>().ToListAsync();

        var list = new List<Communication>();
        for (var i = 0; i < entries; i++)
        {
            var random = new Random();
            var randomSubmissionTypeIndex = random.Next(0, submissionTypes.Count - 1);
            var randomCountryIndex = random.Next(0, countries.Count - 1);
            var randomProductId = random.Next(0, products.Count - 1);
            var request = CommunicationBuilder.Default()
                .WithSubmissionTypeId(submissionTypes[randomSubmissionTypeIndex].Id)
                .WithCountryId(countries[randomCountryIndex].Id)
                .WithApplications(Applications.ApplicationBuilder.Default().WithSubmissions(SubmissionBuilder.Default().Build()).Build())
                .WithComments(new Comment()
                {
                    ProductExtensions = new List<ProductExtension> { products[randomProductId].ProductExtensions.Single() },
                    Description = Fake.Comment.Description,
                    CommentDrugSubstances = [new CommentDrugSubstances { DrugSubstanceId = products[randomProductId].DrugSubstances.Single().Id }],
                    Tenant = TenantConstants.DEFAULT_TENANT
                })
                .Build();
            list.Add(request);
        }

        dbContext.Communications.AddRange(list);
        await dbContext.SaveChangesAsync();

        return list;
    }
}
