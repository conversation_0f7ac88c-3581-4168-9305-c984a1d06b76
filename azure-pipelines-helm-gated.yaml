name: '$(Date:yyyyMMdd).$(Rev:r)'

trigger:
  paths:
    include:
    - helm/*

resources:
  repositories:
    - repository: devops-templates
      name: DevOps/DevOps.Pipelines.Templates
      type: git
    - repository: axon-templates
      name: Axon/Axon.Pipelines.Templates
      type: git

variables:
  - template: Variables/environments.yml@axon-templates


pool: pv-pool

steps:
  - task: 'AzureCLI@2'
    inputs:
      azureSubscription: $(development_subscription)
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az aks get-credentials -n $(dev_cluster) -g $(dev_rg) --admin --overwrite-existing
  - template: Helm/gated.yaml@devops-templates
    parameters:
      useCheckovHelmScan: true
      chartPath: deploy/helm/api-chart