﻿using Axon.HAComms.Application.Models.Application;
using Axon.HAComms.Tests.Builders.Submissions;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Applications
{
    public class ApplicationModelBuilder : IBuilder<ApplicationModel>
    {
        private string number = Fake.Application.Number;
        private readonly List<SubmissionModelBuilder>? submissionsBuilder;

        public ApplicationModelBuilder()
        {
            submissionsBuilder = new List<SubmissionModelBuilder>();
        }

        public static ApplicationModelBuilder Default() => new();

        public ApplicationModelBuilder WithNumber(string number)
        {
            this.number = number;
            return this;
        }

        public ApplicationModelBuilder WithSubmission(Action<SubmissionModelBuilder> action)
        {
            var builder = new SubmissionModelBuilder();
            action.Invoke(builder);
            submissionsBuilder?.Add(builder);
            return this;
        }

        public ApplicationModel Build()
        {
            return new()
            {
                Number = number,
                Submissions = submissionsBuilder?.Select(x => x.Build()).ToList()
            };
        }
    }
}
