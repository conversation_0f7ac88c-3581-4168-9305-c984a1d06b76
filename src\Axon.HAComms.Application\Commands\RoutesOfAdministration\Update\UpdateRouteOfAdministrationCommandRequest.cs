﻿using MediatR;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Update
{
    public class UpdateRouteOfAdministrationCommandRequest : IRequest<UpdateRouteOfAdministrationCommandResponse>
    {
        public int Id { get; }
        [Required]
        public string Name { get; }

        public UpdateRouteOfAdministrationCommandRequest(int id, string name)
        {
            Id = id;
            Name = name;
        }
    }
}
