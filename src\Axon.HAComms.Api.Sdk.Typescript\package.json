{"name": "axon-hacomms-api-sdk", "version": "1.0.0", "description": "OpenAPI client for ", "author": "OpenAPI-Generator Contributors", "keywords": ["axios", "typescript", "openapi-client", "openapi-generator", ""], "license": "Unlicense", "main": "./dist/index.js", "typings": "./dist/index.d.ts", "scripts": {"build": "tsc --outDir dist/", "prepublishOnly": "npm run build"}, "dependencies": {"axios": "^1.8.4"}, "devDependencies": {"@types/node": "^12.11.5", "typescript": "^4.0"}}