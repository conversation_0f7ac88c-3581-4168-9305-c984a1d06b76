using Axon.HAComms.Application.Models.Application;
using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Application.Models.SubmissionType;

namespace Axon.HAComms.Application.Models.Communications;

public class CommunicationModel
{
    public int Id { get; set; }

    public string Subject { get; set; } = string.Empty;

    public CountryModel Country { get; set; } = new CountryModel(0, string.Empty);

    public DateTime DateOfCommunication { get; set; }
    public SubmissionTypeModel SubmissionType { get; set; } = new SubmissionTypeModel(string.Empty);
    public List<ApplicationModel> Applications { get; set; } = [];
    public bool IsCompleted { get; set; }
    public string LastUpdatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public int GeneralGuidanceCommentsCount { get; set; }

    public ProductDtoModel[]? Products { get; set; }

    public CommunicationModel()
    {

    }

    public CommunicationModel(
        string subject,
        CountryModel country,
        DateTime dateOfCommunication,
        List<ApplicationModel> applications,
        SubmissionTypeModel submissionType,
        bool isCompleted,
        string lastUpdatedBy,
        DateTime createdDate,
        DateTime lastUpdatedDate,
        string createdBy,
        int generalGuidanceCommentsCount,
        ProductDtoModel[]? products)
    {
        Subject = subject;
        Country = country;
        DateOfCommunication = dateOfCommunication;
        Applications = applications;
        SubmissionType = submissionType;
        IsCompleted = isCompleted;
        LastUpdatedBy = lastUpdatedBy;
        CreatedDate = createdDate;
        CreatedBy = createdBy;
        LastUpdatedDate = lastUpdatedDate;
        GeneralGuidanceCommentsCount = generalGuidanceCommentsCount;
        Products = products;
    }
}
