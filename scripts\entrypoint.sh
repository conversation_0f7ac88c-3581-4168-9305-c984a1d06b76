#!/bin/sh
dotnet test ${testProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/UnitTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
&& dotnet test ${archTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/ArchTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" \
&& dotnet test ${intTestProjectName} -c ${configuration_env} --logger "xunit;LogFilePath=/tests/output/TestResults/IntegrationTestResults.xml" --collect "XPlat Code coverage" --results-directory "/tests/output/TestResults" --filter Environment!=Full