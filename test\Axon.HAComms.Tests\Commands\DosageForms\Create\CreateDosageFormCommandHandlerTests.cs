﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.DosageForms.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DosageForms.Create;

public class CreateDosageFormCommandHandlerTests
{
    private readonly CreateDosageFormCommandHandler sut;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public CreateDosageFormCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DosageFormsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var dosageFormsRepo = Substitute.For<IDosageFormsRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<CreateDosageFormCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        sut = new CreateDosageFormCommandHandler(dosageFormsRepo, mapper, logger,
            correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var dosageFormName = Fake.DosageForm.Name;

        var request = new CreateDosageFormCommandRequest(dosageFormName);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.DOSAGE_FORM_CREATED, AuditEventCategory.DOSAGE_FORMS, AuditEventDescription.DOSAGE_FORM_CREATE,
                Arg.Any<DosageForm>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsCorrectEntity()
    {
        // Arrange
        var dosageFormName = Fake.DosageForm.Name;

        var request = new CreateDosageFormCommandRequest(dosageFormName);

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.DOSAGE_FORM_CREATED,
                AuditEventCategory.DOSAGE_FORMS,
                AuditEventDescription.DOSAGE_FORM_CREATE,
                Arg.Any<DosageForm>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Name.Should().Be(request.Name);
    }
}
