﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.ProductTypes
{
    public class ProductTypeBuilder : IBuilder<ProductType>
    {
        private int id;
        private string name = Fake.ProductType.Name;
        private readonly DateTime createdDate = DateTime.Now;
        private readonly DateTime lastUpdatedDate = DateTime.Now;
        private readonly string lastUpdatedBy = Fake.ProductType.LastUpdatedBy;
        private readonly string createdBy = Fake.ProductType.CreatedBy;

        public ProductTypeBuilder()
        {
            id = Fake.Tag.Id;
            name = Fake.Tag.Name;
        }

        public ProductTypeBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public ProductType Build()
        {
            return new(this.id)
            {
                Name = name,
                CreatedDate = createdDate,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy,
                CreatedBy = createdBy,
            };
        }
    }
}
