﻿using AutoMapper;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings;

public class ProductExtensionsMappingProfile : Profile
{
    public ProductExtensionsMappingProfile()
    {
        CreateMap<ProductExtension, ProductExtensionModel>()
            .ForMember(dest => dest.DosageFormId, o => o.MapFrom(s => s.DosageForm.Id))
            .ForMember(dest => dest.DosageFormName, o => o.MapFrom(s => s.DosageForm.Name))
            .ForMember(dest => dest.RouteOfAdministrationIds, o => o.MapFrom(s => s.RouteOfAdministrations.Select(x => x.Id)));
        CreateMap<CommentProductExtension, ProductExtensionModel>()
            .ForMember(dest => dest.Id, o => o.MapFrom(s => s.ProductExtensionId))
            .ForMember(dest => dest.IsActive, o => o.MapFrom(s => s.ProductExtension != null && s.ProductExtension.IsActive))
            .ForMember(dest => dest.ProductId, o => o.MapFrom(s => s.ProductExtension != null ? s.ProductExtension.ProductId : 0))
            .ForMember(dest => dest.PCID, o => o.MapFrom(s => s.ProductExtension != null ? s.ProductExtension.PCID : string.Empty))
            .ForMember(dest => dest.DosageFormId, o => o.MapFrom(s => s.ProductExtension != null ? s.ProductExtension.DosageForm.Id : 0))
            .ForMember(dest => dest.DosageFormName, o => o.MapFrom(s => s.ProductExtension != null ? s.ProductExtension.DosageForm.Name : string.Empty))
            .ForMember(dest => dest.RouteOfAdministrations, o => o.MapFrom(s => s.CommentProductExtensionRoutesOfAdministrations.Select(r => r.RouteOfAdministration)))
            .ForMember(dest => dest.RouteOfAdministrationIds, o => o.MapFrom(s => s.CommentProductExtensionRoutesOfAdministrations.Select(x => x.RouteOfAdministrationId)));
        CreateMap<ProductExtensionModel, ProductExtension>()
            .ForMember(dest => dest.RouteOfAdministrations, o => o.Ignore())
            .ForMember(dest => dest.DosageForm, o => o.Ignore())
            .ForMember(dest => dest.Product, o => o.Ignore())
            .ForMember(dest => dest.ProductId, o => o.Ignore())
            .ForMember(dest => dest.ProductExtensionRouteOfAdministrations, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore());
        CreateMap<ProductExtension, ProductExtensionResponseModel>()
            .ForMember(dest => dest.RoutesOfAdministration, o => o.MapFrom(s => s.RouteOfAdministrations))
            .ForMember(dest => dest.IsAssociatedToComment, o => o.Ignore());
    }
}
