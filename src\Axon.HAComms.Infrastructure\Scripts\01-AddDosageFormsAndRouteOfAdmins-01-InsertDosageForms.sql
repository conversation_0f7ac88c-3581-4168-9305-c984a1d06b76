DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

INSERT INTO [dbo].[DosageForms] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
VALUES 
(N'AAV2 VECTOR FOR GENE THERAPY', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ANTICOAGULANT SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE AND SOLVENT FOR CUTANEOUS SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE FOR EMULSION FOR INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE FOR ORAL SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE FOR ORAL SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE FOR SOLUTION FOR INJECTION/INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CUTANEOUS EMULSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CUTANEOUS POWDER', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CUTANEOUS SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'DENTAL CEMENT', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'DISPERSION FOR CONCENTRATE FOR DISPERSION FOR INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'DISPERSION FOR INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EAR DROPS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EAR DROPS, SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EMULSION FOR INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ENDOTRACHEOPULMONARY INSTILLATION, POWDER AND SOLVENT FOR SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ENDOTRACHEOPULMONARY INSTILLATION, POWDER FOR SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EndOTRACHEOPULMONARY USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EYE CREAM', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EYE DROPS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EYE DROPS, PROLONGED-RELEASE SOLUTION IN SINGLE-DOSE CONTAINER', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EYE OINTMENT', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GAS FOR DISPERSION FOR INJECTION/INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GASTRIC USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GASTROENTERAL EMULSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GASTROENTERAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GRANULES', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GRANULES FOR SYRUP', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'IMPLANT IN PRE-FILLED SYRINGE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACEREBROVENTRICULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACORNEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACORONARY USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRALESIONAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAOSSEOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPERITONEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPROSTATIC USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAUTERINE USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS USE AND ORAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVITREAL IMPLANT IN APPLICATOR', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVITREAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'LIQUID DILUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'LIVING TISSUE EQUIVALENT', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Lotion, spray', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'MODIFIED-RELEASE TABLET', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'MOUTHWASH, TABLET FOR SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'NASAL CREAM', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'NASAL GEL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'NASAL SPRAY, SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'NEBULISER DISPERSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL EMULSION IN SACHET', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL SOLUBLE FILM', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL SOLUTION/CONCENTRATE FOR NEBULISER SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROMUCOSAL CAPSULE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROMUCOSAL DROPS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROMUCOSAL LIQUID', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROMUCOSAL SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PILLULES', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POULTICE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR ENDOSINUSIAL SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR INTRAVESICAL SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR INTRAVESICAL SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR SEALANT', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR SOLUTION FOR INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR SOLUTION FOR INTRAOCULAR IRRIGATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER AND SOLVENT FOR SUSPENSION FOR INJECTION IN PRE-FILLED SYRINGE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER FOR BLADDER IRRIGATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER FOR DENTAL GEL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER FOR GINGIVAL GEL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER FOR INTRAVESICAL SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'POWDER FOR NEBULISER SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PRESSURISED INHALATION, SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'RECTAL TAMPON', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'RETROBULBAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLUTIO FOR ADDITION TO BICARBONATE CONCENTRATE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLUTIO FOR INFUSION/INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLUTION FOR INFUSION IN CARTRIDGE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLUTION FOR INJECTION IN ADMINISTRATION SYSTEM', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLUTION FOR INTRAVITREAL INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SOLVENT FOR PARENTERAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUBLINGUAL SPRAY, SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUPPOSITORY', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUSPENSION FOR INJECTION IN PRE-FILLED PEN', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'TABLET AND SOLVENT FOR RECTAL SUSPENSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'TABLET FOR VAGINAL SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'TABLETS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'VAGINAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Wipes', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted);
