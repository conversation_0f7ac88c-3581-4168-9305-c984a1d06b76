﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore.Query;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface ICommentsRepository : IRepository<Comment>
{
    /// <summary>
    /// Get Comment object without its sub collections
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="EntityNotFoundException"></exception>
    Task<Comment> GetItemByIdAsync(int id);

    Task<Comment> GetItemWithAllIncludesAsync(int id);

    Task<bool> ExistsAsync(Expression<Func<Comment, bool>> filter);

    Task<IEnumerable<Comment>> GetItemsWithProductExtensionIdsAsync(int[] productExtensionIds);

    IQueryable<Comment> GetFilteredComments(Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>? include = null,
        Expression<Func<Comment, bool>>? filter = null);

    void DeleteItems(Comment[] comments);
}
