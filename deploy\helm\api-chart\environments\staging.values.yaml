image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: latest

ingress:
  tls:
    - tlsSecretName: tls-app-staging-smartphlex-com
      hosts:
        - app-staging.smartphlex.com
  hosts:
    - host: app-staging.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)

replicas: 2
minAvailability: 1

keyVaultName: hac-stg-kv-eun
clientId: 329eb7fa-bee5-4e15-b62f-5e785fe9eb2d #Smartphlex stg

corsOriginUrl0: https://app-staging.smartphlex.com
corsOriginUrl1: https://localhost:4070

azureSearch:
  isEnabled: true
  serviceName: ss-nonprod-ss-eun
  IndexName: hacomms-index-stg
  IndexerName: hacomms-indexer-stg
  DataSourceName: hacomms-db-stg

azureWorkload:
  appName: axon-hacomms-stg
  clientId: 30743dd3-bf91-436a-a9be-2ec2010e0e78
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AppScope: "api://smartphlex-stg/.default"
ApiHost: "https://app-staging.smartphlex.com/api/core"
GrpcHost: "http://axon-core-api-grpc.axon-core-stg.svc.cluster.local:9090"

DataProtectionBlobStorageUri: 'https://axnstgstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-stg-kv-eun.vault.azure.net/keys/AxonDataProtection'