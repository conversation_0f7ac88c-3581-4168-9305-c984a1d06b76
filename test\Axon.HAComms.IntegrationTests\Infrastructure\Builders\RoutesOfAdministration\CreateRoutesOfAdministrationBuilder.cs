using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;

public class CreateRoutesOfAdministrationBuilder
{
    private string name = Fake.RouteOfAdministration.Name;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static CreateRoutesOfAdministrationBuilder Default() => new();

    public RouteOfAdministration Build()
    {
        return new RouteOfAdministration()
        {
            Name = name,
            Tenant = tenant
        };
    }

    public CreateRoutesOfAdministrationBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CreateRoutesOfAdministrationBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
