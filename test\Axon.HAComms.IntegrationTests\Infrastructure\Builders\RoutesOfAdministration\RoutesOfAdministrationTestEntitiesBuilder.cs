using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;

public static class RoutesOfAdministrationTestEntitiesBuilder
{
    public static async Task<List<RouteOfAdministration>> Build(HACommsContext dbContext, int entries = 100)
    {
        var list = new List<RouteOfAdministration>();

        for (var i = 0; i < entries; i++)
        {
            list.Add(CreateRoutesOfAdministrationBuilder.Default().Build());
        }

        dbContext.RouteOfAdministrations.AddRange(list);
        await dbContext.SaveChangesAsync();

        return list;
    }
}
