using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Delete
{
    [UsedImplicitly]
    public class DeleteRouteOfAdministrationCommandValidator : AbstractValidator<DeleteRouteOfAdministrationCommandRequest>
    {
        public DeleteRouteOfAdministrationCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty();
        }
    }
}
