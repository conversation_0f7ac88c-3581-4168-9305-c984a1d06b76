using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;

[UsedImplicitly]
public class UpdateRouteOfAdministrationCommandValidator : AbstractValidator<UpdateRouteOfAdministrationCommandRequest>
{
    public UpdateRouteOfAdministrationCommandValidator(IRouteOfAdministrationRepository repoRoutesOfAdministration)
    {
        RuleFor(x => x.Id)
            .NotEmpty();

        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(200)
            .WithMessage("Name cannot exceed 200 characters.");

        RuleFor(x => x).Custom((request, context) =>
        {
            var exists = repoRoutesOfAdministration.ExistsAsync(x => x.Id != request.Id && (string.Equals(x.Name, request.Name))).GetAwaiter().GetResult();
            if (exists)
            {
                context.AddFailure(nameof(request.Name), $"Route of administration with name '{request.Name}' already exists.");
            }
        });
    }
}
