﻿namespace Axon.HAComms.Domain.Constants;

public static class AuditEventType
{
    public const string SEARCH_EXECUTED = "Search:Executed";
    public const string SEARCH_RESULT_VIEWED = "Search:ResultViewed";
    public const string SEARCH_FILE_EXPORTED = "Search:FileExported";

    public const string DRUG_PRODUCT_CREATED = "DrugProduct:Created";
    public const string DRUG_PRODUCT_VIEWED = "DrugProduct:Viewed";
    public const string DRUG_PRODUCT_DELETED = "DrugProduct:Deleted";
    public const string DRUG_PRODUCT_UPDATED = "DrugProduct:Updated";

    public const string DRUG_SUBSTANCE_CREATED = "DrugSubstance:Created";
    public const string DRUG_SUBSTANCE_DELETED = "DrugSubstance:Deleted";
    public const string DRUG_SUBSTANCE_UPDATED = "DrugSubstance:Updated";

    public const string ROUTE_OF_ADMINISTRATION_CREATED = "RouteOfAdministration:Created";
    public const string ROUTE_OF_ADMINISTRATION_DELETED = "RouteOfAdministration:Deleted";
    public const string ROUTE_OF_ADMINISTRATION_UPDATED = "RouteOfAdministration:Updated";

    public const string COMMENT_CREATED = "Comment:Created";
    public const string COMMENT_UPDATED = "Comment:Updated";
    public const string COMMENT_DELETED = "Comment:Deleted";

    public const string COMMUNICATION_CREATED = "Communication:Created";
    public const string COMMUNICATION_UPDATED = "Communication:Updated";
    public const string COMMUNICATION_DELETED = "Communication:Deleted";
    public const string COMMUNICATION_COMPLETED = "Communication:Completed";
    public const string COMMUNICATION_REINSTATED = "Communication:Reinstated";

    public const string DOSAGE_FORM_CREATED = "DosageForm:Created";
    public const string DOSAGE_FORM_DELETED = "DosageForm:Deleted";
    public const string DOSAGE_FORM_UPDATED = "DosageForm:Updated";

    public const string TAG_CREATED = "Tag:Created";
    public const string TAG_DELETED = "Tag:Deleted";
    public const string TAG_UPDATED = "Tag:Updated";

    public static IReadOnlyCollection<string> All
        =>
        [
            SEARCH_EXECUTED,
            SEARCH_RESULT_VIEWED,
            SEARCH_FILE_EXPORTED,

            DRUG_PRODUCT_CREATED,
            DRUG_PRODUCT_VIEWED,
            DRUG_PRODUCT_DELETED,
            DRUG_PRODUCT_UPDATED,

            DRUG_SUBSTANCE_CREATED,
            DRUG_SUBSTANCE_DELETED,
            DRUG_SUBSTANCE_UPDATED,

            COMMENT_CREATED,
            COMMENT_UPDATED,
            COMMENT_DELETED,

            COMMUNICATION_CREATED,
            COMMUNICATION_UPDATED,
            COMMUNICATION_DELETED,
            COMMUNICATION_COMPLETED,
            COMMUNICATION_REINSTATED,

            ROUTE_OF_ADMINISTRATION_CREATED,
            ROUTE_OF_ADMINISTRATION_DELETED,
            ROUTE_OF_ADMINISTRATION_UPDATED,

            DOSAGE_FORM_CREATED,
            DOSAGE_FORM_DELETED,
            DOSAGE_FORM_UPDATED,

            TAG_CREATED,
            TAG_DELETED,
            TAG_UPDATED
        ];
}
