IF EXISTS(SELECT 1 FROM sys.views WHERE NAME='SearchInComments' and TYPE='v')
DROP VIEW SearchInComments;
GO
CREATE VIEW [dbo].[SearchInComments] AS
SELECT 
	c.Id,
	c.Description,
	c.<PERSON>uid<PERSON>,
	c.Is<PERSON>uestionIncluded,
	c.<PERSON>,
	pe.ProductId,
	pe.PCID AS ProductCode,
	dp.Name AS ProductName,
	dsra.SubstanceTypeIds,
	dsra.DrugSubstanceIds,
	dsra.RouteOfAdministrationIds,
	dsra.DrugSubstanceNames,
	dsra.TagIds,
	dsra.TagNames,
	dsra.DrugTypeNames,
	pe.DosageFormId,
	CommAppSub.SubmissionTypeId,
	CommAppSub.CommunicationId,
	CommAppSub.DateOfCommunication,
	CommAppSub.IsCompleted,
	CommAppSub.CountryId AS CountryId,
	CommAppSub.Subject,
	country.Name AS CountryName,
	df.Name AS DosageFormName,
	dsra.RouteOfAdministrationNames,
	st.Name AS SubmissionTypeName,
	CommAppSub.ApplicationIds,
	CommAppSub.SubmissionIds,
	CommAppSub.ApplicationNumbers,
	CommAppSub.SubmissionNumbers
FROM 
	(SELECT ct.CommentId, gcds.SubstanceTypeIds, gcds.DrugSubstanceIds, gra.RouteOfAdministrationIds, gcds.DrugSubstanceNames, gra.RouteOfAdministrationNames, ct.TagIds, ct.TagNames, gcds.DrugTypeNames FROM  
		(SELECT gds.CommentId, '[' + STRING_AGG(gds.DrugSubstanceIds, ',') + ']' AS DrugSubstanceIds, '[' + STRING_AGG(gds.DrugTypeId, ',') + ']' AS SubstanceTypeIds, 
		STRING_AGG(gds.DrugSubstanceNames, ', ') AS DrugSubstanceNames, STRING_AGG(gds.DrugTypeName, ', ') AS DrugTypeNames FROM 
			(SELECT cds.CommentId, STRING_AGG(ds.Id, ',') AS DrugSubstanceIds, ds.DrugTypeId, STRING_AGG(ds.Name, ', ') AS DrugSubstanceNames, dt.Name AS DrugTypeName FROM DrugSubstances ds
			JOIN CommentDrugSubstances cds ON cds.DrugSubstanceId = ds.Id
			LEFT JOIN DrugTypes AS dt ON ds.DrugTypeId = dt.Id
			GROUP BY cds.CommentId, ds.DrugTypeId, dt.Name) AS gds
		GROUP BY gds.CommentId) gcds
	JOIN 
		(SELECT cra.CommentId, '[' + STRING_AGG(cra.RouteOfAdministrationId, ',')  + ']' AS RouteOfAdministrationIds, 
				STRING_AGG(ra.Name, ', ') AS RouteOfAdministrationNames FROM RouteOfAdministrations ra 
				JOIN CommentRoutesOfAdministration AS cra ON ra.Id = cra.RouteOfAdministrationId
			GROUP BY cra.CommentId) AS gra ON gra.CommentId = gcds.CommentId
	RIGHT JOIN 
		(SELECT ct.CommentId, '[' + STRING_AGG(ct.TagId, ',')  + ']' AS TagIds, STRING_AGG(t.Name, ', ') AS TagNames FROM Tags as t
		JOIN CommentTags AS ct ON t.Id = ct.TagId
			GROUP BY ct.CommentId) AS ct ON ct.CommentId = gcds.CommentId) AS dsra
RIGHT JOIN Comments AS c ON c.Id = dsra.CommentId
LEFT JOIN ProductExtensions pe on pe.Id = c.ProductExtensionId
LEFT JOIN DrugProducts AS dp ON pe.ProductId = dp.Id
LEFT JOIN DosageForms AS df ON pe.DosageFormId = df.Id
JOIN (
	SELECT 
		comm.SubmissionTypeId,
		comm.Id AS CommunicationId,
		comm.DateOfCommunication,
		comm.IsCompleted,
		comm.CountryId AS CountryId,
		comm.Subject,
		'[' + STRING_AGG(a.Id, ',') + ']' AS ApplicationIds,
		'[' + STRING_AGG(sub.Id, ',') + ']' AS SubmissionIds,
		'["' + STRING_AGG(a.Number, '", "') + '"]' AS ApplicationNumbers,
		'["' + STRING_AGG(sub.Number, '", "') + '"]' AS SubmissionNumbers
	FROM Communications comm
	JOIN Applications AS a ON a.CommunicationId = comm.Id
	LEFT JOIN Submissions AS sub ON sub.ApplicationId = a.Id
	GROUP BY comm.SubmissionTypeId, comm.Id, comm.DateOfCommunication, comm.IsCompleted, comm.CountryId, comm.Subject)
	AS CommAppSub ON CommAppSub.CommunicationId = c.CommunicationId
JOIN Countries AS country ON CommAppSub.CountryId = country.Id
JOIN SubmissionTypes AS st ON st.Id = CommAppSub.SubmissionTypeId