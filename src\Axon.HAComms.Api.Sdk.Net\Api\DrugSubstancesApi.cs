/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Api.Sdk.Net.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDrugSubstancesApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>CreateDrugSubstanceCommandResponse</returns>
        CreateDrugSubstanceCommandResponse CreateDrugSubstance(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateDrugSubstanceCommandResponse</returns>
        ApiResponse<CreateDrugSubstanceCommandResponse> CreateDrugSubstanceWithHttpInfo(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        void DeleteDrugSubstance(int id, string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DeleteDrugSubstanceWithHttpInfo(int id, string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>DrugSubstanceModelApiListResult</returns>
        DrugSubstanceModelApiListResult GetDrugSubstancesList(string tenant);

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of DrugSubstanceModelApiListResult</returns>
        ApiResponse<DrugSubstanceModelApiListResult> GetDrugSubstancesListWithHttpInfo(string tenant);
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>DrugSubstancePagedListModelApiPagedListResult</returns>
        DrugSubstancePagedListModelApiPagedListResult GetPagedDrugSubstancesList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of DrugSubstancePagedListModelApiPagedListResult</returns>
        ApiResponse<DrugSubstancePagedListModelApiPagedListResult> GetPagedDrugSubstancesListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?));
        /// <summary>
        /// 
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>UpdateDrugSubstanceCommandResponse</returns>
        UpdateDrugSubstanceCommandResponse UpdateDrugSubstance(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateDrugSubstanceCommandResponse</returns>
        ApiResponse<UpdateDrugSubstanceCommandResponse> UpdateDrugSubstanceWithHttpInfo(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDrugSubstancesApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateDrugSubstanceCommandResponse</returns>
        System.Threading.Tasks.Task<CreateDrugSubstanceCommandResponse> CreateDrugSubstanceAsync(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateDrugSubstanceCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CreateDrugSubstanceCommandResponse>> CreateDrugSubstanceWithHttpInfoAsync(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DeleteDrugSubstanceAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DeleteDrugSubstanceWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DrugSubstanceModelApiListResult</returns>
        System.Threading.Tasks.Task<DrugSubstanceModelApiListResult> GetDrugSubstancesListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DrugSubstanceModelApiListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<DrugSubstanceModelApiListResult>> GetDrugSubstancesListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DrugSubstancePagedListModelApiPagedListResult</returns>
        System.Threading.Tasks.Task<DrugSubstancePagedListModelApiPagedListResult> GetPagedDrugSubstancesListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DrugSubstancePagedListModelApiPagedListResult)</returns>
        System.Threading.Tasks.Task<ApiResponse<DrugSubstancePagedListModelApiPagedListResult>> GetPagedDrugSubstancesListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateDrugSubstanceCommandResponse</returns>
        System.Threading.Tasks.Task<UpdateDrugSubstanceCommandResponse> UpdateDrugSubstanceAsync(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// 
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateDrugSubstanceCommandResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UpdateDrugSubstanceCommandResponse>> UpdateDrugSubstanceWithHttpInfoAsync(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IDrugSubstancesApi : IDrugSubstancesApiSync, IDrugSubstancesApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class DrugSubstancesApi : IDisposable, IDrugSubstancesApi
    {
        private Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public DrugSubstancesApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public DrugSubstancesApi(string basePath)
        {
            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public DrugSubstancesApi(Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DrugSubstancesApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DrugSubstancesApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                new Axon.HAComms.Api.Sdk.Net.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public DrugSubstancesApi(HttpClient client, Axon.HAComms.Api.Sdk.Net.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Axon.HAComms.Api.Sdk.Net.Client.Configuration.MergeConfigurations(
                Axon.HAComms.Api.Sdk.Net.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Axon.HAComms.Api.Sdk.Net.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DrugSubstancesApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public DrugSubstancesApi(Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient client, Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient asyncClient, Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Axon.HAComms.Api.Sdk.Net.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Axon.HAComms.Api.Sdk.Net.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Axon.HAComms.Api.Sdk.Net.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>CreateDrugSubstanceCommandResponse</returns>
        public CreateDrugSubstanceCommandResponse CreateDrugSubstance(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDrugSubstanceCommandResponse> localVarResponse = CreateDrugSubstanceWithHttpInfo(tenant, createDrugSubstanceCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of CreateDrugSubstanceCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDrugSubstanceCommandResponse> CreateDrugSubstanceWithHttpInfo(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->CreateDrugSubstance");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createDrugSubstanceCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CreateDrugSubstanceCommandResponse>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateDrugSubstanceCommandResponse</returns>
        public async System.Threading.Tasks.Task<CreateDrugSubstanceCommandResponse> CreateDrugSubstanceAsync(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDrugSubstanceCommandResponse> localVarResponse = await CreateDrugSubstanceWithHttpInfoAsync(tenant, createDrugSubstanceCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="createDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateDrugSubstanceCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<CreateDrugSubstanceCommandResponse>> CreateDrugSubstanceWithHttpInfoAsync(string tenant, CreateDrugSubstanceCommandRequest? createDrugSubstanceCommandRequest = default(CreateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->CreateDrugSubstance");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = createDrugSubstanceCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CreateDrugSubstanceCommandResponse>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns></returns>
        public void DeleteDrugSubstance(int id, string tenant)
        {
            DeleteDrugSubstanceWithHttpInfo(id, tenant);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object> DeleteDrugSubstanceWithHttpInfo(int id, string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->DeleteDrugSubstance");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/{tenant}/v1/DrugSubstances/{id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DeleteDrugSubstanceAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            await DeleteDrugSubstanceWithHttpInfoAsync(id, tenant, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="id"></param>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<Object>> DeleteDrugSubstanceWithHttpInfoAsync(int id, string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->DeleteDrugSubstance");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("id", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(id)); // path parameter
            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/{tenant}/v1/DrugSubstances/{id}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>DrugSubstanceModelApiListResult</returns>
        public DrugSubstanceModelApiListResult GetDrugSubstancesList(string tenant)
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstanceModelApiListResult> localVarResponse = GetDrugSubstancesListWithHttpInfo(tenant);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <returns>ApiResponse of DrugSubstanceModelApiListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstanceModelApiListResult> GetDrugSubstancesListWithHttpInfo(string tenant)
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->GetDrugSubstancesList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<DrugSubstanceModelApiListResult>("/{tenant}/v1/DrugSubstances/all", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetDrugSubstancesList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DrugSubstanceModelApiListResult</returns>
        public async System.Threading.Tasks.Task<DrugSubstanceModelApiListResult> GetDrugSubstancesListAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstanceModelApiListResult> localVarResponse = await GetDrugSubstancesListWithHttpInfoAsync(tenant, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DrugSubstanceModelApiListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstanceModelApiListResult>> GetDrugSubstancesListWithHttpInfoAsync(string tenant, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->GetDrugSubstancesList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<DrugSubstanceModelApiListResult>("/{tenant}/v1/DrugSubstances/all", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetDrugSubstancesList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>DrugSubstancePagedListModelApiPagedListResult</returns>
        public DrugSubstancePagedListModelApiPagedListResult GetPagedDrugSubstancesList(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstancePagedListModelApiPagedListResult> localVarResponse = GetPagedDrugSubstancesListWithHttpInfo(tenant, filters, skip, take, order);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <returns>ApiResponse of DrugSubstancePagedListModelApiPagedListResult</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstancePagedListModelApiPagedListResult> GetPagedDrugSubstancesListWithHttpInfo(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->GetPagedDrugSubstancesList");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<DrugSubstancePagedListModelApiPagedListResult>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedDrugSubstancesList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DrugSubstancePagedListModelApiPagedListResult</returns>
        public async System.Threading.Tasks.Task<DrugSubstancePagedListModelApiPagedListResult> GetPagedDrugSubstancesListAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstancePagedListModelApiPagedListResult> localVarResponse = await GetPagedDrugSubstancesListWithHttpInfoAsync(tenant, filters, skip, take, order, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="filters"> (optional)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="take"> (optional, default to 20)</param>
        /// <param name="order"> (optional, default to &quot;&quot;)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DrugSubstancePagedListModelApiPagedListResult)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<DrugSubstancePagedListModelApiPagedListResult>> GetPagedDrugSubstancesListWithHttpInfoAsync(string tenant, List<string>? filters = default(List<string>?), int? skip = default(int?), int? take = default(int?), string? order = default(string?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->GetPagedDrugSubstancesList");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            if (filters != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("multi", "filters", filters));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }
            if (take != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "take", take));
            }
            if (order != null)
            {
                localVarRequestOptions.QueryParameters.Add(Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToMultiMap("", "order", order));
            }

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<DrugSubstancePagedListModelApiPagedListResult>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetPagedDrugSubstancesList", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>UpdateDrugSubstanceCommandResponse</returns>
        public UpdateDrugSubstanceCommandResponse UpdateDrugSubstance(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDrugSubstanceCommandResponse> localVarResponse = UpdateDrugSubstanceWithHttpInfo(tenant, updateDrugSubstanceCommandRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <returns>ApiResponse of UpdateDrugSubstanceCommandResponse</returns>
        public Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDrugSubstanceCommandResponse> UpdateDrugSubstanceWithHttpInfo(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->UpdateDrugSubstance");

            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json",
                "text/json",
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };

            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateDrugSubstanceCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<UpdateDrugSubstanceCommandResponse>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateDrugSubstanceCommandResponse</returns>
        public async System.Threading.Tasks.Task<UpdateDrugSubstanceCommandResponse> UpdateDrugSubstanceAsync(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDrugSubstanceCommandResponse> localVarResponse = await UpdateDrugSubstanceWithHttpInfoAsync(tenant, updateDrugSubstanceCommandRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <exception cref="Axon.HAComms.Api.Sdk.Net.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tenant"></param>
        /// <param name="updateDrugSubstanceCommandRequest"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateDrugSubstanceCommandResponse)</returns>
        public async System.Threading.Tasks.Task<Axon.HAComms.Api.Sdk.Net.Client.ApiResponse<UpdateDrugSubstanceCommandResponse>> UpdateDrugSubstanceWithHttpInfoAsync(string tenant, UpdateDrugSubstanceCommandRequest? updateDrugSubstanceCommandRequest = default(UpdateDrugSubstanceCommandRequest?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'tenant' is set
            if (tenant == null)
                throw new Axon.HAComms.Api.Sdk.Net.Client.ApiException(400, "Missing required parameter 'tenant' when calling DrugSubstancesApi->UpdateDrugSubstance");


            Axon.HAComms.Api.Sdk.Net.Client.RequestOptions localVarRequestOptions = new Axon.HAComms.Api.Sdk.Net.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json", 
                "text/json", 
                "application/*+json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/xml"
            };


            var localVarContentType = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("tenant", Axon.HAComms.Api.Sdk.Net.Client.ClientUtils.ParameterToString(tenant)); // path parameter
            localVarRequestOptions.Data = updateDrugSubstanceCommandRequest;

            // authentication (Bearer) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("Authorization")))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", this.Configuration.GetApiKeyWithPrefix("Authorization"));
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<UpdateDrugSubstanceCommandResponse>("/{tenant}/v1/DrugSubstances", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("UpdateDrugSubstance", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
