﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class ProductTypesRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<ProductTypesRepository> logger)
    : SqlServerRepository<ProductType>(context, tenant, logger), IProductTypesRepository
{
    public async Task<IEnumerable<ProductType>> GetItemsAsync()
    {
        return await context.Set<ProductType>()
            .AsNoTracking()
            .ToListAsync();
    }

    public IQueryable<ProductType> GetQueryableItems()
    {
        return context.Set<ProductType>().AsQueryable();
    }

    public async Task<ProductType[]> GetAllByIdsAsync(params int[] ids)
    {
        var items = await this.GetQueryableItems().Where(x => ids.Contains(x.Id)).ToArrayAsync();
        return items;
    }

    public async Task<ProductType?> GetItemByFilterAsync(Expression<Func<ProductType, bool>> filter)
    {
        return await context.Set<ProductType>().SingleOrDefaultAsync(filter);
    }
}
