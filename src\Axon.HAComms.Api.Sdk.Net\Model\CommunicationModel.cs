/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CommunicationModel
    /// </summary>
    [DataContract(Name = "CommunicationModel")]
    public partial class CommunicationModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CommunicationModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="subject">subject.</param>
        /// <param name="country">country.</param>
        /// <param name="dateOfCommunication">dateOfCommunication.</param>
        /// <param name="submissionType">submissionType.</param>
        /// <param name="applications">applications.</param>
        /// <param name="isCompleted">isCompleted.</param>
        /// <param name="lastUpdatedBy">lastUpdatedBy.</param>
        /// <param name="createdDate">createdDate.</param>
        /// <param name="lastUpdatedDate">lastUpdatedDate.</param>
        /// <param name="createdBy">createdBy.</param>
        /// <param name="generalGuidanceCommentsCount">generalGuidanceCommentsCount.</param>
        /// <param name="products">products.</param>
        public CommunicationModel(int id = default(int), string subject = default(string), CountryModel country = default(CountryModel), DateTime dateOfCommunication = default(DateTime), SubmissionTypeModel submissionType = default(SubmissionTypeModel), List<ApplicationModel> applications = default(List<ApplicationModel>), bool isCompleted = default(bool), string lastUpdatedBy = default(string), DateTime createdDate = default(DateTime), DateTime lastUpdatedDate = default(DateTime), string createdBy = default(string), int generalGuidanceCommentsCount = default(int), List<ProductDtoModel> products = default(List<ProductDtoModel>))
        {
            this.Id = id;
            this.Subject = subject;
            this.Country = country;
            this.DateOfCommunication = dateOfCommunication;
            this.SubmissionType = submissionType;
            this.Applications = applications;
            this.IsCompleted = isCompleted;
            this.LastUpdatedBy = lastUpdatedBy;
            this.CreatedDate = createdDate;
            this.LastUpdatedDate = lastUpdatedDate;
            this.CreatedBy = createdBy;
            this.GeneralGuidanceCommentsCount = generalGuidanceCommentsCount;
            this.Products = products;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Subject
        /// </summary>
        [DataMember(Name = "subject", EmitDefaultValue = true)]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or Sets Country
        /// </summary>
        [DataMember(Name = "country", EmitDefaultValue = false)]
        public CountryModel Country { get; set; }

        /// <summary>
        /// Gets or Sets DateOfCommunication
        /// </summary>
        [DataMember(Name = "dateOfCommunication", EmitDefaultValue = false)]
        public DateTime DateOfCommunication { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionType
        /// </summary>
        [DataMember(Name = "submissionType", EmitDefaultValue = false)]
        public SubmissionTypeModel SubmissionType { get; set; }

        /// <summary>
        /// Gets or Sets Applications
        /// </summary>
        [DataMember(Name = "applications", EmitDefaultValue = true)]
        public List<ApplicationModel> Applications { get; set; }

        /// <summary>
        /// Gets or Sets IsCompleted
        /// </summary>
        [DataMember(Name = "isCompleted", EmitDefaultValue = true)]
        public bool IsCompleted { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedBy
        /// </summary>
        [DataMember(Name = "lastUpdatedBy", EmitDefaultValue = true)]
        public string LastUpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets CreatedDate
        /// </summary>
        [DataMember(Name = "createdDate", EmitDefaultValue = false)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedDate
        /// </summary>
        [DataMember(Name = "lastUpdatedDate", EmitDefaultValue = false)]
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or Sets CreatedBy
        /// </summary>
        [DataMember(Name = "createdBy", EmitDefaultValue = true)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or Sets GeneralGuidanceCommentsCount
        /// </summary>
        [DataMember(Name = "generalGuidanceCommentsCount", EmitDefaultValue = false)]
        public int GeneralGuidanceCommentsCount { get; set; }

        /// <summary>
        /// Gets or Sets Products
        /// </summary>
        [DataMember(Name = "products", EmitDefaultValue = true)]
        public List<ProductDtoModel> Products { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CommunicationModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Subject: ").Append(Subject).Append("\n");
            sb.Append("  Country: ").Append(Country).Append("\n");
            sb.Append("  DateOfCommunication: ").Append(DateOfCommunication).Append("\n");
            sb.Append("  SubmissionType: ").Append(SubmissionType).Append("\n");
            sb.Append("  Applications: ").Append(Applications).Append("\n");
            sb.Append("  IsCompleted: ").Append(IsCompleted).Append("\n");
            sb.Append("  LastUpdatedBy: ").Append(LastUpdatedBy).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  LastUpdatedDate: ").Append(LastUpdatedDate).Append("\n");
            sb.Append("  CreatedBy: ").Append(CreatedBy).Append("\n");
            sb.Append("  GeneralGuidanceCommentsCount: ").Append(GeneralGuidanceCommentsCount).Append("\n");
            sb.Append("  Products: ").Append(Products).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
