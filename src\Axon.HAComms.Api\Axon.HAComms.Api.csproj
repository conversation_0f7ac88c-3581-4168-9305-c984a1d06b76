﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
		<UserSecretsId>5c6785fd-5bd8-41af-a4e4-d3e93c31a640</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<Company>Phlexglobal Ltd</Company>
		<DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
		<DockerfileContext>..\..</DockerfileContext>
		<DockerfileFile>.\Dockerfile.develop</DockerfileFile>
    <InvariantGlobalization>false</InvariantGlobalization>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.0.0" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="9.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.SystemWebAdapters" Version="1.3.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.3" />
		<PackageReference Include="Microsoft.OData.ModelBuilder" Version="2.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.0" />
		<PackageReference Include="NewRelic.Agent.Api" Version="10.34.0" />
		<PackageReference Include="NLog" Version="5.2.8" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
		<PackageReference Include="Phlex.Core.FunctionalExtensions" Version="23.0.0.101" />
		<PackageReference Include="Phlex.Core.Multitenancy" Version="23.0.0.135" />
		<PackageReference Include="Polly" Version="8.3.1" />
		<PackageReference Include="Scrutor" Version="4.2.2" />
		<PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.9.0" />
	</ItemGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\Axon.HAComms.Application\Axon.HAComms.Application.csproj" />
		<ProjectReference Include="..\Axon.HAComms.Infrastructure\Axon.HAComms.Infrastructure.csproj" />
	</ItemGroup>

</Project>
