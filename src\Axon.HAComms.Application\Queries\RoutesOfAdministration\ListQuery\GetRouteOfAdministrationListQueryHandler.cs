﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.RoutesOfAdministration.ListQuery;

internal class GetRouteOfAdministrationListQueryHandler(IRouteOfAdministrationRepository repo, IMapper mapper)
    : IRequestHandler<GetRouteOfAdministrationListQueryRequest, ApiListResult<RouteOfAdministrationModel>>
{
    public async Task<ApiListResult<RouteOfAdministrationModel>> Handle(GetRouteOfAdministrationListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync();
        return new ApiListResult<RouteOfAdministrationModel>(mapper.Map<List<RouteOfAdministrationModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
