﻿using MediatR;
using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Commands.DrugSubstances.Update
{
    public class UpdateDrugSubstanceCommandRequest : IRequest<UpdateDrugSubstanceCommandResponse>
    {
        public int Id { get; }

        public string Name { get; }
        [Required]
        public string Code { get; }
        public string Description { get; }

        public UpdateDrugSubstanceCommandRequest(int id, string name, string code, string description)
        {
            Id = id;
            Name = name;
            Code = code;
            Description = description;
        }
    }
}
