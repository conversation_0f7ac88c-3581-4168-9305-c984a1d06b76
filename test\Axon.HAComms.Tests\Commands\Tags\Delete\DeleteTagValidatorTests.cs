﻿using Axon.HAComms.Application.Commands.Tags.Delete;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Delete
{
    public class DeleteTagValidatorTests
    {
        private readonly DeleteTagCommandValidator sut;

        public DeleteTagValidatorTests()
        {
            sut = new DeleteTagCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var result = sut.TestValidate(new DeleteTagCommandRequest(0));
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var result = sut.TestValidate(new DeleteTagCommandRequest(1));
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
