﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Tags;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Tags.ListQuery;

internal class GetTagListQueryHandler(ITagRepository repo, IMapper mapper) : IRequestHandler<GetTagListQueryRequest, ApiListResult<TagModel>>
{
    public async Task<ApiListResult<TagModel>> Handle(GetTagListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await repo.GetItemsAsync();
        return new ApiListResult<TagModel>(mapper.Map<List<TagModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
