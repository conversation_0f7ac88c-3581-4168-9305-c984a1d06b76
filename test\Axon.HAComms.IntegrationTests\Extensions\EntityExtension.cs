﻿using Axon.HAComms.Domain.Entities.Base;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.IntegrationTests.Extensions;

public static class EntityExtensions
{
    public static void Clear<T>(this DbSet<T> dbSet) where T : class
    {
        dbSet.RemoveRange(dbSet);
    }

    public static async Task<T> GetRandomEntity<T>(this DbSet<T> dbSet) where T : BaseEntity
    {
        var random = new Random();
        var idList = await dbSet.Select(x => x.Id).ToListAsync();
        var randomIndex = random.Next(0, idList.Count - 1);
        var randomId = idList[randomIndex];
        return await dbSet.FirstAsync(x => x.Id == randomId);
    }

    public static async Task<List<T>> GetRandomEntities<T>(this DbSet<T> dbSet, int numberOfEntities) where T : BaseEntity
    {
        return await dbSet.OrderBy(r => Guid.NewGuid()).Take(numberOfEntities).ToListAsync();
    }

    public static async Task<List<T>> GetRandomEntities<T>(this IQueryable<T> list, int numberOfEntities) where T : BaseEntity
    {
        return await list.OrderBy(r => Guid.NewGuid()).Take(numberOfEntities).ToListAsync();
    }
}
