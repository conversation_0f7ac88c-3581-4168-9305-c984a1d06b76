DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

INSERT INTO [dbo].[Countries] (Name, Region, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
VALUES      
(N'Afghanistan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Albania', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Algeria', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Andorra', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Angola', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Antigua and Barbuda', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Argentina', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Armenia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Australia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Austria', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Azerbaijan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bahamas', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bahrain', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bangladesh', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Barbados', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Belarus', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Belgium', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Belize', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Benin', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bhutan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bolivia (Plurinational State of)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bosnia and Herzegovina', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Botswana', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Brazil', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Brunei Darussalam', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Bulgaria', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Burkina Faso', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Burundi', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cabo Verde', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cambodia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cameroo', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Canada', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Central African Republic', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Chad', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Chile', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'China', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Colombia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Comoros', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Congo', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cook Islands', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Costa Rica', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cote d''Ivoire', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Croatia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cuba', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Cyprus', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Czechia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Czechoslovakia (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Democratic People''s Republic of Korea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Democratic Republic of the Congo', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Denmark', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Djibouti', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Dominica', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Dominican Republic', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Ecuador', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Egypt', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'El Salvador', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Equatorial Guinea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Eritrea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Estonia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Eswatini', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Ethiopia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Fiji', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Finland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'France', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Gabon', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Gambia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Georgia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Germany', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Germany, Democratic Republic (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Germany, Federal Republic (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Germany, West Berlin (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Ghana', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Greece', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Greenland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Grenada', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Guadeloupe (until 2003)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Guatemala', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Guinea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Guinea-Bissau', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Guyana', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Haiti', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Honduras', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Hungary', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Iceland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'India', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'India (until 1975)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Indonesia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Iran (Islamic Republic of)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Iraq', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Ireland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Israel', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Italy', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Jamaica', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Japan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Jordan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kazakhstan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kenya', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kiribati', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kiribati (until 1984)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kuwait', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Kyrgyzstan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Lao People''s Democratic Republic', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Latvia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Lebanon', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Lesotho', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Liberia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Libya', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Lithuania', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Luxembourg', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Madagascar', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Malawi', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Malaysia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Maldives', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mali', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Malta', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Marshall Islands', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mauritania', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mauritius', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mexico', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Micronesia (Federated States of)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Monaco', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mongolia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Montenegro', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Morocco', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Mozambique', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Myanmar', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Namibia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Nauru', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Nepal', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Netherlands', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Netherlands Antilles (from 1986 until 2010)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Netherlands Antilles (until 1985)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'New Zealand', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Nicaragua', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Niger', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Nigeria', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Niue', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'North Macedonia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Norway', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Oman', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Pakistan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Palau', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Panama', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Papua New Guinea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Paraguay', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Peru', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Philippines', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Poland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Portugal', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Puerto Rico', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Qatar', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Republic of Korea', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Republic of Moldova', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Romania', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Russian Federation', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Rwanda', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Saint Kitts and Nevis', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Saint Lucia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Saint Vincent and the Grenadines', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Samoa', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'San Marino', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sao Tome and Principe', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Saudi Arabia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Senegal', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Serbia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Serbia and Montenegro (2003-2006)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Seychelles', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sierra Leone', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Singapore', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Slovakia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Slovenia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Solomon Islands', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Somalia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'South Africa', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'South Sudan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'South Viet Nam (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Spain', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sri Lanka', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'State Union of Serbia and Montenegro (1993-2002)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sudan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sudan (until 2011)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Suriname', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Sweden', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Switzerland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Syrian Arab Republic', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Tajikistan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Thailand', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Timor-Leste', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Togo', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Tokelau', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Tonga', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Trinidad and Tobago', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Tunisia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Turkey', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Turkmenistan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Tuvalu', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Uganda', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Ukraine', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Union of Soviet Socialist Republics (former)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'United Arab Emirates', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'United Kingdom of Great Britain and Northern Ireland', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'United Republic of Tanzania', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'United States of America', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Uruguay', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Uzbekistan', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'European Union', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Vanuatu', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Venezuela (Bolivarian Republic of)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Viet Nam', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Yemen', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Yemen Arab Republic (until 1990)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Yugoslavia, Socialist Federal Republic (until 1992)', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Zambia', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted),
(N'Zimbabwe', N'', getdate(), @hacommsuser, getdate(), @hacommsuser, @deleted)