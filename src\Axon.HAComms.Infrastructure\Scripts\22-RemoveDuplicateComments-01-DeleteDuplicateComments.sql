﻿BEGIN TRY
BEGIN TRANSACTION    -- Start the transaction

	--Start iteration
	DECLARE @MaxId int
	DECLARE @CommentProductExtensionId int

	DECLARE MY_CURSOR CURSOR 
	  LOCAL STATIC READ_ONLY FORWARD_ONLY
	FOR 
		SELECT agg.MaxCommId, cs.Value CommentProductExtensionId
		FROM (SELECT c.Description,
					 pe.ProductId, 
					 string_agg(cpe.Id, ',') CommentProductExtensionIds, 
					 MAX(c.Id) MaxCommId
			FROM Comments c
			JOIN CommentProductExtension cpe ON cpe.CommentId = c.Id
			JOIN ProductExtensions pe ON pe.Id = cpe.ProductExtensionId
			GROUP BY c.CommunicationId, pe.ProductId, c.Description
			HAVING COUNT(c.CommunicationId) > 1) AS agg
		cross apply STRING_SPLIT (agg.CommentProductExtensionIds, ',') cs
		order by agg.MaxCommId

	OPEN MY_CURSOR
	FETCH NEXT FROM MY_CURSOR INTO @MaxId, @CommentProductExtensionId
	WHILE @@FETCH_STATUS = 0
	BEGIN 
		 UPDATE CommentProductExtension 
		 SET CommentId = @MaxId
		 WHERE Id = @CommentProductExtensionId

		PRINT 'MaxId: ' + CAST(@MaxId AS VARCHAR) + ' CommentProductExtensionId: ' + CAST(@CommentProductExtensionId AS VARCHAR)
		FETCH NEXT FROM MY_CURSOR INTO @MaxId, @CommentProductExtensionId
	END
	CLOSE MY_CURSOR
	DEALLOCATE MY_CURSOR
	-- stop iteration

	DELETE FROM Comments WHERE Id in 
		(SELECT c.Id FROM Comments c
		LEFT JOIN CommentProductExtension cpe ON cpe.CommentId = c.Id
		WHERE cpe.Id IS NULL AND c.IsGeneralGuidance = 0)
COMMIT
END TRY
BEGIN CATCH
-- Whoops, there was an error
IF @@TRANCOUNT > 0
 ROLLBACK

-- Raise an error with the details of the exception
DECLARE @ErrMsg nvarchar(4000), @ErrSeverity int
SELECT @ErrMsg = ERROR_MESSAGE(),
      @ErrSeverity = ERROR_SEVERITY()

RAISERROR(@ErrMsg, @ErrSeverity, 1)
END CATCH
