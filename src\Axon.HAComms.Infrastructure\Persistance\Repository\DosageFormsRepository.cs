﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class DosageFormsRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<DosageFormsRepository> logger)
    : SqlServerRepository<DosageForm>(context, tenant, logger), IDosageFormsRepository
{
    public async Task<IEnumerable<DosageForm>> GetItemsAsync()
    {
        return await context.Set<DosageForm>()
            .AsNoTracking()
            .ToListAsync();
    }

    public IQueryable<DosageForm> GetQueryableItems()
    {
        return context.Set<DosageForm>().Include(x => x.ProductExtensions).AsQueryable();
    }

    public async Task<DosageForm[]> GetAllByIdsAsync(params int[] ids)
    {
        var items = await context.Set<DosageForm>().Where(x => ids.Contains(x.Id)).ToArrayAsync();
        return items;
    }

    public async Task<DosageForm> GetItemAsync(int id)
    {
        var entity = await context.Set<DosageForm>().SingleOrDefaultAsync(d => d.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(DosageForm), id);
    }

    public async Task<bool> ExistsAsync(Expression<Func<DosageForm, bool>> filter)
    {
        return await context.Set<DosageForm>().AsNoTracking().AnyAsync(filter);
    }
}
