﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class SubmissionTypesRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<SubmissionTypesRepository> logger)
    : SqlServerRepository<SubmissionType>(context, tenant, logger), ISubmissionTypesRepository
{
    public async Task<IEnumerable<SubmissionType>> GetItemsAsync()
    {
        return await context
            .Set<SubmissionType>()
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<bool> ExistsAsync(Expression<Func<SubmissionType, bool>> filter)
    {
        return await context.Set<SubmissionType>().AsNoTracking().AnyAsync(filter);
    }
}
