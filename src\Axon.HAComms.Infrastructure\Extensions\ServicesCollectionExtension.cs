﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.Infrastructure.Persistance.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Axon.HAComms.Infrastructure.Extensions;

public static class ServicesCollectionExtension
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<HACommsContext>(
            options =>
                options.UseSqlServer(
                    configuration.GetConnectionString("default")));

        services
            .AddScoped<IDrugSubstancesRepository, DrugSubstancesRepository>()
            .AddScoped<IProductsRepository, ProductsRepository>()
            .AddScoped<ICommentsRepository, CommentsRepository>()
            .AddScoped<ICommunicationsRepository, CommunicationsRepository>()
            .AddScoped<IRouteOfAdministrationRepository, RouteOfAdministrationRepository>()
            .AddScoped<IDosageFormsRepository, DosageFormsRepository>()
            .AddScoped<ISubmissionTypesRepository, SubmissionTypesRepository>()
            .AddScoped<ICountriesRepository, CountriesRepository>()
            .AddScoped<ITagRepository, TagRepository>()
            .AddScoped<IProductExtensionsRepository, ProductExtensionsRepository>()
            .AddScoped<IApplicationsRepository, ApplicationsRepository>()
            .AddScoped<ISubmissionsRepository, SubmissionsRepository>()
            .AddScoped<IProductTypesRepository, ProductTypesRepository>()
            .AddScoped<ICommunicationService, CommunicationService>();
        return services;
    }
}
