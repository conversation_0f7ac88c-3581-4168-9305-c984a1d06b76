﻿using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Products.Create;

[UsedImplicitly]
public class CreateProductCommandValidator : AbstractValidator<CreateProductCommandRequest>
{
    public CreateProductCommandValidator(IProductsRepository productsRepo, IProductExtensionsRepository productExtensionsRepo)
    {
        ClassLevelCascadeMode = CascadeMode.Stop;

        this.AddProductRequestValidation();

        RuleFor(x => x)
            .Must(request =>
            {
                return !productsRepo.ExistsAsync(x => string.Equals(x.Name, request.Name)).GetAwaiter().GetResult();
            })
            .WithName("NameAlreadyExists")
            .WithMessage(x => $"Entity with Name = ({x.Name}) already exists.");
    }
}
