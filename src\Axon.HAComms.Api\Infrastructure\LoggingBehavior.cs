﻿using MediatR;

namespace Axon.HAComms.Api.Infrastructure
{
    public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
    {
        private readonly ILogger<LoggingBehavior<TRequest, TResponse>> logger;

        public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
        {
            this.logger = logger;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            logger.LogInformation("Execution of {RequestName} started, details: {request}", typeof(TRequest).Name, request);
            var response = await next();
            logger.LogInformation("Execution of {RequestType} completed", typeof(TResponse).Name);
            return response;
        }
    }
}