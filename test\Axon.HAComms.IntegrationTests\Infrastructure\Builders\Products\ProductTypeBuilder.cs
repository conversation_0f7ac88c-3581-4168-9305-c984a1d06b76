using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;

public class ProductTypeBuilder
{
    private string name = Fake.Product.Name;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static ProductTypeBuilder Default() => new();

    public ProductType Build()
    {
        var result = new ProductType()
        {
            Name = name,
            Tenant = tenant
        };
        return result;
    }

    public ProductTypeBuilder WithName(string productName)
    {
        name = productName;
        return this;
    }

    public ProductTypeBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
