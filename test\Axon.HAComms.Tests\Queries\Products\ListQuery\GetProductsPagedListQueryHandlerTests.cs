﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Products.PagedListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using Axon.HAComms.Tests.Builders.ProductExtensions;
using Axon.HAComms.Tests.Builders.Products;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Products.ListQuery;

public class GetProductsPagedListQueryHandlerTests
{
    private readonly GetProductsPagedListQueryHandler handler;
    private readonly IProductExtensionsRepository productExtensionsRepo;

    public GetProductsPagedListQueryHandlerTests()
    {
        productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new DrugSubstancesMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
            cfg.AddProfile(new DosageFormsMappingProfile());
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetProductsPagedListQueryHandler(productExtensionsRepo, mapper);
    }

    [Fact]
    public async Task Handle_ValidPaginationRequest_ReturnsCorrectPagedItems()
    {
        //Arrange
        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(101);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([], 100, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(1);
    }

    [Fact]
    public async Task Handle_ValidFilterRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var name = Fake.Product.Name;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var product = new ProductsBuilder().WithName(name).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            true,
            [routeOfAdministration],
            product,
            Fake.ProductExtension.Id);
         
        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([$"name=>{name}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Name != null && x.Name.Equals(name));
    }

    [Fact]
    public async Task Handle_ValidFilterIsActiveRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var isActive = true;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var product = new ProductsBuilder().WithName(Fake.Product.Name).WithIsActive(isActive).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            true,
            [routeOfAdministration],
            product,
            Fake.ProductExtension.Id);

        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([$"isactive=>{isActive}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.IsActive);
    }

    [Fact]
    public async Task Handle_ValidFilterSubstanceRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var isActive = true;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var product = new ProductsBuilder().WithName(Fake.Product.Name).WithIsActive(isActive).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            true,
            [routeOfAdministration],
            product,
            Fake.ProductExtension.Id);

        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([$"substances=>{drugSubstances[0].Code}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.Substances != null && x.Substances.Select(d => d.Name).Contains(drugSubstances[0].Name));
        result.Data.Should().Contain(x => x.Substances != null && x.Substances.Select(d => d.Code).Contains(drugSubstances[0].Code));
    }

    [Fact]
    public async Task Handle_ValidFilterProductCodeRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var productCode = Fake.ProductExtension.PCID;
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var product = new ProductsBuilder().WithName(Fake.Product.Name).WithIsActive(true).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            productCode,
            dosageForm,
            true,
            [routeOfAdministration],
            product,
            Fake.ProductExtension.Id);

        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();

        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([$"productcode=>{productCode}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.ProductExtensions != null && x.ProductExtensions.Select(e => e.PCID).Contains(productCode));
    }

    [Fact]
    public async Task Handle_ValidFilterDosageFormRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var product = new ProductsBuilder().WithName(Fake.Product.Name).WithIsActive(true).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            true,
            [routeOfAdministration],
            product,
            Fake.ProductExtension.Id);

        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();

        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([$"dosageform=>{dosageForm.Id}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.ProductExtensions != null && x.ProductExtensions.Select(e => e.DosageFormName).Contains(dosageForm.Name));
    }

    [Fact]
    public async Task Handle_ValidFilterRouteOfAdministrationRequest_ReturnsCorrectFilteredItem()
    {
        //Arrange
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var dosageForm = new DosageFormBuilder().Build();
        var routesOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(2);
        var product = new ProductsBuilder().WithName(Fake.Product.Name).WithIsActive(true).WithDrugSubstances(drugSubstances).Build();
        var productExtension = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            true,
            routesOfAdministration.ToList(),
            product,
            Fake.ProductExtension.Id);

        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(24);
        productExtensions.Add(productExtension);
        var mock = productExtensions.BuildMock();

        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock); 

        var request = new GetProductsPagedListQueryRequest([$"routeofadministration=>{routesOfAdministration[0].Id}"], 0, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().Contain(x => x.ProductExtensions != null && x.ProductExtensions.SelectMany(e => e.RouteOfAdministrations!.Select(r => r.Name)).Contains(routesOfAdministration[0].Name));
    }


    [Fact]
    public async Task Handle_PassInvalidSkip_ReturnsFirstPage()
    {
        //Arrange
        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(101);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([], -10, 10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(10);
    }

    [Fact]
    public async Task Handle_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        var productExtensions = TestEntitiesGenerator<ProductExtension, ProductExtensionBuilder>.Generate(50);
        var mock = productExtensions.BuildMock();
        productExtensionsRepo.GetQueryableItemsWithIncludes().Returns(mock);

        var request = new GetProductsPagedListQueryRequest([], 10, -10, null);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().NotBeEmpty();
        result.Data.Should().HaveCount(40);
    }
}
