using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;

public static class DosageFormsTestEntitiesBuilder
{
    public static async Task<List<DosageForm>> Build(HACommsContext dbContext, int entries = 100)
    {
        var list = new List<DosageForm>();

        for (var i = 0; i < entries; i++)
        {
            list.Add(CreateDosageFormsBuilder.Default().Build());
        }

        dbContext.DosageForms.AddRange(list);
        await dbContext.SaveChangesAsync();

        return list;
    }
}
