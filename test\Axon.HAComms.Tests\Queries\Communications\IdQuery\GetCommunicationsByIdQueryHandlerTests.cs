﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.Communications.IdQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Communications.IdQuery;

public class GetCommunicationByIdQueryHandlerTests
{
    private readonly GetCommunicationByIdQueryHandler handler;
    private readonly ICommunicationsRepository communicationsRepo;
    private readonly ICommentsRepository commentsRepo;

    public GetCommunicationByIdQueryHandlerTests()
    {
        communicationsRepo = Substitute.For<ICommunicationsRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommunicationsMappingProfile());
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new DrugSubstancesMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
            cfg.AddProfile(new DosageFormsMappingProfile());
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
            cfg.AddProfile(new CommentsMappingProfile());
            cfg.AddProfile(new CountryMappingProfile());
            cfg.AddProfile(new SubmissionTypeMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetCommunicationByIdQueryHandler(communicationsRepo, commentsRepo, mapper);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsItem()
    {
        // Arrange
        var communication = new CommunicationsBuilder().Build();
        var communicationId = Fake.Communication.Id;
        var comment1 = new Comment()
        {
            CommunicationId = communicationId,
            ProductExtensions = new List<ProductExtension>() { new ProductExtension() { ProductId = Fake.Product.Id } },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };
        var comment2 = new Comment()
        {
            CommunicationId = communicationId,
            ProductExtensions = new List<ProductExtension>() { new ProductExtension() { ProductId = Fake.Product.Id } },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };
        var comments = new List<Comment> { comment1, comment2 };
        communicationsRepo.GetItemAsync(communicationId).Returns(communication);
        commentsRepo.GetFilteredComments(filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(comments.BuildMock());
        var request = new GetCommunicationByIdQueryRequest(communicationId);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
    }
}
