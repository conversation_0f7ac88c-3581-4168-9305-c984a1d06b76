﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.RoutesOfAdministration;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetRouteOfAdministrationListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly RouteOfAdministrationApi api = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetRouteOfAdministrationList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentRouteOfAdministrationCount = dbContext.RouteOfAdministrations.Count();
        var routeOfAdministration1 = CreateRoutesOfAdministrationBuilder.Default().WithName(Fake.RouteOfAdministration.Name).Build();
        var routeOfAdministration2 = CreateRoutesOfAdministrationBuilder.Default().WithName(Fake.RouteOfAdministration.Name).Build();
        var routeOfAdministration3 = CreateRoutesOfAdministrationBuilder.Default().WithName(Fake.RouteOfAdministration.Name).Build();
        await dbContext.RouteOfAdministrations.AddRangeAsync(routeOfAdministration1, routeOfAdministration2, routeOfAdministration3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await api.GetRouteOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentRouteOfAdministrationCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(routeOfAdministration1.Name);
        responseNames.Should().Contain(routeOfAdministration2.Name);
        responseNames.Should().Contain(routeOfAdministration3.Name);


        dbContext.RouteOfAdministrations.RemoveRange(routeOfAdministration1, routeOfAdministration2, routeOfAdministration3);
        await dbContext.SaveChangesAsync();
    }

    [Fact]
    public async Task GetPagedRouteOfAdministrationList_ValidRequest_ReturnsOk()
    {
        //Arrange
        var currentRouteOfAdministrationCount = dbContext.RouteOfAdministrations.Count();
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 56);

        //Act
        var responseObj = await api.GetPagedRoutesOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT, null, currentRouteOfAdministrationCount + 50, 10);

        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedRouteOfAdministrationList_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 77);
        var route = await dbContext.RouteOfAdministrations.GetRandomEntity();

        //Act
        var responseObj = await api.GetPagedRoutesOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT, new List<string> { $"name=>{route.Name}" }, 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Name.Equals(route.Name));
    }

    [Fact]
    public async Task GetPagedRouteOfAdministrationList_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 25);

        //Act
        var responseObj = await api.GetPagedRoutesOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT,
            null, 0, 10, "name=>desc");

        //Assert
        responseObj.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task GetPagedRouteOfAdministrationList_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 55);

        //Act
        var responseObj = await api.GetPagedRoutesOfAdministrationListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15);

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.RouteOfAdministrations.Clear();

        await dbContext.SaveChangesAsync();
    }
}
