﻿using MediatR;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Application.Models.Products;

namespace Axon.HAComms.Application.Commands.Products.Update
{
    public class UpdateProductCommandRequest : ProductRequestModel, IRequest<UpdateProductCommandResponse>
    {
        public int Id { get; }

        public UpdateProductCommandRequest(
            int id, 
            string name,
            bool isActive, 
            ICollection<ProductExtensionModel> productExtensions,
            List<int> drugSubstanceIds,
            List<int> productTypeIds,
            string notAssigned = default!) : base(name, isActive, productExtensions, drugSubstanceIds, productTypeIds, notAssigned)
        {
            Id = id;
        }
    }
}
