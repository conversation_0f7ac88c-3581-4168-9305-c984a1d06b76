﻿using FluentValidation;
using JetBrains.Annotations;
namespace Axon.HAComms.Application.Queries.Comments.CommentsPagedList
{
    [UsedImplicitly]
    internal class GetCommentsPagedListQueryValidator: AbstractValidator<GetCommentsPagedListQueryRequest>
    {
        public GetCommentsPagedListQueryValidator()
        {
            RuleFor(x => x.CommunicationId)
                .NotEmpty()
                .GreaterThan(0);
        }
    }
}
