﻿using Axon.HAComms.Domain.Constants;
using MediatR;

namespace Axon.HAComms.Application.Queries.Audit.GetEventConstantsQuery;

internal sealed class GetEventConstantsQueryHandler : IRequestHandler<GetEventConstantsQueryRequest, GetEventConstantsQueryResponse>
{
    public Task<GetEventConstantsQueryResponse> Handle(GetEventConstantsQueryRequest request, CancellationToken cancellationToken)
    {
        var response = new GetEventConstantsQueryResponse(AuditEventCategory.All, AuditEventType.All);

        return Task.FromResult(response);
    }
}
