﻿using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.ProductExtensions;

public class ProductExtensionModelBuilder
{
    public static ProductExtensionModelBuilder Default() => new();

    private int productId = Fake.Product.Id;
    private int id = Fake.ProductExtension.Id;       
    private bool isActive = Fake.ProductExtension.IsActive;
    private string pcid = Fake.ProductExtension.PCID;
    private int dosageFormId = Fake.DosageForm.Id;
    private IList<int> routeOfAdministrationIds;

    public ProductExtensionModelBuilder()
    {
        routeOfAdministrationIds = new List<int>();
    }

    public ProductExtensionModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public ProductExtensionModelBuilder WithIsActive(bool isActive)
    {
        this.isActive = isActive;
        return this;
    }

    public ProductExtensionModelBuilder WithPcid(string pcid)
    {
        this.pcid = pcid;
        return this;
    }

    public ProductExtensionModelBuilder WithDosageFormId(int dosageFormId)
    {
        this.dosageFormId = dosageFormId;
        return this;
    }

    public ProductExtensionModelBuilder WithRouteOfAdministrationIds(List<int> routeOfAdministrationIds)
    {
        this.routeOfAdministrationIds = routeOfAdministrationIds;
        return this;
    }

    public ProductExtensionModel Build()
    {
        var model = new ProductExtensionModel()
        {
            Id = id,
            ProductId = productId,
            PCID = pcid,
            DosageFormId = dosageFormId,
            RouteOfAdministrationIds = routeOfAdministrationIds,
            IsActive = isActive
        };
        return model;
    }
}
