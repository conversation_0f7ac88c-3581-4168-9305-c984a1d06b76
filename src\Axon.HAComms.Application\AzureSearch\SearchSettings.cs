﻿using Axon.HAComms.Application.AzureSearch;

namespace Axon.HAComms.Application.Helpers
{
    public class SearchSettings
    {
        public bool IsEnabled { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string IndexName { get; set; } = string.Empty;
        public string IndexerName { get; set; } = string.Empty;
        public string DataSourceName { get; set; } = string.Empty;
        public string TableOrView { get; set; } = string.Empty;
        public required DbSettings ConnectionString { get; set; }
        public double Interval { get; set; }
        public string HighWatermarkColumn { get; set; } = string.Empty;
        public string SoftDeleteColumn { get; set; } = string.Empty;
        public string SoftDeleteMarker { get; set; } = string.Empty;
    }
}
