using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Tags.Delete;

internal class DeleteTagCommandHandler(ITagRepository repoTags,
    ICommentsRepository commentsRepo,
    ILogger<DeleteTagCommandHandler> logger,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider) : IRequestHandler<DeleteTagCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteTagCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoTags.GetItemAsync(request.Id);

        if (await commentsRepo.ExistsAsync(x => x.CommentTags.Any(s => s.TagId == entity.Id)))
        {
            logger.LogWarning("Tag {TagId} has associated comments and cannot be deleted.", entity.Id);
            throw new AssociationExistsException("Tag", entity.Id);
        }

        repoTags.DeleteItem(entity);
        await repoTags.SaveChangesAsync(userProvider);

        auditService.Log(correlationIdProvider.Provide(), clientDetailsProvider.Provide(), AuditEventType.TAG_DELETED, AuditEventCategory.TAGS,
            AuditEventDescription.TAG_DELETE, entity);

        return Result.Success();
    }
}
