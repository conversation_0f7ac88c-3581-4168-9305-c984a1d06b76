﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Common.Builders;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Products;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetProductCodesIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductsApi productsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetProductCodesDistinctList_GetRequest_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productExtensionModel3 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(testDosageForms[2].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[2].Id])
            .Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstances.Select(d => d.Id).ToList())
            .WithProductTypes(productTypes.Select(p => p.Id).ToList())
            .WithProductExtensions(productExtensionModel1, productExtensionModel2, productExtensionModel3).Build();

        //Act
        var productResponse = await productsApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        Assert.NotNull(productResponse);

        //Act
        var responseObj = await productsApi.GetAllProductCodesAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(3);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
