﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;

public class SubmissionBuilder
{
    private string number = Fake.Submission.Number;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static SubmissionBuilder Default() => new();

    public Submission Build()
    {
        return new Submission
        {
            Number = number,
            Tenant = tenant
        };
    }

    public SubmissionBuilder WithNumber(string number)
    {
        this.number = number;
        return this;
    }

    public SubmissionBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
