﻿GO

-- To be executed on PROD

UPDATE [dbo].[Communications]
   SET [CountryId] = 46 WHERE [CountryId] = 242 -- From CZECH REPUBLIC to Czechia
UPDATE [dbo].[Communications]
   SET [CountryId] = 88 WHERE [CountryId] = 256 -- From IRAN to Iran (Islamic Republic of Iran)
UPDATE [dbo].[Communications]
   SET [CountryId] = 152 WHERE [CountryId] = 281 -- From KOREA to Republic of Korea
UPDATE [dbo].[Communications]
   SET [CountryId] = 138 WHERE [CountryId] = 283 -- From MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF to North Macedonia
UPDATE [dbo].[Communications]
   SET [CountryId] = 201 WHERE [CountryId] = 217 -- From UNITED KINGDOM to United Kingdom of Great Britain and Northern Ireland
UPDATE [dbo].[Communications]
   SET [CountryId] = 203 WHERE [CountryId] = 218 -- From UNITED STATES to United States of America

DELETE FROM [dbo].[Countries] WHERE Id in (227 -- AMERICAN SAMOA
,228 -- ANGUILLA
,229 -- ANTARCTICA
,231 -- BERMUDA
,232 -- BOLIVIA
,233 -- BOUVET ISLAND
,234 -- BRITISH INDIAN OCEAN TERRITORY
,235 -- BRUNAI DARUSSALAM
,31 -- Cameroo
,237 -- CAPE VERDE
,238 -- CAYMAN ISLANDS
,239 -- CHRISMAS ISLAND
,240 -- COCOS (KEELING) ISLANDS
,241 -- CONGO-DR
,242 -- CZECH REPUBLIC
,243 -- CZECHOSLOVAKIA
,47 -- Czechoslovakia (former)
,244 -- EAST TIMOR
,245 -- FALKLAND ISLANDS
,246 -- FAROE ISLANDS
,247 -- FRENCH GUIANA
,248 -- FRENCH POLYNESIA
,249 -- FRENCH SOUTHERN TERRITORIES
,250 -- GERMANY EAST
,69 -- Germany, Democratic Republic (former)
,70 -- Germany, Federal Republic (former)
,71 -- Germany, West Berlin (former)
,251 -- GIBRALTAR
,74 -- Greenland
,252 -- GUADELOUPE
,76 -- Guadeloupe (until 2003)
,253 -- GUAM
,254 -- HEARD ISLAND AND MCDONALD ISLANDS
,255 -- HOLY SEE
,86 -- India (until 1975)
,256 -- IRAN
,99 -- Kiribati (until 1984)
,281 -- KOREA
,257 -- KOREA-DR
,285 -- KOSOVO
,258 -- LAO-DR
,260 -- MACAO
,283 -- MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF
,261 -- MARTINIQUE
,262 -- MAYOTTE
,263 -- MICRONESIA
,279 -- MOLDOVA
,264 -- MONTSERRAT
,265 -- NETHERLANDS ANTILLES
,131 -- Netherlands Antilles (from 1986 until 2010)
,132 -- Netherlands Antilles (until 1985)
,266 -- NEW CALEDONIA
,267 -- NORFOLK ISLAND
,268 -- NORTHERN MARIANA ISLANDS
,270 -- PITCAIRN
,150 -- Puerto Rico
,271 -- REUNION
,272 -- SAINT HELENA
,273 -- SAINT PIERRE AND MIQUELON
,166 -- Serbia and Montenegro (2003-2006)
,274 -- SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS
,176 -- South Viet Nam (former)
,179 -- State Union of Serbia and Montenegro (1993-2002)
,181 -- Sudan (until 2011)
,275 -- SVALBARD AND JAN MAYEN
,276 -- SWAZILAND
,277 -- SYRIA
,284 -- TADZIKISTAN
,215 -- TANZANIA
,190 -- Tokelau
,216 -- TURKS AND CAICOS ISLANDS
,199 -- Union of Soviet Socialist Republics (former)
,217 -- UNITED KINGDOM
,218 -- UNITED STATES
,219 -- UNITED STATES MINOR OUTLYING ISLANDS
,208 -- Venezuela (Bolivarian Republic of)
,221 -- VIRGIN ISLANDS, BRITISH
,222 -- VIRGIN ISLANDS, US
,223 -- WALLIS AND FUTUNA
,224 -- WESTERN SAHARA --/
,211 -- Yemen Arab Republic (until 1990)
,225 -- YEMEN-DR
,226 -- YUGOSLAVIA
,212 -- Yugoslavia, Socialist Federal Republic (until 1992)
)

-- Update Names
UPDATE [dbo].[Countries]
   SET [Name] = 'Venezuela' WHERE [Id] = 220 -- From VENEZUELA to Venezuela
UPDATE [dbo].[Countries]
   SET [Name] = 'Aruba' WHERE [Id] = 230 -- From ARUBA to Aruba
UPDATE [dbo].[Countries]
   SET [Name] = 'Cameroon' WHERE [Id] = 236 -- From CAMEROON to Cameroon
UPDATE [dbo].[Countries]
   SET [Name] = 'Liechtenstein' WHERE [Id] = 259 -- From LIECHTENSTEIN to Liechtenstein
UPDATE [dbo].[Countries]
   SET [Name] = 'Bolivia (Plurinational State of Bolivia)' WHERE [Id] = 21 -- From Bolivia (Plurinational State of) to Bolivia (Plurinational State of Bolivia)
UPDATE [dbo].[Countries]
   SET [Name] = 'Türkiye' WHERE [Id] = 194 -- From Turkey to Türkiye
UPDATE [dbo].[Countries]
   SET [Name] = 'Iran (Islamic Republic of Iran)' WHERE [Id] = 88 -- From Iran (Islamic Republic of) to Iran (Islamic Republic of Iran)
UPDATE [dbo].[Countries]
   SET [Name] = 'Micronesia (Federated States of Micronesia)' WHERE [Id] = 120 -- From Micronesia (Federated States of) to Iran (Islamic Republic of Iran)
UPDATE [dbo].[Countries]
   SET [Name] = 'Vietnam' WHERE [Id] = 209 -- From Viet Nam to Vietnam
UPDATE [dbo].[Countries]
   SET [Name] = 'The Netherlands' WHERE [Id] = 130 -- From Netherlands to The Netherlands
UPDATE [dbo].[Countries]
   SET [Name] = 'Eurasian Economic Union (EAEU)' WHERE [Id] = 286 -- From EURASIAN ECONOMIC UNION (EAEU) to Eurasian Economic Union (EAEU)
UPDATE [dbo].[Countries]
   SET [Name] = 'Hong Kong' WHERE [Id] = 280 -- From HONG KONG to Hong Kong
UPDATE [dbo].[Countries]
   SET [Name] = 'Palestine' WHERE [Id] = 269 -- From PALESTIN to Palestine
UPDATE [dbo].[Countries]
   SET [Name] = 'Sint Maarten' WHERE [Id] = 278 -- From SINT MAARTEN to Sint Maarten
UPDATE [dbo].[Countries]
   SET [Name] = 'Taiwan' WHERE [Id] = 282 -- From TAIWAN to Taiwan

GO
