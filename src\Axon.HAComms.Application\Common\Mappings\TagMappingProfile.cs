﻿using AutoMapper;
using Axon.HAComms.Application.Commands.Tags.Create;
using Axon.HAComms.Application.Commands.Tags.Update;
using Axon.HAComms.Application.Models.Tags;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class TagMappingProfile : Profile
    {
        public TagMappingProfile()
        {
            CreateMap<Tag, TagModel>();
            CreateMap<TagModel, Tag>()
                .ForMember(dest => dest.Comments, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<CreateTagCommandRequest, Tag>()
                .ForMember(dest => dest.Comments, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<UpdateTagCommandRequest, Tag>()
                .ForMember(dest => dest.Comments, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.ExternalId, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
            CreateMap<Tag, UpdateTagCommandResponse>();
            CreateMap<Tag, CreateTagCommandResponse>();
            CreateMap<Tag, UpdateTagCommandRequest>();
        }
    }
}
