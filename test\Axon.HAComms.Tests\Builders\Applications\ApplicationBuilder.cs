﻿using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Builders.Submissions;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Applications
{
    public class ApplicationBuilder : IBuilder<Domain.Entities.Application>
    {
        private string number;
        private readonly ICollection<SubmissionBuilder> submissionsBuilder;

        public ApplicationBuilder()
        {
            number = Fake.Application.Number;
            submissionsBuilder = new List<SubmissionBuilder>();
        }

        public Domain.Entities.Application Build()
        {
            return new()
            {
                Number = number,
                Submissions = submissionsBuilder.Select(x => x.Build()).ToList(),
            };
        }

        public ApplicationBuilder WithNumber(string number)
        {
            this.number = number;
            return this;
        }
    }
}
