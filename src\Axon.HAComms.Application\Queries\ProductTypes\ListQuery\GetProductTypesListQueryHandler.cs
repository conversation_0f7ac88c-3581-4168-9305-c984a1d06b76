﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.ProductType;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.ProductTypes.ListQuery;

internal class GetProductTypesListQueryHandler(IProductTypesRepository productTypesRepo, IMapper mapper)
    : IRequestHandler<GetProductTypesListQueryRequest, ApiListResult<ProductTypeModel>>
{
    public async Task<ApiListResult<ProductTypeModel>> Handle(GetProductTypesListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await productTypesRepo.GetItemsAsync();
        return new ApiListResult<ProductTypeModel>(mapper.Map<List<ProductTypeModel>>(entities.OrderBy(x => x.Name).ToArray()));
    }
}
