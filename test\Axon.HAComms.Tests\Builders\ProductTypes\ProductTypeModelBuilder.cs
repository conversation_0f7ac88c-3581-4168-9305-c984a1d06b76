﻿using Axon.HAComms.Application.Models.ProductType;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.ProductTypes;

public class ProductTypeModelBuilder : IBuilder<ProductTypeModel>
{
    private int id = Fake.ProductType.Id;
    private string name = Fake.ProductType.Name;

    public static ProductTypeModelBuilder Default() => new();

    public ProductTypeModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public ProductTypeModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public ProductTypeModel Build()
    {
        return new(name)
        {
            Id = id,
            Name = name
        };
    }
}
