using Axon.HAComms.Application.Models.RoutesOfAdministration;

namespace Axon.HAComms.Application.Models.ProductExtensions;

public class ProductExtensionModel
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string PCID { get; set; } = string.Empty;
    public int DosageFormId { get; set; }
    public string? DosageFormName { get; set; }
    public IList<int> RouteOfAdministrationIds { get; set; } = [];
    public bool IsActive { get; set; }
    public ICollection<RouteOfAdministrationModel>? RouteOfAdministrations { get; set; } = null;
}
