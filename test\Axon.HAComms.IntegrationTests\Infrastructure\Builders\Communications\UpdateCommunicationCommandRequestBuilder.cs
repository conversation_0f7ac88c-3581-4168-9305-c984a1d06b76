﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications
{
    public class UpdateCommunicationCommandRequestBuilder
    {
        private int id;
        private string subject = Fake.Communication.Subject;
        private int submissionTypeId = Fake.SubmissionType.Id;
        private int countryId = Fake.Country.Id;
        private DateTime dateOfCommunication = DateTime.Now;
        private IList<ApplicationModel> applications = [];

        public static UpdateCommunicationCommandRequestBuilder Default() => new();

        public UpdateCommunicationCommandRequest Build()
        {
            return new UpdateCommunicationCommandRequest(
                subject,
                submissionTypeId,
                countryId,
                dateOfCommunication,
                [.. applications],
                id);
        }

        public UpdateCommunicationCommandRequestBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public UpdateCommunicationCommandRequestBuilder WithSubject(string subject)
        {
            this.subject = subject;
            return this;
        }

        public UpdateCommunicationCommandRequestBuilder WithSubmissionTypeId(int submissionTypeId)
        {
            this.submissionTypeId = submissionTypeId;
            return this;
        }

        public UpdateCommunicationCommandRequestBuilder WithCountryId(int countryId)
        {
            this.countryId = countryId;
            return this;
        }

        public UpdateCommunicationCommandRequestBuilder WithDateOfCommunication(DateTime dateOfCommunication)
        {
            this.dateOfCommunication = dateOfCommunication;
            return this;
        }

        public UpdateCommunicationCommandRequestBuilder WithApplications(params ApplicationModel[] applications)
        {
            this.applications = applications.ToList();
            return this;
        }
    }
}
