{"Logging": {"LogLevel": {"Default": "Information", "Grpc": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"default": "Data Source=(local);Initial Catalog=hacomms-test;Trusted_connection=true; Trust Server Certificate=true;Integrated Security=true;"}, "Audit": {"SqlServer": {"ConnectionString": "Server=.;Initial Catalog=hacomms-test;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Connection Timeout=30;Integrated Security=True", "Schema": "dbo", "TableName": "Audit", "IdColumnName": "Id", "JsonColumnName": "JsonData", "LastUpdatedDateColumnName": "LastUpdatedDate"}}}