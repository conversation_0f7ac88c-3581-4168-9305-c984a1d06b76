﻿using Autofac;
using Axon.Core.Shared.Audit.Settings;
using Axon.Core.Shared.Extensions;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.Infrastructure.Persistance.Repository;
using Phlex.Core.Multitenancy;

namespace Axon.HAComms.Api.Infrastructure.IoC;

/// <summary>
/// Register dependency injection types per tenant
/// </summary>
public static class MultitenantStartup
{
    public static void ConfigureServices(ITenant tenant, IConfiguration configuration, IHostEnvironment hostEnvironment, IServiceCollection services)
    {
        //Multitenant services registered here
        services.AddGrpcAuthorisationClientResources(null);
    }

    public static void ConfigureContainer(ITenant tenant, IConfiguration configuration, IHostEnvironment hostEnvironment, ContainerBuilder builder)
    {
        //Multitenant container types registered here
        builder.AddMultiTenantAuthorisationClient(configuration, tenant);

        var settings = configuration.GetSection("Audit:SqlServer").Get<SqlServerDbSettings>();
        builder.AddAxonAuditService(configuration, tenant, hostEnvironment, settings);

        builder
            .RegisterType<MultitenantHacommsDbContext>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<ApplicationsRepository>().As<IApplicationsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<CommentsRepository>().As<ICommentsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<CommunicationsRepository>().As<ICommunicationsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<CountriesRepository>().As<ICountriesRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<DosageFormsRepository>().As<IDosageFormsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<DrugSubstancesRepository>().As<IDrugSubstancesRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<ProductExtensionsRepository>().As<IProductExtensionsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<ProductsRepository>().As<IProductsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<ProductTypesRepository>().As<IProductTypesRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<RouteOfAdministrationRepository>().As<IRouteOfAdministrationRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<SubmissionsRepository>().As<ISubmissionsRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<SubmissionTypesRepository>().As<ISubmissionTypesRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));

        builder
            .RegisterType<TagRepository>().As<ITagRepository>()
            .WithParameter(new TypedParameter(typeof(ITenant), tenant));
    }
}
