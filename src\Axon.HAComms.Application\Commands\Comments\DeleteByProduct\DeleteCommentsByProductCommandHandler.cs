﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Comments.DeleteByProduct;

internal class DeleteCommentsByProductCommandHandler(ICommentsRepository commentsRepo,
    ILogger<DeleteCommentsByProductCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService)
    : IRequestHandler<DeleteCommentsByProductCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteCommentsByProductCommandRequest request, CancellationToken cancellationToken)
    {

#pragma warning disable CS8619, CS8620
        var communicationComments = await commentsRepo.GetFilteredComments(
                                                        include: x => x.Include(c => c.CommentProductExtensions)
                                                                .ThenInclude(c => c.ProductExtension),
                                                        filter: c => c.CommunicationId == request.CommunicationId)
                                                        .AsNoTracking()
                                                        .ToArrayAsync(cancellationToken: cancellationToken);
#pragma warning restore CS8619, CS8620

        // Check if there is at least one general guidance comment and this is the only product in the communication and the product matches the one in the request
        var generalComments = communicationComments.Where(c => c.IsGeneralGuidance).ToArray();
        var communicationProducts = communicationComments.SelectMany(c => c.CommentProductExtensions!.Select(p => p.ProductExtension).Select(p => p!.ProductId))
                                                         .Distinct()
                                                         .ToArray();
        if (generalComments.Length == 0 && communicationProducts.Length == 1 && communicationProducts[0] == request.ProductId)
        {
            throw new InvalidOperationException("Cannot delete the last product for a communication.");
        }

        var commentIdsToDelete = communicationComments.Where(c =>
                c.CommentProductExtensions != null &&
                c.CommentProductExtensions.Select(p => p.ProductExtension).Select(p => p!.ProductId).Any(p => p == request.ProductId))
            .Select(x => x.Id).ToArray();

        //Get Comments without sub-collections
        var commentsToDelete = await commentsRepo.GetFilteredComments(filter: c => commentIdsToDelete.Contains(c.Id))
                                                 .ToArrayAsync(cancellationToken: cancellationToken);


        var correlationId = correlationIdProvider.Provide();
        var clientDetails = clientDetailsProvider.Provide();

        foreach (var comment in commentsToDelete)
        {
            await auditService.LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS,
                                AuditEventDescription.COMMENT_DELETE, comment, async () => await FuncDeleteComment(comment));
        }

        logger.LogInformation("{Count} comments for communication with Id {Id} deleted successfully.", commentsToDelete.Length, request.CommunicationId);

        return Result.Success();

        async Task FuncDeleteComment(Comment comment)
        {
            comment.Delete();
            commentsRepo.UpdateItem(comment);
            await commentsRepo.SaveChangesAsync(userProvider);
            logger.LogInformation("Comment with Id {Id} deleted successfully.", comment.Id);
        }
    }
}
