﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductTypeConfiguration : BaseEntityConfiguration<ProductType>
{
    protected override void ConfigureEntity(EntityTypeBuilder<ProductType> builder)
    {
        builder.ToTable("ProductTypes");

        builder.HasMany(pt => pt.Products)
            .WithMany(p => p.ProductTypes)
            .UsingEntity<ProductProductTypes>(
                l => l.<PERSON>One(ppt => ppt.Product).WithMany(p => p.ProductProductTypes).HasForeignKey(roa => roa.ProductId),
                r => r.<PERSON>ne(ppt => ppt.ProductType).WithMany(x => x.ProductProductTypes).HasForeignKey(p => p.ProductTypeId),
                b => b.ToTable("ProductProductTypes"));
    }
}
