﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductTypeConfiguration : IEntityTypeConfiguration<ProductType>
{
    public void Configure(EntityTypeBuilder<ProductType> builder)
    {
        builder.ToTable("ProductTypes");

        builder.HasMany(pt => pt.Products)
            .WithMany(p => p.ProductTypes)
            .UsingEntity<ProductProductTypes>(
                l => l.HasOne(ppt => ppt.Product).WithMany(p => p.ProductProductTypes).HasForeignKey(roa => roa.ProductId),
                r => r.<PERSON>ne(ppt => ppt.ProductType).WithMany(x => x.ProductProductTypes).HasForeignKey(p => p.ProductTypeId),
                b => b.ToTable("ProductProductTypes"));

        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
