﻿using AutoMapper;
using Axon.HAComms.Application.Models.ProductType;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings;

public class ProductTypesMappingProfile : Profile
{
    public ProductTypesMappingProfile()
    {
        CreateMap<ProductType, ProductTypeModel>();
        CreateMap<ProductTypeModel, ProductType>()
            .ForMember(dest => dest.ProductProductTypes, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore())
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.Products, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore());
    }
}
