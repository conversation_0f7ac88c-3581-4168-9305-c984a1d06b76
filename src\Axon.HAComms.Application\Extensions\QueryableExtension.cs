﻿using System.Linq.Expressions;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Application.Extensions;

public static class QueryableExtension
{
    public static IQueryable<TEntity> FilterItems<TEntity>(this IQueryable<TEntity> entities,
        Expression<Func<TEntity, bool>>? expression,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy,
        int skip = 0,
        int take = 0) where TEntity : BaseEntity
    {
        if (expression != null)
        {
            entities = entities.Where(expression);
        }

        if (orderBy != null)
        {
            entities = orderBy(entities);
        }

        if (typeof(TEntity) == typeof(ProductExtension))
        {
            entities = entities.FilterProductExtensions(skip, take);
        }
        else
        {
            if (skip > 0)
            {
                entities = entities.Skip(skip);
            }

            if (take > 0)
            {
                entities = entities.Take(take);
            }
        }

        return entities;
    }

    private static IQueryable<TEntity> FilterProductExtensions<TEntity>(this IQueryable<TEntity> entities, int skip, int take) where TEntity : BaseEntity
    {
        var productExtensionsEntities = entities as IQueryable<ProductExtension>;
        if (productExtensionsEntities != null)
        {
            var productIds = productExtensionsEntities.GroupBy(d => d.Product.Id).OrderBy(g => g.Key).Select(g => g.Key);

            if (skip > 0)
            {
                productIds = productIds.Skip(skip);
            }
            if (take > 0)
            {
                productIds = productIds.Take(take);
            }

            entities = (IQueryable<TEntity>)productExtensionsEntities.Where(e => productIds.Contains(e.Product.Id));
        }

        return entities;
    }
}
