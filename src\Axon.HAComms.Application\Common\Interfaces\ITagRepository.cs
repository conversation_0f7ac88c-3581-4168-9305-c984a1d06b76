﻿using Axon.HAComms.Domain.Entities;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Common.Interfaces;

public interface ITagRepository : IRepository<Tag>
{
    Task<IEnumerable<Tag>> GetItemsAsync();

    Task<Tag> GetItemAsync(int id);

    Task<List<Tag>> GetAllByIdsAsync(params int[] ids);

    Task<bool> ExistsAsync(Expression<Func<Tag, bool>> filter);

    IQueryable<Tag> GetQueryableItems();
}
