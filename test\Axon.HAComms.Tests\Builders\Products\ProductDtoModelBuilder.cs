﻿using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Products;

public class ProductDtoModelBuilder : IBuilder<ProductDtoModel>
{
    private int id = Fake.Product.Id;
    private string name = Fake.Product.Name;

    public static ProductDtoModelBuilder Default() => new();

    public ProductDtoModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public ProductDtoModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public ProductDtoModel Build()
    {
        return new(id, name)
        {
            Id = id,
            Name = name
        };
    }
}
