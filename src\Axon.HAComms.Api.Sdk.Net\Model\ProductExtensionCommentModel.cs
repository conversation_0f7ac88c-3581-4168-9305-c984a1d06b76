/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// ProductExtensionCommentModel
    /// </summary>
    [DataContract(Name = "ProductExtensionCommentModel")]
    public partial class ProductExtensionCommentModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductExtensionCommentModel" /> class.
        /// </summary>
        /// <param name="productExtensionId">productExtensionId.</param>
        /// <param name="routeOfAdministrationIds">routeOfAdministrationIds.</param>
        public ProductExtensionCommentModel(int productExtensionId = default(int), List<int> routeOfAdministrationIds = default(List<int>))
        {
            this.ProductExtensionId = productExtensionId;
            this.RouteOfAdministrationIds = routeOfAdministrationIds;
        }

        /// <summary>
        /// Gets or Sets ProductExtensionId
        /// </summary>
        [DataMember(Name = "productExtensionId", EmitDefaultValue = false)]
        public int ProductExtensionId { get; set; }

        /// <summary>
        /// Gets or Sets RouteOfAdministrationIds
        /// </summary>
        [DataMember(Name = "routeOfAdministrationIds", EmitDefaultValue = true)]
        public List<int> RouteOfAdministrationIds { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ProductExtensionCommentModel {\n");
            sb.Append("  ProductExtensionId: ").Append(ProductExtensionId).Append("\n");
            sb.Append("  RouteOfAdministrationIds: ").Append(RouteOfAdministrationIds).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
