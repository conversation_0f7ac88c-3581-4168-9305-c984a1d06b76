﻿using System.Linq.Expressions;

namespace Axon.HAComms.Application.Extensions
{
    public static class ExpressionExtension
    {
        public static Expression<Func<T, bool>> AndAlso<T>(this Expression<Func<T, bool>>? left, Expression<Func<T, bool>> right)
        {
            if (left == null)
            {
                return right;
            }

            Expression<Func<T, bool>> combined = Expression.Lambda<Func<T, bool>>(
                Expression.AndAlso(
                    left.Body,
                    new ExpressionParameterVisitor(right.Parameters, left.Parameters).Visit(right.Body)
                    ), left.Parameters);

            return combined;
        }
    }

    public class ExpressionParameterVisitor : ExpressionVisitor
    {
        private Dictionary<ParameterExpression, ParameterExpression> ParameterReplacements { get; set; }

        public ExpressionParameterVisitor(IList<ParameterExpression> fromParameters, IList<ParameterExpression> toParameters)
        {
            ParameterReplacements = [];

            for (var i = 0; i != fromParameters.Count && i != toParameters.Count; i++)
            {
                ParameterReplacements.Add(fromParameters[i], toParameters[i]);
            }
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            if (ParameterReplacements.TryGetValue(node, out ParameterExpression? replacement))
            {
                node = replacement;
            }

            return base.VisitParameter(node);
        }
    }
}
