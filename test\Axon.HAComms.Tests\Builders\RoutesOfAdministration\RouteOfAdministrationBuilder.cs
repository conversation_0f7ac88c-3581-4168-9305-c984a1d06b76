﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.RoutesOfAdministration
{
    public class RouteOfAdministrationBuilder : IBuilder<RouteOfAdministration>
    {
        private int id = Fake.RouteOfAdministration.Id;
        private string name = Fake.RouteOfAdministration.Name;
        private DateTime createdDate = DateTime.Now;
        private DateTime lastUpdatedDate = DateTime.Now;
        private string lastUpdatedBy = Fake.RouteOfAdministration.LastUpdatedBy;
        private string createdBy = Fake.RouteOfAdministration.CreatedBy;

        public RouteOfAdministrationBuilder WithId(int id)
        {
            this.id = id;
            return this;
        }

        public RouteOfAdministrationBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public RouteOfAdministrationBuilder WithCreatedDate(DateTime createdDate)
        {
            this.createdDate = createdDate;
            return this;
        }

        public RouteOfAdministrationBuilder WithCreatedBy(string createdBy)
        {
            this.createdBy = createdBy;
            return this;
        }

        public RouteOfAdministrationBuilder WithLastUpdatedDate(DateTime lastUpdatedDate)
        {
            this.lastUpdatedDate = lastUpdatedDate;
            return this;
        }

        public RouteOfAdministrationBuilder WithLastUpdatedBy(string lastUpdatedBy)
        {
            this.lastUpdatedBy = lastUpdatedBy;
            return this;
        }

        public RouteOfAdministration Build()
        {
            return new(id)
            {
                Name = name,
                CreatedDate = createdDate,
                CreatedBy = createdBy,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy,               
            };
        }
    }
}
