﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Products;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteProductIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteProduct_ValidProductId_ReturnsOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtension = new ProductExtensionModel(
            pcid: Fake.ProductExtension.PCID,
            dosageFormId: dosageForm.Id,
            routeOfAdministrationIds: [routeOfAdministration.Id]);

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithProductExtensions(productExtension)
            .WithDrugSubstances(drugSubstances.Select(d => d.Id).ToList())
            .WithProductTypes(productTypeIds)
            .Build();
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);

        //Act
        await productApi.DeleteProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var productResponse = () => productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);
        await productResponse.Should().ThrowAsync<ApiException>();
    }

    [Fact]
    public async Task DeleteProduct_InvalidProductId_ThrowsException()
    {
        //Arrange
        var productId = Fake.Product.Id;

        //Act
        var deleteProductResponse = () => productApi.DeleteProductAsync(productId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await deleteProductResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("EntityNotFoundException", exception.And.Message);
    }

    [Fact]
    public async Task DeleteProduct_WithAssociatedComments_ThrowsException()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtension = new ProductExtensionModel(
            pcid: Fake.ProductExtension.PCID,
            dosageFormId: dosageForm.Id, routeOfAdministrationIds: [routeOfAdministration.Id]);

        var createProductRequest = new CreateProductCommandRequestBuilder()
            .WithProductExtensions(productExtension)
            .WithDrugSubstances(drugSubstances.Select(d => d.Id).ToList())
            .WithProductTypes(productTypeIds)
            .Build();

        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, createProductRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productResponse.ProductExtensions[0].Id, RouteOfAdministrationIds = productExtension.RouteOfAdministrationIds })
            .WithDrugSubstanceIds(productResponse.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);


        //Act
        var deleteProductResponse = () => productApi.DeleteProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await deleteProductResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("AssociationExistsException");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugSubstances.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Submissions.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        dbContext.Tags.Clear();
        await dbContext.SaveChangesAsync();
    }
}
