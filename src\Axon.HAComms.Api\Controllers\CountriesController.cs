﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Application.Queries.Countries.ListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Countries")]
public class CountriesController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all countries
    /// </summary>
    /// <returns>All countries</returns>
    [HttpGet(Name = "GetCountryList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewCountry))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<CountryModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetCountryListAsync()
        => await Send(new GetCountryListQueryRequest());
}
