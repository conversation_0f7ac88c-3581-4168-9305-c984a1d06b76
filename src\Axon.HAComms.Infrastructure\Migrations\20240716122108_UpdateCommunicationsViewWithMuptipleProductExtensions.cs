﻿using Axon.HAComms.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Axon.HAComms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateCommunicationsViewWithMuptipleProductExtensions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("20-UpdateCommunicationsView-01-AddMultipleProductExtensions.sql");
            migrationBuilder.SqlFileExec("21-UpdateSearchInComments-01-AddMultipleProductExtensions.sql");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //do nothing
        }
    }
}
