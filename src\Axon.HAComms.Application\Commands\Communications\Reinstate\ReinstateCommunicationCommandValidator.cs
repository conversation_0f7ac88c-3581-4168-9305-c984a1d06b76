﻿using FluentValidation;
using JetBrains.Annotations;

namespace Axon.HAComms.Application.Commands.Communications.Reinstate
{
    [UsedImplicitly]
    public class ReinstateCommunicationCommandValidator : AbstractValidator<ReinstateCommunicationCommandRequest>
    {
        public ReinstateCommunicationCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty();
        }
    }
}