﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.RoutesOfAdministration;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateRouteOfAdministrationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly RouteOfAdministrationApi apiRouteOfAdministration = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateRouteOfAdministration_ValidRequest_ReturnsOk()
    {
        //Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;
        var requestObj = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);

        //Act
        var responseObj = await apiRouteOfAdministration.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Name.Should().Be(routeOfAdministrationName);
    }

    [Fact]
    public async Task CreateRouteOfAdministration_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;

        var requestObj1 = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);

        var requestObj2 = new CreateRouteOfAdministrationCommandRequest(routeOfAdministrationName);

        //Act
        var responseObj1 = await apiRouteOfAdministration.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();

        var responseObj2 = () => apiRouteOfAdministration.CreateRouteOfAdministrationAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        //Assert
        var exception = await responseObj2.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Route of administration with name '{routeOfAdministrationName}' already exists.");
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
