﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Phlex.Core.Multitenancy;
using System.Linq.Expressions;

namespace Axon.HAComms.Infrastructure.Persistance.Repository;

public class RouteOfAdministrationRepository(MultitenantHacommsDbContext context, ITenant tenant, ILogger<RouteOfAdministrationRepository> logger)
    : SqlServerRepository<RouteOfAdministration>(context, tenant, logger), IRouteOfAdministrationRepository
{
    public async Task<IEnumerable<RouteOfAdministration>> GetItemsAsync()
    {
        return await context.Set<RouteOfAdministration>()
            .AsNoTracking()
            .ToListAsync();
    }

    public IQueryable<RouteOfAdministration> GetQueryableItems()
    {
        return context.Set<RouteOfAdministration>().Include(x => x.ProductExtensions).AsQueryable();
    }

    public async Task<RouteOfAdministration[]> GetAllByIdsAsync(params int[] ids)
    {
        var items = await context.Set<RouteOfAdministration>().Where(x => ids.Contains(x.Id)).ToArrayAsync();
        return items;
    }

    public async Task<RouteOfAdministration> GetItemAsync(int id)
    {
        var entity = await context.Set<RouteOfAdministration>().SingleOrDefaultAsync(d => d.Id == id);

        return entity ?? throw new EntityNotFoundException(nameof(RouteOfAdministration), id);
    }

    public async Task<bool> ExistsAsync(Expression<Func<RouteOfAdministration, bool>> filter)
    {
        return await context.Set<RouteOfAdministration>().AsNoTracking().AnyAsync(filter);
    }
}
