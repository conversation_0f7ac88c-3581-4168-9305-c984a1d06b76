﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class ProductExtensionRouteOfAdministration : BaseEntity
{
    public int ProductExtensionId { get; set; }

    public ProductExtension ProductExtension { get; set; } = null!;

    public int RouteOfAdministrationId { get; set; }

    public RouteOfAdministration RouteOfAdministration { get; set; } = null!;

    public static ProductExtensionRouteOfAdministration Create(ProductExtension productExtension, int routeOfAdministrationId)
    {
        return new()
        {
            ProductExtension = productExtension,
            RouteOfAdministrationId = routeOfAdministrationId
        };
    }
}
