﻿using AutoMapper;
using Axon.HAComms.Application.Common.Mappings;
using Xunit;

namespace Axon.HAComms.Tests
{
    public class MappingTests
    {
        private readonly IMapper sut;

        public MappingTests() 
        {
            var assembly = typeof(DrugSubstancesMappingProfile).Assembly;
            sut = new MapperConfiguration(cfg => cfg.AddMaps(assembly)).CreateMapper();
        }
        
        [Fact]
        public void All_mappings_should_be_setup_correctly() => sut.ConfigurationProvider.AssertConfigurationIsValid();
    }
}
