﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateCommunicationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateCommunication_ValidRequest_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var updatedSubmissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var updatedCountry = await dbContext.Countries.GetRandomEntity();
        var updatedApplication = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(Fake.Communication.Subject)
            .WithSubmissionTypeId(updatedSubmissionType.Id)
            .WithCountryId(updatedCountry.Id)
            .WithApplications(updatedApplication)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(updatedCountry.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().HaveCount(1);
        updatedCommunication.Applications[0].Number.Should().Be(updatedApplication.Number);
        updatedCommunication.Applications[0].Submissions.Should().HaveCount(1);
        updatedCommunication.Applications[0].Submissions[0].Number.Should().Be(updatedApplication.Submissions.Single().Number);
        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }
    
    [Fact]
    public async Task UpdateCommunication_InvalidCountry_ThrowsEntityNotFoundException()
    {
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var maxCountryId = dbContext.Countries.Max(r => r.Id);
        var invalidCountryId = Fake.GetRandomInt(maxCountryId + 1, maxCountryId + 50);

        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(subject)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(invalidCountryId)
            .WithApplications(application)
            .WithDateOfCommunication(dateOfCommunication).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Country with id '{invalidCountryId}' does not exist");
    }

    [Fact]
    public async Task UpdateCommunication_InvalidSubmissionType_ThrowsEntityNotFoundException()
    {
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var maxSubmissionTypeId = dbContext.SubmissionTypes.Max(r => r.Id);
        var invalidSubmissionTypeId = Fake.GetRandomInt(maxSubmissionTypeId + 1, maxSubmissionTypeId + 50);

        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithNumber(Fake.Application.Number)
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions[0];
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(subject)
            .WithApplications(application)
            .WithSubmissionTypeId(invalidSubmissionTypeId)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Submission Type with id '{invalidSubmissionTypeId}' does not exist");
    }

    [Fact]
    public async Task UpdateCommunication_InvalidCommunication_ThrowsEntityNotFoundException()
    {
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;
        var invalidId = Fake.GetRandomInt(communicationId + 1, communicationId + 50);
            
        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(invalidId)
            .WithSubject(subject)
            .WithApplications(application)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"Communication\\\" ({invalidId}) was not found.", exception.And.Message);
    }

    [Fact]
    public async Task UpdateCommunication_WithoutCountry_ThrowsException()
    {
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(subject)
            .WithApplications(application)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(0)
            .WithDateOfCommunication(dateOfCommunication).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Country Id' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task UpdateCommunication_WithoutSubmissionType_ThrowsException()
    {
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(subject)
            .WithApplications(application)
            .WithSubmissionTypeId(0)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Submission Type Id' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task UpdateCommunication_WithoutDateOfCommunication_ThrowsException()
    {
        var subject = Fake.Communication.Subject;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions[0];
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        //Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(subject)
            .WithApplications(application)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(default).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Date Of Communication' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task UpdateCommunication_CompletedCommunication_ThrowsException()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;
        await communicationApi.CompleteCommunicationAsync(communicationId, TenantConstants.DEFAULT_TENANT);

        // Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application).Build();

        var response = () => communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);

        // Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("Cannot update completed communication!", exception.And.Message);
    }

    [Fact]
    public async Task UpdateCommunication_WithoutApplicationAndSubmissionNumber_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubject(Fake.Communication.Subject)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(country.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().BeEmpty();
        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateCommunication_WithOneApplicationNumberWithoutSubmissionNumber_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var application = ApplicationModelBuilder.Default().Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(country.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().HaveCount(1);
        updatedCommunication.Applications[0].Number.Should().Be(application.Number);
        updatedCommunication.Applications[0].Submissions.Should().BeEmpty();
        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateCommunication_WithMultipleApplicationNumbersWithoutSubmissionNumber_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var application1 = ApplicationModelBuilder.Default().Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application1)
            .WithComment(comment).Build();
        
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var application2 = ApplicationModelBuilder.Default().Build();
        var application3 = ApplicationModelBuilder.Default().Build();
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application1, application2, application3)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(country.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().HaveCount(3);
        updatedCommunication.Applications[0].Number.Should().Be(application1.Number);
        updatedCommunication.Applications[1].Number.Should().Be(application2.Number);
        updatedCommunication.Applications[2].Number.Should().Be(application3.Number);
        updatedCommunication.Applications[0].Submissions.Should().BeEmpty();
        updatedCommunication.Applications[1].Submissions.Should().BeEmpty();
        updatedCommunication.Applications[2].Submissions.Should().BeEmpty();
        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateCommunication_WithMultipleApplicationNumbersWithMultipleSubmissionNumbers_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var submission1 = SubmissionModelBuilder.Default().Build();
        var submission2 = SubmissionModelBuilder.Default().Build();
        var submission3 = SubmissionModelBuilder.Default().Build();
        var submission4 = SubmissionModelBuilder.Default().Build();
            
        var application1 = ApplicationModelBuilder.Default().WithSubmissions(submission1, submission3).Build();
        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application1)
            .WithComment(comment).Build();
       
        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var application2 = ApplicationModelBuilder.Default().WithSubmissions(submission2).Build();
        var application3 = ApplicationModelBuilder.Default().WithSubmissions(submission4).Build();
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application1, application2, application3)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(country.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().HaveCount(3);
        updatedCommunication.Applications[0].Number.Should().Be(application1.Number);
        updatedCommunication.Applications[0].Submissions.Should().HaveCount(2);
        updatedCommunication.Applications[0].Submissions[0].Number.Should().Be(application1.Submissions[0].Number);
        updatedCommunication.Applications[0].Submissions[1].Number.Should().Be(application1.Submissions[1].Number);

        updatedCommunication.Applications[1].Number.Should().Be(application2.Number);
        updatedCommunication.Applications[1].Submissions.Should().HaveCount(1);
        updatedCommunication.Applications[1].Submissions[0].Number.Should().Be(application2.Submissions[0].Number);

        updatedCommunication.Applications[2].Number.Should().Be(application3.Number);
        updatedCommunication.Applications[2].Submissions.Should().HaveCount(1);
        updatedCommunication.Applications[2].Submissions[0].Number.Should().Be(application3.Submissions[0].Number);

        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateCommunication_WithOneApplicationNumberWithMultipleSubmissionNumbers_ReturnsOk()
    {
        // Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var productExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        var submission1 = SubmissionModelBuilder.Default().Build();
        var submission2 = SubmissionModelBuilder.Default().Build();
        var submission3 = SubmissionModelBuilder.Default().Build();
        var application1 = ApplicationModelBuilder.Default()
            .WithSubmissions(submission1, submission2, submission3).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Take(2).Select(x => x.Id).ToArray()).Build();

        var createRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(DateTime.Now)
            .WithApplications(application1)
            .WithComment(comment).Build();

        var createCommunicationResponse = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createRequest);
        createCommunicationResponse.Should().NotBeNull();
        createCommunicationResponse.Id.Should().NotBe(0);
        var communicationId = createCommunicationResponse.Id;

        // Act
        var updateRequest = UpdateCommunicationCommandRequestBuilder.Default()
            .WithId(communicationId)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithApplications(application1)
            .WithDateOfCommunication(DateTime.Now).Build();

        var updateResponse = await communicationApi.UpdateCommunicationAsync(TenantConstants.DEFAULT_TENANT, updateRequest);
        updateResponse.Should().NotBeNull();
        updateResponse.Id.Should().Be(communicationId);

        var updatedCommunication = await communicationApi.GetCommunicationAsync(updateResponse.Id, TenantConstants.DEFAULT_TENANT);

        // Assert
        updatedCommunication.Should().NotBeNull();
        updatedCommunication.Id.Should().Be(communicationId);
        updatedCommunication.Country.Id.Should().Be(updateRequest.CountryId);
        updatedCommunication.Country.Name.Should().Be(country.Name);
        updatedCommunication.Subject.Should().Be(updateRequest.Subject);
        updatedCommunication.Applications.Should().HaveCount(1);
        updatedCommunication.Applications[0].Number.Should().Be(application1.Number);
        updatedCommunication.Applications[0].Submissions.Should().HaveCount(3);
        updatedCommunication.Applications[0].Submissions[0].Number.Should().Be(application1.Submissions[0].Number);
        updatedCommunication.Applications[0].Submissions[1].Number.Should().Be(application1.Submissions[1].Number);
        updatedCommunication.Applications[0].Submissions[2].Number.Should().Be(application1.Submissions[2].Number);
        updatedCommunication.DateOfCommunication.Should().BeSameDateAs(updateRequest.DateOfCommunication);
        updatedCommunication.SubmissionType.Id.Should().Be(updateRequest.SubmissionTypeId);
        updatedCommunication.IsCompleted.Should().BeFalse();
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();

        await dbContext.SaveChangesAsync();
    }
}
