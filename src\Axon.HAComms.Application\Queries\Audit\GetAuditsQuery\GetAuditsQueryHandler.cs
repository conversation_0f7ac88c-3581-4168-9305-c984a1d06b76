﻿using Axon.Core.Shared.Audit.Providers;
using Axon.Core.Shared.Audit;
using Axon.HAComms.Application.Common;
using MediatR;
using Microsoft.AspNetCore.OData.Query;

namespace Axon.HAComms.Application.Queries.Audit.GetAuditsQuery;

internal sealed class GetAuditsQueryHandler(
    IAuditProvider<ApplicationAudit> auditProvider) : IRequestHandler<GetAuditsQueryRequest, GetAuditsQueryResponse>
{
    public Task<GetAuditsQueryResponse> Handle(GetAuditsQueryRequest request, CancellationToken cancellationToken)
    {
        var auditQuery = auditProvider.QueryEvents().Where(audit => audit.Tenant == request.Tenant);

        if (request.OdataOptions.Filter != null)
        {
            auditQuery = request.OdataOptions.Filter.ApplyTo(auditQuery, new ODataQuerySettings()) as IQueryable<ApplicationAudit>;
        }

        if (request.OdataOptions.OrderBy != null)
        {
            auditQuery = request.OdataOptions.OrderBy.ApplyTo(auditQuery, new ODataQuerySettings());
        }

        if (request.OdataOptions.Skip != null)
        {
            auditQuery = request.OdataOptions.Skip.ApplyTo(auditQuery, new ODataQuerySettings());
        }

        auditQuery = request.OdataOptions.Top == null
            ? auditQuery?.Take(request.OdataOptions.Context.DefaultQueryConfigurations.MaxTop ?? Constants.O_DATA_MAX_TOP)
            : request.OdataOptions.Top?.ApplyTo(auditQuery, new ODataQuerySettings());

        if (auditQuery == null) return Task.FromResult(new GetAuditsQueryResponse());

        var audits = auditQuery.ToList();
        return Task.FromResult(new GetAuditsQueryResponse { ApplicationAudits = audits });
    }
}
