﻿using Axon.HAComms.Application.Models.Submission;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Submissions
{
    public class SubmissionModelBuilder : IBuilder<SubmissionModel>
    {
        private string number = Fake.Submission.Number;

        public static DosageFormModelBuilder Default() => new();

        public SubmissionModelBuilder WithNumber(string number)
        {
            this.number = number;
            return this;
        }

        public SubmissionModel Build()
        {
            return new()
            {
                Number = number
            };
        }
    }
}
