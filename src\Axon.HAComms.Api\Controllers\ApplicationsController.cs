﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Queries.Applications.ListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Applications")]
public class ApplicationsController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get all application numbers
    /// </summary>
    [HttpGet("application-numbers", Name = "GetAllApplicationNumbers")]
    //[HasPermissions(nameof(HacommsPermissions.ViewApplicationNumber))]
    [ProducesResponseType(200, Type = typeof(ApiListResult<string>))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAllApplicationNumbersAsync() =>
        await Send(new GetApplicationNumberListQueryRequest());
}
