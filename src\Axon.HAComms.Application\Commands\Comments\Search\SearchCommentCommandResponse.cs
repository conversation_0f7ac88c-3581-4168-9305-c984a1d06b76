namespace Axon.HAComms.Application.Commands.Comments.Search
{
    public class SearchCommentCommandResponse
    {
        public IEnumerable<SearchComment>? Comments { get; set; }

        public int? Total { get; set; }
    }

    public class SearchComment
    {
        public int CommentId { get; set; }
        public int CommunicationId { get; set; }
        public string? Description { get; set; }
        public DateTime? DateOfCommunication { get; set; }
        public string? ProductCodes { get; set; }
        public string? ProductName { get; set; }
        public string? CountryName { get; set; }
        public string? DrugSubstanceNames { get; set; }
        public string? DosageFormNames { get; set; }
        public string? RouteOfAdministrationNames { get; set; }
        public string? SubmissionNumberNames { get; set; }
        public string? ApplicationNumberNames { get; set; }
        public string? SubmissionTypeName { get; set; }
        public string? TagNames { get; set; }
        public string? ProductTypeNames { get; set; }
        public bool? IsQuestionIncluded { get; set; }
        public bool? IsGeneralGuidance { get; set; }
        public string? BIRDSLinkToBIResponse { get; set; }
        public string? BIRDSLinkToBISAMP { get; set; }
        public string? Question { get; set; }
        public string? Response { get; set; }
    }
}
