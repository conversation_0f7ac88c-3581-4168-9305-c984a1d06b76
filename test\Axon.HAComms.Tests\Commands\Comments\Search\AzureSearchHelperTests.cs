﻿using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Helpers;
using Axon.HAComms.Tests.Common;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;
using Xunit.Sdk;

namespace Axon.HAComms.Tests.Commands.Comments.Search
{
    public class AzureSearchHelperTests
    {
        private readonly AzureSearchHelper helper;
        private readonly SearchIndexClient searchIndexClient;
        private readonly SearchIndexerClient searchIndexerClient;
        private readonly SearchSettings searchSettings;

        public AzureSearchHelperTests()
        {
            var searchClient = Substitute.For<SearchClient>();
            searchIndexClient = Substitute.For<SearchIndexClient>();
            searchIndexerClient = Substitute.For<SearchIndexerClient>();
            var option = Substitute.For<IOptionsMonitor<SearchSettings>>();
            searchSettings = new SearchSettings
            {
                ApiKey = Fake.SearchSettings.ApiKey,
                DataSourceName = Fake.SearchSettings.DataSourceName,
                ServiceName = Fake.SearchSettings.ServiceName,
                TableOrView = Fake.SearchSettings.TableOrView,
                IndexerName = Fake.SearchSettings.IndexerName,
                IndexName = Fake.SearchSettings.IndexerName,
                Interval = Fake.SearchSettings.Interval,
                HighWatermarkColumn = Fake.SearchSettings.HighWatermarkColumn,
                SoftDeleteColumn = Fake.SearchSettings.SoftDeleteColumn,
                SoftDeleteMarker = Fake.SearchSettings.SoftDeleteMarker,
                ConnectionString = new Application.AzureSearch.DbSettings() { Default = Fake.DbSettings.Default }
            };
            option.CurrentValue.Returns(searchSettings);
            var logger = Substitute.For<ILogger<AzureSearchHelper>>();

            helper = new AzureSearchHelper(searchClient, searchIndexClient, searchIndexerClient, option, logger);
        }

        [Fact]
        public async Task CreateDataSource_ShouldCallCreateDataSourceConnectionAsync_WithCorrectParameters()
        {
            // Act
            await helper.CreateDataSource();

            // Assert
            await searchIndexerClient.Received(1).CreateDataSourceConnectionAsync(Arg.Is<SearchIndexerDataSourceConnection>(ds =>
                ds.Name == searchSettings.DataSourceName &&
                ds.Type == SearchIndexerDataSourceType.AzureSql &&
                ds.ConnectionString == searchSettings.ConnectionString.Default &&
                ds.Container.Name == searchSettings.TableOrView &&
                ((HighWaterMarkChangeDetectionPolicy)ds.DataChangeDetectionPolicy).HighWaterMarkColumnName == searchSettings.HighWatermarkColumn &&
                ((SoftDeleteColumnDeletionDetectionPolicy)ds.DataDeletionDetectionPolicy).SoftDeleteColumnName == searchSettings.SoftDeleteColumn &&
                ((SoftDeleteColumnDeletionDetectionPolicy)ds.DataDeletionDetectionPolicy).SoftDeleteMarkerValue == searchSettings.SoftDeleteMarker
            ));
        }

        [Fact]
        public async Task CreateIndexAsync_ShouldCallCreateIndexAsync_WithCorrectParameters()
        {
            // Act
            await helper.CreateIndexAsync<TestClass>();

            // Assert
            await searchIndexClient.Received(1).CreateIndexAsync(Arg.Is<SearchIndex>(index =>
                index.Name == searchSettings.IndexName &&
                index.Fields != null &&
                index.Analyzers.Count == 1 &&
                ((CustomAnalyzer)index.Analyzers[0]).Name == "custom-analyzer" &&
                ((CustomAnalyzer)index.Analyzers[0]).TokenizerName == LexicalTokenizerName.MicrosoftLanguageStemmingTokenizer &&
                ((CustomAnalyzer)index.Analyzers[0]).TokenFilters.Contains(TokenFilterName.AsciiFolding) &&
                ((CustomAnalyzer)index.Analyzers[0]).TokenFilters.Contains(TokenFilterName.Lowercase)
            ));
        }

        [Fact]
        public async Task CreateIndexer_ShouldCallCreateIndexerAsync_WithCorrectParameters()
        {
            // Act
            await helper.CreateIndexer();

            // Assert
            await searchIndexerClient.Received(1).CreateIndexerAsync(Arg.Is<SearchIndexer>(indexer =>
                indexer.Name == searchSettings.IndexerName &&
                indexer.DataSourceName == searchSettings.DataSourceName &&
                indexer.TargetIndexName == searchSettings.IndexName &&
                indexer.Schedule != null &&
                indexer.Schedule.Interval == TimeSpan.FromMinutes(searchSettings.Interval)
            ));
        }

        private class TestClass
        {
            public string Name { get; set; } = string.Empty;
        }
    }
}
