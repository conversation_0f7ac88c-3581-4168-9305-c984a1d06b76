﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.Comments.Delete;

internal class DeleteCommentCommandHandler(
    ICommentsRepository commentsRepo,
    ILogger<DeleteCommentCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteCommentCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteCommentCommandRequest request, CancellationToken cancellationToken)
    {
        var comment = await commentsRepo.GetItemByIdAsync(request.Id);
        if (comment == null)
        {
            throw new EntityNotFoundException(nameof(Comment), request.Id);
        }

        var hasOtherComments = await commentsRepo.ExistsAsync(c => c.CommunicationId == comment.CommunicationId && c.Id != request.Id);

        if (!hasOtherComments)
        {
            throw new InvalidOperationException("Cannot delete the last comment for a communication.");
        }

        await auditService.LogAsync(correlationIdProvider.Provide(), clientDetailsProvider.Provide(), AuditEventType.COMMENT_DELETED, AuditEventCategory.COMMENTS,
                AuditEventDescription.COMMENT_DELETE, comment, FuncDeleteComment);

        return Result.Success();

        async Task FuncDeleteComment()
        {
            comment.Delete();
            commentsRepo.UpdateItem(comment);
            await commentsRepo.SaveChangesAsync(userProvider);
            logger.LogInformation("Comment with Id {Id} deleted successfully.", request.Id);
        }
    }
}
