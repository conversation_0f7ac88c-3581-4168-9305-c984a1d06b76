﻿using Axon.HAComms.Application.Commands.Products.Create;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Tests.Builders.ProductExtensions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Create;

public class CreateProductsCommandValidatorTests
{
    private readonly CreateProductCommandValidator validator;

    public CreateProductsCommandValidatorTests()
    {
        var productsRepo = Substitute.For<IProductsRepository>();
        var productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        validator = new CreateProductCommandValidator(productsRepo, productExtensionsRepo);
    }

    [Fact]
    public void Validate_ProductNameIsEmpty_ThrowsException()
    {
        //Arrange
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(string.Empty, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("'Name' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_ProductNameIsNotEmpty_DoesNotThrowException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(productName, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_ProductNameExceedsLength_ThrowsException()
    {
        //Arrange
        var productName = Fake.GetRandomString(31);
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(productName, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("The length of 'Name' must be 30 characters or fewer.", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_ProductNameDoesNotExceedLength_DoesNotThrowException()
    {
        //Arrange
        var productName = Fake.GetRandomString(30);
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(productName, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_ProductNameContainsIllegalCharacters_ThrowsExceptions()
    {
        //Arrange
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;
        var productName = Constants.ILLEGAL_CHARACTERS[0].ToString();

        var request = new CreateProductCommandRequest(productName, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
        Assert.Contains("Name cannot contain illegal characters.", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_ValidProduct_DoesNotThrowException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_DrugSubstanceIdsEmpty_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var drugSubstanceIds = new List<int>();
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.DrugSubstanceIds);
        Assert.Contains("'Drug Substance Ids' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DrugSubstanceIdsContainZero_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        drugSubstanceIds.AddRange(new List<int> { 0, 0 });
        var productTypeIds = Fake.Product.ProductTypeIds;
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.DrugSubstanceIds);
        result.Errors.Should().HaveCount(2);
        result.Errors[0].ErrorMessage.Should().Be("Drug substance Id must not be 0.");
        result.Errors[1].ErrorMessage.Should().Be("Drug substance Id must not be 0.");
    }

    [Fact]
    public void Validate_ProductExtensionsEmpty_ThrowsException()
    {
        //Arrange
        var productExtensions = new List<ProductExtensionModel>();
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductExtensions);
        Assert.Contains("'Product Extensions' must not be empty.", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_ProductExtensionWithPcidExceedsLength_ThrowsException()
    {
        //Arrange
        var pcidLength = 16;
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.GetRandomString(pcidLength))
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert

        result.ShouldHaveValidationErrorFor("ProductExtensions[0].PCID");
        result.Errors.Should().HaveCount(1);
        result.Errors[0].ErrorMessage.Should().Contain($"The length of 'PCID' must be 15 characters or fewer. You entered {pcidLength} characters.");
    }

    [Fact]
    public void Validate_ProductExtensionWithMaxLengthPcid_DoesNotThrowException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductExtensionWithValidLengthPcid_DoesNotThrowException()
    {
        //Arrange
        var pcidLength = 5;
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.GetRandomString(pcidLength))
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductExtensionsWithoutDuplicatePcid_DoesNotThrowException()
    {
        //Arrange
        var productExtension1 = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtension2 = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtension3 = ProductExtensionModelBuilder.Default()
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension1, productExtension2, productExtension3 };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ProductExtensionWithPcidNotAlphaNumeric_ThrowsException()
    {
        //Arrange
        var pcid = "!@#$%^&*()=+-_";
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithPcid(pcid)
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].PCID");
        result.Errors.Should().HaveCount(1);
        result.Errors[0].ErrorMessage.Should().Contain("PCID should only contain alphanumerical characters.");
    }


    [Fact]
    public void Validate_ProductExtensionWithDosageFormIdEqualToZero_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(0)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].DosageFormId");
        result.Errors.Should().HaveCount(1);
        result.Errors[0].ErrorMessage.Should().Contain("'Dosage Form Id' must not be empty.");
    }

    [Fact]
    public void Validate_ProductExtensionWithEmptyRoutesOfAdministration_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].RouteOfAdministrationIds");
        result.Errors.Should().HaveCount(1);
        result.Errors[0].ErrorMessage.Should().Contain("'Route Of Administration Ids' must not be empty.");
    }

    [Fact]
    public void Validate_ProductExtensionWithRouteOfAdministrationIdsContainZero_ThrowsException()
    {
        //Arrange
        var productExtension1 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { 0, Fake.RouteOfAdministration.Id, Fake.RouteOfAdministration.Id }).Build();

        var productExtension2 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { 0, 0, 0 }).Build();

        var productExtension3 = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id, Fake.RouteOfAdministration.Id, Fake.RouteOfAdministration.Id }).Build();

        var productExtensions = new List<ProductExtensionModel> { productExtension1, productExtension2, productExtension3 };
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, Fake.Product.DrugSubstanceIds, Fake.Product.ProductTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor("ProductExtensions[0].RouteOfAdministrationIds[0]");
        result.ShouldHaveValidationErrorFor("ProductExtensions[1].RouteOfAdministrationIds[0]");
        result.ShouldHaveValidationErrorFor("ProductExtensions[1].RouteOfAdministrationIds[1]");
        result.ShouldHaveValidationErrorFor("ProductExtensions[1].RouteOfAdministrationIds[2]");
        result.Errors.Should().HaveCount(4);
        result.Errors.ForEach(x => x.ErrorMessage.Should().Contain("Route of Administration Id must not be 0."));
    }

    [Fact]
    public void Validate_ProductTypeIdsEmpty_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = new List<int>();

        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductTypeIds);
        Assert.Contains("'Product Type Ids' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_ProductTypeIdsContainZero_ThrowsException()
    {
        //Arrange
        var productExtension = ProductExtensionModelBuilder.Default()
            .WithDosageFormId(Fake.DosageForm.Id)
            .WithRouteOfAdministrationIds(new List<int>() { Fake.RouteOfAdministration.Id }).Build();
        var productExtensions = new List<ProductExtensionModel> { productExtension };
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;
        productTypeIds.AddRange(new List<int> { 0, 0 });
        var request = new CreateProductCommandRequest(Fake.Product.Name, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductTypeIds);
        result.Errors.Should().HaveCount(2);
        result.Errors[0].ErrorMessage.Should().Be("Product type Id must not be 0.");
        result.Errors[1].ErrorMessage.Should().Be("Product type Id must not be 0.");
    }

    [Fact]
    public void Validate_ProductTypeIdsAreNotEmpty_DoesNotThrowException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var productExtensions = new List<ProductExtensionModel>();
        var drugSubstanceIds = Fake.Product.DrugSubstanceIds;
        var productTypeIds = Fake.Product.ProductTypeIds;

        var request = new CreateProductCommandRequest(productName, Fake.Product.IsActive, productExtensions, drugSubstanceIds, productTypeIds);

        //Act
        var result = validator.TestValidate(request);

        //Assert
        result.ShouldNotHaveValidationErrorFor(x => x.ProductTypeIds);
    }
}
