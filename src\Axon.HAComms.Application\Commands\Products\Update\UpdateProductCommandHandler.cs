using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Helpers;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Products.Update;

internal class UpdateProductCommandHandler(
    IProductsRepository productsRepo,
    ICommentsRepository commentsRepo,
    IDrugSubstancesRepository drugSubstancesRepo,
    IRouteOfAdministrationRepository routeOfAdministrationRepo,
    IDosageFormsRepository dosageFormsRepo,
    IProductExtensionsRepository productExtensionsRepo,
    IProductTypesRepository productTypesR<PERSON>o,
    I<PERSON>apper mapper,
    ILogger<UpdateProductCommandHandler> logger,
    IAuditService auditService,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider) : IRequestHandler<UpdateProductCommandRequest, UpdateProductCommandResponse>
{
    public async Task<UpdateProductCommandResponse> Handle(UpdateProductCommandRequest request, CancellationToken cancellationToken)
    {
        var productExtensions = request.ProductExtensions.ToArray();
        var dosageFormIds = productExtensions.Select(x => x.DosageFormId).Distinct().ToArray();
        var dosageForms = await dosageFormsRepo.GetAllByIdsAsync(dosageFormIds);
        dosageFormIds.ValidateAllIdsExist<DosageForm>(dosageForms.Select(d => d.Id).ToArray());

        var routeOfAdministrationIds = productExtensions.SelectMany(x => x.RouteOfAdministrationIds).Distinct().ToArray();
        var routeOfAdministrationEntities = await routeOfAdministrationRepo.GetAllByIdsAsync(routeOfAdministrationIds);
        routeOfAdministrationIds.ValidateAllIdsExist<RouteOfAdministration>(routeOfAdministrationEntities.Select(r => r.Id).ToArray());

        var drugSubstancesIds = request.DrugSubstanceIds.Distinct().ToArray();
        var drugSubstances = await drugSubstancesRepo.GetAllByIdsAsync(drugSubstancesIds);
        drugSubstancesIds.ValidateAllIdsExist<DrugSubstance>(drugSubstances.Select(d => d.Id).ToArray());

        var productTypeIds = request.ProductTypeIds.Distinct().ToArray();
        var productTypes = await productTypesRepo.GetAllByIdsAsync(productTypeIds);
        productTypeIds.ValidateAllIdsExist<ProductType>(productTypes.Select(d => d.Id).ToArray());

        await ProductsHelper.ValidateProductTypeCombinationAsync(productTypesRepo, request.ProductTypeIds);

        var entity = await productsRepo.GetItemAsync(request.Id);
        var associatedComments = (await commentsRepo.GetItemsWithProductExtensionIdsAsync(entity.ProductExtensions.Select(x => x.Id).ToArray())).ToList();

        var hasAssociatedComments = associatedComments.Count != 0;
        if (hasAssociatedComments)
        {
            ValidateForAssociatedComment(entity, productExtensions, drugSubstancesIds, associatedComments);
        }

        var updatedCommentsCount = 0;
        if (!entity.ProductTypes.Select(x => x.Id).OrderBy(e => e).SequenceEqual(productTypeIds.OrderBy(e => e)) && hasAssociatedComments)
        {
            updatedCommentsCount = associatedComments.Count;
        }

        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DRUG_PRODUCT_UPDATED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_UPDATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                entity.IsActive = request.IsActive;

                await DeleteRemovedProductExtensions(entity, productExtensions);
                await UpdateProductExtensions(entity, productExtensions, dosageForms, routeOfAdministrationEntities);
                await AddNewProductExtensions(entity, productExtensions, dosageForms, routeOfAdministrationEntities);
                entity.DrugSubstanceProducts = drugSubstances.Select(ds => new DrugSubstanceDrugProduct { DrugSubstancesId = ds.Id, ProductsId = entity.Id }).ToList();
                entity.ProductProductTypes = productTypes.Select(pt => new ProductProductTypes { ProductTypeId = pt.Id, ProductId = entity.Id }).ToList();

                productsRepo.UpdateItem(entity);
                await productsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Product {ProductName} updated successfully.", entity.Name);
            });

        var response = mapper.Map<UpdateProductCommandResponse>(entity);
        response.UpdatedCommentsCount = updatedCommentsCount;
        return response;
    }

    private async Task AddNewProductExtensions(
        Product product,
        ProductExtensionModel[] productExtensions,
        DosageForm[] dosageForms,
        RouteOfAdministration[] routeOfAdministrationEntities)
    {
        var newProductExtensions = productExtensions.Where(p => p.Id == 0).ToArray();
        if (newProductExtensions.Length == 0)
        {
            return;
        }
        var extensions = newProductExtensions.ToProductExtensionEntity(dosageForms, routeOfAdministrationEntities, product);

        foreach (var extension in extensions)
        {
            productExtensionsRepo.AddItem(extension);
        }
        await productExtensionsRepo.SaveChangesAsync(userProvider);
    }

    private void ValidateForAssociatedComment(Product product,
        ProductExtensionModel[] productExtensions,
        int[] drugSubstancesIds,
        ICollection<Comment> associatedComments)
    {
        if ((product.DrugSubstances.Count != drugSubstancesIds.Length || !product.DrugSubstances.Select(ds => ds.Id).All(i => drugSubstancesIds.Contains(i)))
            && associatedComments.Count != 0)
        {
            logger.LogInformation("Product {ProductName} has associated comments and cannot be modified.", product.Name);
            throw new AssociationExistsException("Product", product.Name);
        }
        var extensionIdsForDelete = product.ProductExtensions.Select(p => p.Id).Except(productExtensions.Select(pe => pe.Id));

        var associatedCommentsProductExtensionIds =
            associatedComments.Where(x => x.CommentProductExtensions != null).SelectMany(x => x.CommentProductExtensions!.Select(pe => pe.ProductExtensionId)).ToArray();

        if (extensionIdsForDelete.Any(el => associatedCommentsProductExtensionIds.Contains(el)))
        {
            logger.LogInformation("Product Extension {ExtensionPcid} has associated comments and cannot be modified.",
                product.ProductExtensions.First(e => associatedCommentsProductExtensionIds.Contains(e.Id)));
            throw new AssociationExistsException("Product Extension", product.ProductExtensions.First(e => associatedCommentsProductExtensionIds.Contains(e.Id)));
        }

        var extensionsToCheck = productExtensions.Where(pe => pe.Id != 0).ToList();
        extensionsToCheck.ForEach(
            dto =>
            {
                var existingProductExtension = product.ProductExtensions.SingleOrDefault(pe => pe.Id == dto.Id);
                if (existingProductExtension == null) return;
                if (existingProductExtension.DosageForm.Id != dto.DosageFormId && associatedComments.Any(x =>
                        x.CommentProductExtensions != null && x.CommentProductExtensions.Select(pe => pe.ProductExtensionId).Contains(dto.Id)))
                {
                    logger.LogInformation("Product Extension {ExtensionPcid} has associated comments and cannot be modified.", dto.Id);
                    throw new AssociationExistsException("Product Extension", dto.Id);
                }

                var existingRouteOfAdminIds = existingProductExtension.RouteOfAdministrations.Select(r => r.Id).ToList();
                if ((existingRouteOfAdminIds.Count != dto.RouteOfAdministrationIds.Count || !existingRouteOfAdminIds.TrueForAll(i => dto.RouteOfAdministrationIds.Contains(i)))
                    && associatedComments.Any(x => x.CommentProductExtensions != null && x.CommentProductExtensions.Select(pe => pe.ProductExtensionId).Contains(dto.Id)))
                {
                    logger.LogInformation("Product Extension {ExtensionPcid} has associated comments and cannot be modified.", dto.Id);
                    throw new AssociationExistsException("Product Extension", dto.Id);
                }
            });
    }

    private async Task UpdateProductExtensions(Product product,
        ProductExtensionModel[] productExtensions,
        DosageForm[] dosageForms,
        RouteOfAdministration[] routeOfAdministrationEntities)
    {
        var updatedProductExtensions = productExtensions.Where(pe => pe.Id != 0).ToList();
        var productExtensionEntities = product.ProductExtensions;
        var dosageFormMap = dosageForms.ToDictionary(x => x.Id, x => x);

        foreach (var dto in updatedProductExtensions)
        {
            var existingProductExtension = productExtensionEntities.SingleOrDefault(pe => pe.Id == dto.Id);
            if (existingProductExtension == null) continue;

            existingProductExtension.PCID = dto.PCID;
            existingProductExtension.IsActive = dto.IsActive;

            var dosageForm = dosageFormMap[dto.DosageFormId];
            var routeOfAdminIdsForExtension = dto.RouteOfAdministrationIds.ToArray();
            var routeOfAdministrationsForExtension = routeOfAdministrationEntities.Where(x => routeOfAdminIdsForExtension.Contains(x.Id))
                .OrderBy(roa => routeOfAdminIdsForExtension.ToList().IndexOf(roa.Id)).ToList();

            existingProductExtension.DosageFormId = dosageForm.Id;
            existingProductExtension.RouteOfAdministrations.Clear();
            existingProductExtension.ProductExtensionRouteOfAdministrations = routeOfAdministrationsForExtension.Select(roa =>
                new ProductExtensionRouteOfAdministration { ProductExtensionId = existingProductExtension.Id, RouteOfAdministrationId = roa.Id }).ToList();
            productExtensionsRepo.UpdateItem(existingProductExtension);
        }

        await productExtensionsRepo.SaveChangesAsync(userProvider);
    }

    private async Task DeleteRemovedProductExtensions(Product product, ProductExtensionModel[] productExtensions)
    {
        var removedProductExtensionIds = product.ProductExtensions.Select(p => p.Id).Except(productExtensions.Select(pe => pe.Id)).ToList();
        if (removedProductExtensionIds.Count == 0) return;

        var productExtensionsToRemove = product.ProductExtensions.Where(pe => removedProductExtensionIds.Contains(pe.Id)).ToArray();
        foreach (var productExtension in productExtensionsToRemove)
        {
            product.ProductExtensions.Remove(productExtension);
        }

        await productExtensionsRepo.SaveChangesAsync(userProvider);
    }
}
