﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Tags;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateTagIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly TagApi apiTag = new TagApi(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateTag_ValidRequest_ReturnsOk()
    {
        //Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;
        var requestObj = new CreateTagCommandRequest(tagName, tagDescription);

        //Act
        var responseObj = await apiTag.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Name.Should().Be(tagName);
        responseObj.Description.Should().Be(tagDescription);
    }

    [Fact]
    public async Task CreateTag_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;

        var requestObj1 = new CreateTagCommandRequest(tagName, tagDescription);

        var requestObj2 = new CreateTagCommandRequest(tagName, tagDescription);

        //Act
        var responseObj1 = await apiTag.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj1);

        responseObj1.Should().NotBeNull();

        var responseObj2 = () => apiTag.CreateTagAsync(TenantConstants.DEFAULT_TENANT, requestObj2);

        //Assert
        var exception = await responseObj2.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Tag with name '{tagName}' already exists.");
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.Tags.Clear();
        await dbContext.SaveChangesAsync();
    }
}
