/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// ProductModel
    /// </summary>
    [DataContract(Name = "ProductModel")]
    public partial class ProductModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductModel" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected ProductModel() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="name">name (required).</param>
        /// <param name="isActive">isActive.</param>
        /// <param name="drugSubstances">drugSubstances.</param>
        /// <param name="productTypes">productTypes.</param>
        /// <param name="productExtensions">productExtensions.</param>
        public ProductModel(int id = default(int), string name = default(string), bool isActive = default(bool), List<DrugSubstanceModel> drugSubstances = default(List<DrugSubstanceModel>), List<ProductTypeModel> productTypes = default(List<ProductTypeModel>), List<ProductExtensionResponseModel> productExtensions = default(List<ProductExtensionResponseModel>))
        {
            // to ensure "name" is required (not null)
            if (name == null)
            {
                throw new ArgumentNullException("name is a required property for ProductModel and cannot be null");
            }
            this.Name = name;
            this.Id = id;
            this.IsActive = isActive;
            this.DrugSubstances = drugSubstances;
            this.ProductTypes = productTypes;
            this.ProductExtensions = productExtensions;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Name
        /// </summary>
        [DataMember(Name = "name", IsRequired = true, EmitDefaultValue = true)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets IsActive
        /// </summary>
        [DataMember(Name = "isActive", EmitDefaultValue = true)]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstances
        /// </summary>
        [DataMember(Name = "drugSubstances", EmitDefaultValue = true)]
        public List<DrugSubstanceModel> DrugSubstances { get; set; }

        /// <summary>
        /// Gets or Sets ProductTypes
        /// </summary>
        [DataMember(Name = "productTypes", EmitDefaultValue = true)]
        public List<ProductTypeModel> ProductTypes { get; set; }

        /// <summary>
        /// Gets or Sets ProductExtensions
        /// </summary>
        [DataMember(Name = "productExtensions", EmitDefaultValue = true)]
        public List<ProductExtensionResponseModel> ProductExtensions { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ProductModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Name: ").Append(Name).Append("\n");
            sb.Append("  IsActive: ").Append(IsActive).Append("\n");
            sb.Append("  DrugSubstances: ").Append(DrugSubstances).Append("\n");
            sb.Append("  ProductTypes: ").Append(ProductTypes).Append("\n");
            sb.Append("  ProductExtensions: ").Append(ProductExtensions).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // Name (string) minLength
            if (this.Name != null && this.Name.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Name, length must be greater than 1.", new [] { "Name" });
            }

            yield break;
        }
    }

}
