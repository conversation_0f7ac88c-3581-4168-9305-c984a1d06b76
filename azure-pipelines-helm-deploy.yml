pool: pv-pool

trigger: none
pr: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: Axon.HAComms.Api.Build
      trigger:
        branches:
          include:
          - develop
  repositories:
    - repository: devops-templates
      name: DevOps/DevOps.Pipelines.Templates
      type: git
    - repository: axon-templates
      name: Axon/Axon.Pipelines.Templates
      type: git

variables:
  - template: Variables/environments.yml@axon-templates

stages:
  - template: ./azure-pipelines-helm-template.yml