﻿using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.Country;

public class CountryModelBuilder : IBuilder<CountryModel>
{
    private int id = Fake.Country.Id;
    private string name = Fake.Country.Name;

    public static CountryModelBuilder Default() => new();

    public CountryModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CountryModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public CountryModel Build()
    {
        return new(id, name)
        {
            Id = id,
            Name = name
        };
    }
}
