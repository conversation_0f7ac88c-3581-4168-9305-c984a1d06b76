﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <Nullable>enable</Nullable>
	  <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	<None Remove="Scripts\00-AddCountries.sql" />
	<None Remove="Scripts\00-UpdateCountries.sql" />
    <None Remove="Scripts\00-UpdateSubstanceCodes.sql" />
    <None Remove="Scripts\00-UpdateSubstanceINNs.sql" />
    <None Remove="Scripts\01-AddDosageFormsAndRouteOfAdmins-01-InsertDosageForms.sql" />
    <None Remove="Scripts\01-AddDosageFormsAndRouteOfAdmins-02-InsertRouteOfAdministrations.sql" />
    <None Remove="Scripts\02-AddSubmissionTypes-01-InsertSubmissionTypes.sql" />
    <None Remove="Scripts\03-AddCountries-01-InsertCountries.sql" />
    <None Remove="Scripts\04-AddTags-01-InsertTags.sql" />
    <None Remove="Scripts\06-CreateCommentsView.sql" />
    <None Remove="Scripts\07-UpdateCommentsView.sql" />
    <None Remove="Scripts\08-UpdateCommentsView_AddRIMLinks.sql" />
    <None Remove="Scripts\09-UpdateCommentsView_AddDrugSubstanceCodeAndInnToSearchView.sql" />
    <None Remove="Scripts\10-UpdateCommentsView_AddQuestionResponseToSearchView.sql" />
    <None Remove="Scripts\11-UpdateCommentsView_RIMLinksToSearchView.sql" />
    <None Remove="Scripts\12-AddProductTypes-01-InsertProductTypes.sql" />
    <None Remove="Scripts\12-AddProductTypes-02-UpdateExistingProductsAndCommentsWithNotCategorizedProductType.sql" />
    <None Remove="Scripts\13-UpdateCommentsViewWithProductTypes-01-AddProductTypes.sql" />
    <None Remove="Scripts\14-AddNewSubmissionTypes-01-InsertNewSubmissionTypes.sql" />
    <None Remove="Scripts\15-UpdateCommentsViewIncludeCoommunicationsWithoutApplicationNumbers.sql" />
    <None Remove="Scripts\16-RemoveDrugTypesFromCommentsView.sql" />
    <None Remove="Scripts\17-RemoveCommentProductTypeFromCommentsView.sql" />
    <None Remove="Scripts\18-UpdateSearchInCommentsView-01-AddLastUpdatedDateColumn.sql" />
    <None Remove="Scripts\19-MigrateProductExtensionDataToMultipleProductExtensionsStructure.sql" />
    <None Remove="Scripts\20-UpdateCommunicationsView-01-AddMultipleProductExtensions.sql" />
    <None Remove="Scripts\21-UpdateSearchInComments-01-AddMultipleProductExtensions.sql" />
    <None Remove="Scripts\22-RemoveDuplicateComments-01-DeleteDuplicateComments.sql" />
    <None Remove="Scripts\23-UpdateSearchInCommentsViewToRemoveDuplicates-01-MultipleDosageFormsAndProductCodes.sql" />
    <None Remove="Scripts\24-UpdateSearchInCommentsView-01-FixSorting.sql" />
    <None Remove="Scripts\25-UpdateSearchInCommentsViewWithIsDeleted-01-AddIsDeleted.sql" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Scripts\00-AddCountries.sql" />
    <EmbeddedResource Include="Scripts\00-UpdateSubstanceCodes.sql" />
    <EmbeddedResource Include="Scripts\00-UpdateCountries.sql" />
    <EmbeddedResource Include="Scripts\00-UpdateSubstanceINNs.sql" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Scripts\01-AddDosageFormsAndRouteOfAdmins-01-InsertDosageForms.sql" />
    <EmbeddedResource Include="Scripts\03-AddCountries-01-InsertCountries.sql" />
    <EmbeddedResource Include="Scripts\02-AddSubmissionTypes-01-InsertSubmissionTypes.sql" />
    <EmbeddedResource Include="Scripts\01-AddDosageFormsAndRouteOfAdmins-02-InsertRouteOfAdministrations.sql" />
    <EmbeddedResource Include="Scripts\04-AddTags-01-InsertTags.sql" />
    <EmbeddedResource Include="Scripts\06-CreateCommentsView.sql" />
    <EmbeddedResource Include="Scripts\07-UpdateCommentsView.sql" />
    <EmbeddedResource Include="Scripts\08-UpdateCommentsView_AddRIMLinks.sql" />
    <EmbeddedResource Include="Scripts\09-UpdateCommentsView_AddDrugSubstanceCodeAndInnToSearchView.sql" />
    <EmbeddedResource Include="Scripts\10-UpdateCommentsView_AddQuestionResponseToSearchView.sql" />
    <EmbeddedResource Include="Scripts\11-UpdateCommentsView_RIMLinksToSearchView.sql" />
    <EmbeddedResource Include="Scripts\12-AddProductTypes-01-InsertProductTypes.sql" />
    <EmbeddedResource Include="Scripts\12-AddProductTypes-02-UpdateExistingProductsAndCommentsWithNotCategorizedProductType.sql" />
    <EmbeddedResource Include="Scripts\13-UpdateCommentsViewWithProductTypes-01-AddProductTypes.sql" />
    <EmbeddedResource Include="Scripts\14-AddNewSubmissionTypes-01-InsertNewSubmissionTypes.sql" />
    <EmbeddedResource Include="Scripts\15-UpdateCommentsViewIncludeCoommunicationsWithoutApplicationNumbers.sql" />
    <EmbeddedResource Include="Scripts\16-RemoveDrugTypesFromCommentsView.sql" />
    <EmbeddedResource Include="Scripts\17-RemoveCommentProductTypeFromCommentsView.sql" />    
    <EmbeddedResource Include="Scripts\18-UpdateSearchInCommentsView-01-AddLastUpdatedDateColumn.sql" />    
    <EmbeddedResource Include="Scripts\19-MigrateProductExtensionDataToMultipleProductExtensionsStructure.sql" />    
    <EmbeddedResource Include="Scripts\20-UpdateCommunicationsView-01-AddMultipleProductExtensions.sql" />
	<EmbeddedResource Include="Scripts\21-UpdateSearchInComments-01-AddMultipleProductExtensions.sql" />
    <EmbeddedResource Include="Scripts\22-RemoveDuplicateComments-01-DeleteDuplicateComments.sql" />
    <EmbeddedResource Include="Scripts\24-UpdateSearchInCommentsView-01-FixSorting.sql" />
    <EmbeddedResource Include="Scripts\23-UpdateSearchInCommentsViewToRemoveDuplicates-01-MultipleDosageFormsAndProductCodes.sql" />
    <EmbeddedResource Include="Scripts\25-UpdateSearchInCommentsViewWithIsDeleted-01-AddIsDeleted.sql" />
  </ItemGroup>

  <ItemGroup>
	  <PackageReference Include="Autofac" Version="8.2.0" />
	  <PackageReference Include="Azure.Identity" Version="1.13.1" />
	  <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
    <PackageReference Include="System.Formats.Asn1" Version="8.0.2" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.Identity.Web" Version="3.8.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Axon.HAComms.Application\Axon.HAComms.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
  </ItemGroup>

</Project>
