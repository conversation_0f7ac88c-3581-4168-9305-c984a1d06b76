﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.DrugSubstances;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.DrugSubstances.ListQuery;

internal class GetDrugSubstancesListQueryHandler(IDrugSubstancesRepository drugSubstancesRepo, IMapper mapper) :
    IRequestHandler<GetDrugSubstancesListQueryRequest, ApiListResult<DrugSubstanceModel>>
{
    public async Task<ApiListResult<DrugSubstanceModel>> Handle(GetDrugSubstancesListQueryRequest request, CancellationToken cancellationToken)
    {
        var entities = await drugSubstancesRepo.GetItemsAsync();
        return new ApiListResult<DrugSubstanceModel>(entities.OrderBy(s => s.Code).Select(mapper.Map<DrugSubstanceModel>));
    }
}
