﻿using Axon.HAComms.Application.Models.Communications;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Communications.PagedListQuery;

public class GetCommunicationsPagedListQueryRequest(
    string[]? filters,
    int skip,
    int take,
    string? order)
    : IRequest<ApiPagedListResult<CommunicationPagedListModel>>
{
    public string[]? Filters { get; } = filters;
    public int Skip { get; } = skip;
    public int Take { get; } = take;
    public string? Order { get; } = order;
}
