﻿using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Commands.Communications.Create;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Application;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Application.Models.Submission;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Applications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Create;

public class CreateCommunicationCommandValidatorTests
{
    private readonly CreateCommunicationCommandValidator sut;
    private readonly ISubmissionTypesRepository subTypeRepo;
    private readonly ICountriesRepository countryRepo;

    public CreateCommunicationCommandValidatorTests()
    {
        subTypeRepo = Substitute.For<ISubmissionTypesRepository>();
        countryRepo = Substitute.For<ICountriesRepository>();
        sut = new CreateCommunicationCommandValidator(subTypeRepo, countryRepo);
    }

    [Fact]
    public void Validate_SubjectIsEmpty_ThrowsException()
    {
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        var request = new CreateCommunicationCommandRequest(
            string.Empty,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        var result = sut.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Subject);
        Assert.Contains("'Subject' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_SubjectIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Subject);
    }

    [Fact]
    public void Validate_DateOfCommunicationIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            default,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.DateOfCommunication);
        Assert.Contains("'Date Of Communication' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_DateOfCommunicationIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.DateOfCommunication);
    }

    [Fact]
    public void Validate_SubmissionTypeIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            default,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SubmissionTypeId);
        Assert.Contains("'Submission Type Id' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_SubmissionTypeIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.SubmissionTypeId);
    }

    [Fact]
    public void Validate_CountryIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var applications = new List<ApplicationModel>();
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            default,
            applications,
            comment);

        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CountryId);
        Assert.Contains("'Country Id' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_CountryIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                                {
                                    new ProductExtensionCommentModel()
                                    {
                                        ProductExtensionId = Fake.ProductExtension.Id,
                                        RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                                    }
                                },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.CountryId);
    }

    [Fact]
    public void Validate_CommentsIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number,
            Submissions =
            [
                new SubmissionModel { Number = Fake.Submission.Number },
                new SubmissionModel { Number = Fake.Submission.Number }
            ]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application],
            null!);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Comment);
        Assert.Contains("'Comment' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_NonGeneralGuidanceCommentsIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number,
            Submissions =
            [
                new SubmissionModel { Number = Fake.Submission.Number },
                new SubmissionModel { Number = Fake.Submission.Number }
            ]
        };
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };
        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application],
            comment);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_GeneralGuidanceCommentsIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number,
            Submissions =
            [
                new SubmissionModel { Number = Fake.Submission.Number },
                new SubmissionModel { Number = Fake.Submission.Number }
            ]
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = true,
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application],
            comment);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ApplicationNumberIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>() { new() };
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Number");
    }

    [Fact]
    public void Validate_ApplicationNumberIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application],
            comment);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ApplicationNumberMaxLengthNotExceeded_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = TestEntitiesGenerator<ApplicationModel, ApplicationModelBuilder>.Generate(2);

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_ApplicationNumberMaxLengthExceeded_ThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = TestEntitiesGenerator<ApplicationModel, ApplicationModelBuilder>.Generate(6);

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications.Count");
    }

    [Fact]
    public void Validate_ApplicationNumberDuplicateValues_ThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application1 = new ApplicationModel()
        {
            Number = Fake.Application.Number
        };

        var application2 = new ApplicationModel()
        {
            Number = application1.Number
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            new List<ApplicationModel>() { application1, application2 },
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications");
    }


    [Fact]
    public void Validate_ApplicationNumberContainingIllegalCharacters_ThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Constants.ILLEGAL_CHARACTERS[0].ToString()
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            new List<ApplicationModel>() { application },
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Number");
    }

    [Fact]
    public void Validate_SubmissionNumberIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>()
        {
            new()
            {
                Number = Fake.Application.Number,
                Submissions =
                [
                    new SubmissionModel()
                ]
            }
        };
        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Submissions[0].Number");
    }

    [Fact]
    public void Validate_SubmissionNumberIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number,
            Submissions =
            [
                new SubmissionModel
                {
                    Number = Fake.Submission.Number
                }
            ]
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application],
            comment);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_SubmissionNumberContainingIllegalCharacters_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>()
        {
            new()
            {
                Number = Fake.Application.Number,
                Submissions = new List<SubmissionModel>()
                {
                    new() {
                        Number = Constants.ILLEGAL_CHARACTERS[0].ToString()
                    }
                }
            }
        };

        var comment = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
                        {
                            new ProductExtensionCommentModel()
                            {
                                ProductExtensionId = Fake.ProductExtension.Id,
                                RouteOfAdministrationIds = new List<int>() { Fake.RouteOfAdministration.Id }
                            }
                        },
            DrugSubstanceIds = [Fake.DrugSubstance.Id],
            TagIds = [Fake.Tag.Id]
        };

        var request = new CreateCommunicationCommandRequest(subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications,
            comment);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Submissions[0].Number").WithErrorMessage("Submission Number cannot contain illegal characters.");
    }

}
