﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Common.Builders;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DosageForms;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteDosageFormIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DosageFormsApi dosageFormApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteDosageForm_ValidRequest_ReturnsOk()
    {
        //Arrange
        var request = new CreateDosageFormCommandRequest(Fake.DosageForm.Name);

        //Act
        var response = await dosageFormApi.CreateDosageFormAsync(TenantConstants.DEFAULT_TENANT, request);

        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        await dosageFormApi.DeleteDosageFormAsync(response.Id, TenantConstants.DEFAULT_TENANT);
        var responseObj = await dosageFormApi.GetDosageFormListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        Assert.DoesNotContain(responseObj.Data, x => x.Id == response.Id);
    }

    [Fact]
    public async Task DeleteDosageForm_WithAssociatedProducts_ThrowsAssociationException()
    {
        //Arrange
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(2);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routesOfAdministration = await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 3);
        var routesOfAdministrationIds = routesOfAdministration.Select(x => x.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds(routesOfAdministrationIds)
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        var response = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        //Act
        var dosageFormResponse = () => dosageFormApi.DeleteDosageFormAsync(dosageForm.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await dosageFormResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AssociationExistsException", exception.And.Message);
    }

    [Fact]
    public async Task DeleteDosageForm_InvalidDosageFormId_ThrowsEntityNotFoundExceptionException()
    {
        //Arrange
        var dosageFormId = Fake.DosageForm.Id;

        //Act
        var response = () => dosageFormApi.DeleteDosageFormAsync(dosageFormId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains($"EntityNotFoundException: Entity \\\"DosageForm\\\" ({dosageFormId}) was not found.", exception.And.Message);
    }

    public async Task InitializeAsync()
    {
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.RouteOfAdministrations.Clear();
        dbContext.DosageForms.Clear();

        await dbContext.SaveChangesAsync();
    }
}
