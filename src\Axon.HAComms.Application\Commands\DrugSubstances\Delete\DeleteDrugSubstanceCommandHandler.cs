using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.DrugSubstances.Delete;

internal class DeleteDrugSubstanceCommandHandler(
    IDrugSubstancesRepository drugSubstanceRepo,
    ICommentsRepository commentsRepo,
    ILogger<DeleteDrugSubstanceCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteDrugSubstanceCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteDrugSubstanceCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await drugSubstanceRepo.GetItemAsync(request.Id);

        if (await commentsRepo.ExistsAsync(x => x.CommentDrugSubstances.Any(s => s.DrugSubstanceId == entity.Id)))
        {
            logger.LogWarning("Drug Substance {DrugSubstanceId} has associated comments and cannot be deleted.", entity.Id);
            throw new AssociationExistsException("Drug Substance", entity.Id);
        }

        drugSubstanceRepo.DeleteItem(entity);
        await drugSubstanceRepo.SaveChangesAsync(userProvider);

        auditService.Log(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DRUG_SUBSTANCE_DELETED, AuditEventCategory.DRUG_SUBSTANCES,
            AuditEventDescription.DRUG_SUBSTANCE_DELETE, entity);

        return Result.Success();
    }
}
