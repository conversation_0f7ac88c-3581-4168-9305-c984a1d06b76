{"Logging": {"LogLevel": {"Default": "Information", "Grpc": "Information", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "NLog": {"autoReload": true, "internalLogLevel": "Info", "internalLogFile": "/app/logs/internal-nlog.log", "extensions": [{"assembly": "NLog.Web.AspNetCore"}], "targets": {"console-default": {"type": "<PERSON><PERSON><PERSON>", "layout": {"type": "JsonLayout", "includeMdlc": true, "attributes": [{"name": "time", "layout": "${longdate}"}, {"name": "level", "layout": "${uppercase:${level}}"}, {"name": "traceId", "layout": "${activityid:whenEmpty=${mdlc:item=RequestId:whenEmpty=${aspnet-TraceIdentifier}}}"}, {"name": "threadId", "layout": "${threadid}"}, {"name": "logger", "layout": "${logger}"}, {"name": "errorCode", "layout": "${event-properties:item=ErrorCode}"}, {"name": "errorClass", "layout": "${exception:format=Type}"}, {"name": "errorMessage", "layout": "${exception:format=Message}"}, {"name": "errorStack", "layout": "${exception:format=StackTrace}"}, {"name": "message", "layout": "${event-properties:item=Message:whenEmpty=${message}}"}, {"name": "applicationName", "layout": "${appdomain:format={1\\}}"}, {"name": "area", "layout": "${event-properties:item=Area:whenEmpty=${logger}}"}, {"name": "entity.name", "layout": "${gdc:item=entity.name}"}, {"name": "entity.type", "layout": "${gdc:item=entity.type}"}, {"name": "entity.guid", "layout": "${gdc:item=entity.guid}"}, {"name": "hostname", "layout": "${gdc:item=hostname}"}]}}, "filelog-default": {"type": "File", "fileName": "/app/logs/api-${shortdate}.log", "layout": "${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|CorrelationId=${aspnet-item:variable=CorrelationId}|ClientIP=${aspnet-item:variable=ClientIP}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}"}}, "rules": [{"logger": "*", "minlevel": "<PERSON><PERSON>", "writeTo": "filelog-default"}, {"logger": "*", "minlevel": "Info", "writeTo": "console-default"}, {"logger": "Microsoft.*", "minlevel": "Info", "final": true}]}}