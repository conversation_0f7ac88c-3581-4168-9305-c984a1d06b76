﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.ProductExtensions;

public static class DosageFormsTestEntitiesBuilder
{
    public static async Task<DosageForm[]> Build(HACommsContext dbContext, int numberOfItems = 1)
    {
        var dosageForms = new List<DosageForm>();
        for (var i = 0; i < numberOfItems; i++)
        {
            dosageForms.Add(CreateDosageFormsBuilder.Default().WithName(Fake.DosageForm.Name).Build());
        }

        await dbContext.DosageForms.AddRangeAsync(dosageForms);
        await dbContext.SaveChangesAsync();

        return dosageForms.ToArray();
    }
}
