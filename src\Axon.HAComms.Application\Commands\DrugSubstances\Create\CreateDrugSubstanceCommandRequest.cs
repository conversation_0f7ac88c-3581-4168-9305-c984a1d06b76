﻿using MediatR;
using System.ComponentModel.DataAnnotations;

#nullable enable

namespace Axon.HAComms.Application.Commands.DrugSubstances.Create
{
    public class CreateDrugSubstanceCommandRequest : IRequest<CreateDrugSubstanceCommandResponse>
    {
        public string Name { get; }
        [Required]
        public string Code { get; }
        public string Description { get; }

        public CreateDrugSubstanceCommandRequest(string name, string code, string description)
        {
            this.Name = name;
            this.Code = code;
            this.Description= description;
        }
    }
}
