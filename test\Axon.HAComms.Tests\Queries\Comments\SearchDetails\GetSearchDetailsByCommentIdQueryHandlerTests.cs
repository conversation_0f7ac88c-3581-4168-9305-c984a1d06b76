﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Models.Search;
using Axon.HAComms.Application.Queries.Comments.SearchDetails;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.Communications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore.Query;
using MockQueryable.NSubstitute;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Comments.SearchDetails;

public class GetSearchDetailsByCommentIdQueryHandlerTests
{
    private readonly GetSearchDetailsByCommentIdQueryHandler handler;
    private readonly ICommunicationsRepository communicationsRepository;
    private readonly ICommentsRepository commentsRepository;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public GetSearchDetailsByCommentIdQueryHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommunicationsMappingProfile());
            cfg.AddProfile(new CommentsMappingProfile());
        });

        var mapper = mockMapper.CreateMapper();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();
        communicationsRepository = Substitute.For<ICommunicationsRepository>();
        commentsRepository = Substitute.For<ICommentsRepository>();
        handler = new GetSearchDetailsByCommentIdQueryHandler(communicationsRepository, commentsRepository, mapper,
            correlationIdProvider, clientDetailsProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsItem()
    {
        // Arrange
        var communication = new CommunicationsBuilder().Build();
        var communicationId = Fake.Communication.Id;
        var comment1 = new Comment(Fake.Comment.Id)
        {
            CommunicationId = communicationId,
            ProductExtensions = new List<ProductExtension>() { new ProductExtension() { Product = new Product(Fake.Product.Id) } },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };
        var comment2 = new Comment(Fake.Comment.Id)
        {
            CommunicationId = communicationId,
            ProductExtensions = new List<ProductExtension>() { new ProductExtension() { Product = new Product(Fake.Product.Id) } },
            Description = Fake.Comment.Description,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP
        };
        var comments = new List<Comment> { comment1, comment2 };
        communicationsRepository.GetItemAsync(communicationId).Returns(communication);
        commentsRepository.GetItemWithAllIncludesAsync(comment1.Id).Returns(comment1);
        commentsRepository.GetFilteredComments(include: Arg.Any<Func<IQueryable<Comment>, IIncludableQueryable<Comment, object>>>(), filter: Arg.Any<Expression<Func<Comment, bool>>>()).Returns(comments.BuildMock());

        var request = new GetSearchDetailsByCommentIdQueryRequest(communicationId, comment1.Id);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        auditService
           .ReceivedWithAnyArgs(1)
           .Log(Guid.Empty, default, default, default, default, default, false);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.SEARCH_RESULT_VIEWED, AuditEventCategory.SEARCH, AuditEventDescription.SEARCH_OPEN_RESULT,
                Arg.Any<SearchDetailsModel>());
    }
}
