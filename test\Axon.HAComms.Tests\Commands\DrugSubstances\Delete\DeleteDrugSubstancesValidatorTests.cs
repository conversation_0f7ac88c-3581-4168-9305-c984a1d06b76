﻿using Axon.HAComms.Application.Commands.DrugSubstances.Delete;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DrugSubstances.Delete
{
    public class DeleteDrugSubstancesValidatorTests
    {
        private readonly DeleteDrugSubstanceCommandValidator sut;

        public DeleteDrugSubstancesValidatorTests()
        {
            sut = new DeleteDrugSubstanceCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var result = sut.TestValidate(new DeleteDrugSubstanceCommandRequest(0));
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var result = sut.TestValidate(new DeleteDrugSubstanceCommandRequest(1));
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
