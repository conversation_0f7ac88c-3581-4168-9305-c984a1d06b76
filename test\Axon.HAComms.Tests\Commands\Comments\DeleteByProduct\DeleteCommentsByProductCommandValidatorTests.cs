﻿using Axon.HAComms.Application.Commands.Comments.DeleteByProduct;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Comments.DeleteByProduct;

public class DeleteCommentsByProductCommandValidatorTests
{
    private readonly DeleteCommentsByProductCommandValidator sut = new();

    [Fact]
    public void Validate_ValidRequest_DoesNotThrowException()
    {
        // Arrange
        var request = new DeleteCommentsByProductCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            ProductId = Fake.Product.Id,
        };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_CommunicationIdIsEmpty_ThrowsException()
    {
        // Arrange
        var request = new DeleteCommentsByProductCommandRequest() { ProductId = Fake.Product.Id };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.CommunicationId);
        result.Errors.First().ErrorMessage.Should().Contain("'Communication Id' must not be empty.");
    }

    [Fact]
    public void Validate_ProductIdIsEmpty_ThrowsException()
    {
        // Arrange
        var request = new DeleteCommentsByProductCommandRequest() { CommunicationId = Fake.Communication.Id };

        // Act
        var result = sut.TestValidate(request);

        //Assert
        result.ShouldHaveValidationErrorFor(x => x.ProductId);
        result.Errors.First().ErrorMessage.Should().Contain("'Product Id' must not be empty.");
    }
}
