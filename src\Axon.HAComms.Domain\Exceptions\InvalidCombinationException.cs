﻿namespace Axon.HAComms.Domain.Exceptions;

public class InvalidCombinationException : Exception
{
    public InvalidCombinationException() { }
    public InvalidCombinationException(string message) : base(message) { }
    public InvalidCombinationException(string message, Exception innerException) : base(message, innerException)
    { }

    public InvalidCombinationException(string name, object key)
        : base($"Entity with \"{name}\" = ({key}) is not a valid combination.")
    {
    }
}
