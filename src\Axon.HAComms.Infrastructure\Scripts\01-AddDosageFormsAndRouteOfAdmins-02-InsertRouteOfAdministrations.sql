DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

INSERT INTO [dbo].[RouteOfAdministrations] (Name, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
VALUES 
(N'RECTAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROPHARYNGEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPERICARDIAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRASTERNAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUBRETINAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EXTRAPLEURAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'TOPICAL APPLICATION ON CORNEAL AND LIMBAL AREA OF THE EYE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS SLOW BOLUS INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUBCUTANEOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OCULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Eye Area', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ENDOSINUSIAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PERIOSSEOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAEPIDERMAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PERITUMORAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PRESERVATIVE SOLUTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EXTRACORPOREAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'HAEMODIALYSIS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'IMPLANTATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OROMUCOSAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACARTILAGINOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SLOW INTRAVENOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PERINEURAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPERITONEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'DIRECT INTRAVENOUS INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACORNEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'TOPICAL APPLICATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAMUSCULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACEREBRAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRA SEROSA', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRALESIONAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAOCULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL AND IV', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACEREBROVENTRICULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPUTAMINAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS/SUBCUTANEOUS/INTRAMUSCULAR AND ORAL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACERVICAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'CONCENTRATE FOR SOLUTION FOR INFUSION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPORTAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRADERMAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPROSTATIC USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PARENTERAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Diaper/Nappy area', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTUBATION SURFACTANT EXTUBATION (INSURE)', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GINGIVAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ENDOTRACHEOPULMONARY USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACAVERNOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVITREAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRABURSAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INFILTRATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRATYMPANIC', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS USE AND ORAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SUB RETINAL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTESTINAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAUTERINE USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRADISCAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'IONTOPHORESIS', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ENDOCERVICAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAGLANDULAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EPIDURAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ORAL', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'LARYNGOPHARYNGEAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'ROUTE OF ADMINISTRATION NOT APPLICABLE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'RETROBULBAR USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'SKIN SCARIFICATION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAVENOUS INJECTION', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Hair', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRACORONARY USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'PERIODONTAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'OPHTHALMIC', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'Hand', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAOSSEOUS USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRAPLEURAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'VAGINAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'EPILESIONAL USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'GASTRIC USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted),
(N'INTRALYMPHATIC USE', getdate(), @hacommsuser , getdate(), @hacommsuser , @deleted);