﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Commands.Communications.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Applications;
using Axon.HAComms.Tests.Builders.DrugSubstances;
using Axon.HAComms.Tests.Builders.Tags;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Create;

public class CreateCommunicationCommandHandlerTests
{
    private readonly CreateCommunicationCommandHandler handler;
    private readonly ICommunicationsRepository communicationRepo;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IUserProvider userProvider;
    private readonly IAuditService auditService;

    public CreateCommunicationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new CommunicationsMappingProfile());
            cfg.AddProfile(new CommentsMappingProfile());
            cfg.AddProfile(new ApplicationsMappingProfile());
            cfg.AddProfile(new SubmissionsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        communicationRepo = Substitute.For<ICommunicationsRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        var logger = Substitute.For<ILogger<CreateCommunicationCommandHandler>>();
        var communicationService = Substitute.For<ICommunicationService>();
        handler = new CreateCommunicationCommandHandler(communicationService, communicationRepo, commentsRepo, mapper, logger, correlationIdProvider,
            clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequestWithOneComment_ReturnsSuccessResult()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.SubmissionType.Id;
        var countryId = Fake.Communication.CountryId;
        var application1 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var application2 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(4).ToArray();

        var commentRequest = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } }
            },
            DrugSubstanceIds = drugSubstances.Select(x => x.Id).ToList(),
            TagIds = tags.Select(x => x.Id).ToList()
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application1, application2],
            commentRequest);

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.COMMUNICATION_CREATED,
                AuditEventCategory.COMMUNICATIONS,
                AuditEventDescription.COMMUNICATION_CREATE,
                Arg.Any<Communication>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        auditService
            .When(a => a.LogAsync(correlationId,
                clientDetails,
                AuditEventType.COMMENT_CREATED,
                AuditEventCategory.COMMENTS,
                AuditEventDescription.COMMENT_CREATE,
                Arg.Any<Comment>(),
                Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        communicationRepo.ReceivedWithAnyArgs(1).AddItem(default!);
        communicationRepo.Received(1).AddItem(Arg.Is<Communication>(c =>
            c.Subject == subject && c.DateOfCommunication == dateOfCommunication && c.SubmissionTypeId == submissionTypeId && c.CountryId == countryId &&
            c.Applications.Count == 2));
        await communicationRepo.Received(1).SaveChangesAsync(userProvider);

        commentsRepo.ReceivedWithAnyArgs(1).AddItem(default!);
        await commentsRepo.Received(1).SaveChangesAsync(userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequestWithOneComment_LogsAudit()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.SubmissionType.Id;
        var countryId = Fake.Communication.CountryId;
        var application1 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var application2 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();

        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(4).ToArray();

        var commentRequest = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Description = Fake.Comment.Description,
            IsQuestionIncluded = false,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new()
                {
                    ProductExtensionId = Fake.ProductExtension.Id,
                    RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id }
                }
            },
            DrugSubstanceIds = drugSubstances.Select(x => x.Id).ToList(),
            TagIds = tags.Select(x => x.Id).ToList()
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application1, application2],
            commentRequest);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(2)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.COMMUNICATION_CREATED, AuditEventCategory.COMMUNICATIONS,
            AuditEventDescription.COMMUNICATION_CREATE, Arg.Any<Communication>(), Arg.Any<Func<Task>>());
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.COMMENT_CREATED, AuditEventCategory.COMMENTS,
            AuditEventDescription.COMMENT_CREATE, Arg.Any<Comment>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public async Task Handle_ValidRequestWithOneCommentWithQuestionResponse_ReturnsSuccessResult()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.SubmissionType.Id;
        var countryId = Fake.Communication.CountryId;
        var application1 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
        var application2 = ApplicationModelBuilder.Default().WithSubmission(x => x.Build()).Build();
       
        var drugSubstances = TestEntitiesGenerator<DrugSubstance, DrugSubstancesBuilder>.Generate(3).ToArray();
        var tags = TestEntitiesGenerator<Tag, TagBuilder>.Generate(4).ToArray();

        var commentRequest = new CreateCommentCommandRequest()
        {
            CommunicationId = Fake.Communication.Id,
            Question = Fake.Comment.Question,
            Response = Fake.Comment.Response,
            BIRDSLinkToBIResponse = Fake.Comment.BIRDSLinkToBIResponse,
            BIRDSLinkToBISAMP = Fake.Comment.BIRDSLinkToBISAMP,
            IsQuestionIncluded = true,
            IsGeneralGuidance = false,
            ProductExtensions = new List<ProductExtensionCommentModel>()
            {
                new() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = new List<int> { Fake.RouteOfAdministration.Id } }
            },
            DrugSubstanceIds = drugSubstances.Select(x => x.Id).ToList(),
            TagIds = tags.Select(x => x.Id).ToList()
        };

        var request = new CreateCommunicationCommandRequest(
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application1, application2],
            commentRequest);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
    }
}
