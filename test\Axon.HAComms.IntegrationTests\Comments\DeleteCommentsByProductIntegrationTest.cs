﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteCommentsByProductIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteComments_CommunicationWithMultipleProducts_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct1 = product1.ProductExtensions.First();
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var createCommunicationRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createCommunicationRequest);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        // Comment 2, product 1
        var createCommentRequest2 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse1 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest2);
        commentResponse1.Should().NotBeNull();
        commentResponse1.Id.Should().NotBe(0);

        // Comment 3, product 2
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct2 = product2.ProductExtensions.First();
        var createCommentRequest3 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse2 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest3);
        commentResponse2.Should().NotBeNull();
        commentResponse2.Id.Should().NotBe(0);

        // Comment 4, product 2
        var createCommentRequest4 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse3 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest4);
        commentResponse3.Should().NotBeNull();
        commentResponse3.Id.Should().NotBe(0);

        //Act
        var deleteRequest = new DeleteCommentsByProductCommandRequest { CommunicationId = communicationId, ProductId = product2.Id };
        await commentApi.DeleteCommentsByCommunicationAndProductAsync(TenantConstants.DEFAULT_TENANT, deleteRequest);

        //Assert
        var commentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);
        commentsResponse.Should().NotBeNull();
        commentsResponse.Data.Count.Should().Be(2);
        commentsResponse.Data[0].ProductExtensions[0].ProductId.Should().Be(product1.Id);
        commentsResponse.Data[1].ProductExtensions[0].ProductId.Should().Be(product1.Id);

        var commentsResponse2 = await commentApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product2.Id);
        commentsResponse2.Should().NotBeNull();
        commentsResponse2.Data.Count.Should().Be(0);
    }

    [Fact]
    public async Task DeleteComments_CommunicationWithSingleProduct_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct1 = product1.ProductExtensions.First();
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var createCommunicationRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createCommunicationRequest);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        // Comment 2, product 1
        var createCommentRequest2 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse1 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest2);
        commentResponse1.Should().NotBeNull();
        commentResponse1.Id.Should().NotBe(0);

        //Act
        var deleteRequest = new DeleteCommentsByProductCommandRequest { CommunicationId = communicationId, ProductId = product1.Id };
        var deleteCommentResponse = () => commentApi.DeleteCommentsByCommunicationAndProductAsync(TenantConstants.DEFAULT_TENANT, deleteRequest);

        //Assert
        var exception = await deleteCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Cannot delete the last product for a communication.");
    }

    [Fact]
    public async Task DeleteComments_CommunicationWithSingleProductAndGeneralGuidanceComments_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct1 = product1.ProductExtensions.First();
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var createCommunicationRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createCommunicationRequest);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        // Comment 2, product 1
        var createCommentRequest2 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse1 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest2);
        commentResponse1.Should().NotBeNull();
        commentResponse1.Id.Should().NotBe(0);

        var createCommentRequest3 = CreateCommentCommandRequestBuilder.Default()
           .AsCommentOnly()
           .AsGeneralGuidance()
           .WithCommunicationId(communicationId)
           .WithDescription(Fake.Comment.Description)
           .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse2 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest3);
        commentResponse2.Should().NotBeNull();
        commentResponse2.Id.Should().NotBe(0);

        //Act
        var deleteRequest = new DeleteCommentsByProductCommandRequest { CommunicationId = communicationId, ProductId = product1.Id };
        await commentApi.DeleteCommentsByCommunicationAndProductAsync(TenantConstants.DEFAULT_TENANT, deleteRequest);

        //Assert
        var commentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        commentsResponse.Should().NotBeNull();
        commentsResponse.Data.Count.Should().Be(1);
        commentsResponse.Data[0].IsGeneralGuidance.Should().BeTrue();
        commentsResponse.Data[0].ProductExtensions.Should().BeNullOrEmpty();

    }

    [Fact]
    public async Task DeleteComments_CommunicationWithMultipleProducts_InvalidProductId_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct1 = product1.ProductExtensions.First();
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var createCommunicationRequest = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, createCommunicationRequest);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        // Comment 2, product 1
        var createCommentRequest2 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct1.Id, RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse1 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest2);
        commentResponse1.Should().NotBeNull();
        commentResponse1.Id.Should().NotBe(0);

        // Comment 3, product 2
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtensionProduct2 = product2.ProductExtensions.First();
        var createCommentRequest3 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse2 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest3);
        commentResponse2.Should().NotBeNull();
        commentResponse2.Id.Should().NotBe(0);

        // Comment 4, product 2
        var createCommentRequest4 = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communicationId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse3 = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, createCommentRequest4);
        commentResponse3.Should().NotBeNull();
        commentResponse3.Id.Should().NotBe(0);

        //Act
        var deleteRequest = new DeleteCommentsByProductCommandRequest { CommunicationId = communicationId, ProductId = Fake.Product.Id };
        await commentApi.DeleteCommentsByCommunicationAndProductAsync(TenantConstants.DEFAULT_TENANT, deleteRequest);

        //Assert
        var commentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);
        commentsResponse.Should().NotBeNull();
        commentsResponse.Data.Count.Should().Be(2);
        commentsResponse.Data[0].ProductExtensions[0].ProductId.Should().Be(product1.Id);
        commentsResponse.Data[1].ProductExtensions[0].ProductId.Should().Be(product1.Id);

        var commentsResponse2 = await commentApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product2.Id);
        commentsResponse2.Should().NotBeNull();
        commentsResponse2.Data.Count.Should().Be(2);
        commentsResponse2.Data[0].ProductExtensions[0].ProductId.Should().Be(product2.Id);
        commentsResponse2.Data[1].ProductExtensions[0].ProductId.Should().Be(product2.Id);
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }
}
