﻿DECLARE @hacommsuser varchar(30) = N'<EMAIL>';
DECLARE @deleted bit = 'false';

INSERT INTO [CommentProductExtension] (CommentId, ProductExtensionId, CreatedDate,CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted) 
SELECT DISTINCT CommentId, ProductExtensionId, GETDATE(), @hacommsuser,GETDATE(), @hacommsuser, @deleted
FROM Comments
INNER JOIN CommentRoutesOfAdministration on CommentRoutesOfAdministration.CommentId=Comments.Id

INSERT INTO [CommentProductExtensionRoutesOfAdministration] (CommentProductExtensionId, RouteOfAdministrationId, CreatedDate,CreatedBy, LastUpdatedDate, LastUpdatedBy, IsDeleted)
SELECT CommentProductExtension.Id, CommentRoutesOfAdministration.RouteOfAdministrationId, GETDATE(), @hacommsuser,GETDATE(), @hacommsuser, @deleted
FROM  CommentProductExtension
INNER JOIN CommentRoutesOfAdministration on CommentRoutesOfAdministration.CommentId = CommentProductExtension.CommentId
