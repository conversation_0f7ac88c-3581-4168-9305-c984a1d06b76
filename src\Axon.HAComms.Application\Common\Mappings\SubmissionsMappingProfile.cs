﻿using AutoMapper;
using Axon.HAComms.Application.Models.Submission;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings
{
    public class SubmissionsMappingProfile : Profile
    {
        public SubmissionsMappingProfile()
        {
            CreateMap<Submission, SubmissionModel>();
            CreateMap<SubmissionModel, Submission>()
                .ForMember(dest => dest.Application, o => o.Ignore())
                .ForMember(dest => dest.ApplicationId, o => o.Ignore())
                .ForMember(dest => dest.Id, o => o.Ignore())
                .ForMember(dest => dest.IsDeleted, o => o.Ignore())
                .ForMember(dest => dest.CreatedDate, o => o.Ignore())
                .ForMember(dest => dest.CreatedBy, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
                .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
                .ForMember(dest => dest.Tenant, o => o.Ignore());
        }
    }
}
