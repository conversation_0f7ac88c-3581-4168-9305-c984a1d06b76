﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class DrugSubstance : MultiTenantEntity
{
    public DrugSubstance()
    {

    }

    internal DrugSubstance(int id)
    {
        this.Id = id;
    }
    public string? Name { get; set; }
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? ExternalId { get; set; }

    public ICollection<Product> Products { get; set; } = [];
    public ICollection<DrugSubstanceDrugProduct> DrugSubstanceProducts { get; set; } = [];
    public ICollection<Comment> Comments { get; set; } = new HashSet<Comment>();
}
