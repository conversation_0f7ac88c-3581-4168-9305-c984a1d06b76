﻿using Axon.HAComms.Application.Commands.Communications.Update;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Models.Application;
using Axon.HAComms.Application.Models.Submission;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.Applications;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Communications.Update;

public class UpdateCommunicationValidatorTests
{
    private readonly UpdateCommunicationCommandValidator sut;
    private readonly ISubmissionTypesRepository subTypeRepo;
    private readonly ICountriesRepository countryRepo;

    public UpdateCommunicationValidatorTests()
    {
        subTypeRepo = Substitute.For<ISubmissionTypesRepository>();
        countryRepo = Substitute.For<ICountriesRepository>();
        sut = new UpdateCommunicationCommandValidator(subTypeRepo, countryRepo);
    }

    [Fact]
    public void Validate_ValidRequest_DoesNotThrowException()
    {
        //Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new ApplicationModel()
        {
            Number = Fake.Application.Number,
            Submissions =
            [
                new SubmissionModel { Number = Fake.Submission.Number }
            ]
        };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application]);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_IdIsEmpty_ThrowsException()
    {
        // Arrange
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var applications = new List<ApplicationModel>();
        var countryId = Fake.Communication.CountryId;

        var request = new UpdateCommunicationCommandRequest(
            default,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Id).WithErrorMessage("'Id' must not be empty.");
    }

    [Fact]
    public void Validate_SubjectIsEmpty_ThrowsException()
    {
        //Arrange
        var id = Fake.Communication.Id;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();

        var request = new UpdateCommunicationCommandRequest(
            id,
            string.Empty,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Subject).WithErrorMessage("'Subject' must not be empty.");
    }

    [Fact]
    public void Validate_DateOfCommunicationIsEmpty_ThrowsException()
    {
        //Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            default,
            submissionTypeId,
            countryId,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.DateOfCommunication).WithErrorMessage("'Date Of Communication' must not be empty.");
    }

    [Fact]
    public void Validate_SubmissionTypeIsEmpty_ThrowsException()
    {
        //Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>();

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            default,
            countryId,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SubmissionTypeId).WithErrorMessage("'Submission Type Id' must not be empty.");
    }

    [Fact]
    public void Validate_CountryIsEmpty_ThrowsException()
    {
        //Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var applications = new List<ApplicationModel>();

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            default,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CountryId).WithErrorMessage("'Country Id' must not be empty.");
    }

    [Fact]
    public void Validate_ApplicationNumberIsEmpty_ThrowsException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>() { new() };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Number").WithErrorMessage("'Number' must not be empty.");
    }

    [Fact]
    public void Validate_ApplicationsLessThanOrEqualToFive_DoesNotThrowException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = TestEntitiesGenerator<ApplicationModel, ApplicationModelBuilder>.Generate(3);

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_NoApplications_DoesNotThrowException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            []);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_FiveApplications_DoesNotThrowException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = TestEntitiesGenerator<ApplicationModel, ApplicationModelBuilder>.Generate(5);

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_MoreThanFiveApplications_ThrowException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = TestEntitiesGenerator<ApplicationModel, ApplicationModelBuilder>.Generate(6);

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications.Count").WithErrorMessage("'Applications Count' must be less than or equal to '5'.");
    }

    [Fact]
    public void Validate_ApplicationNumberDuplicateValues_ThrowException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application1 = new ApplicationModel()
        {
            Number = Fake.Application.Number
        };

        var application2 = new ApplicationModel()
        {
            Number = application1.Number
        };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            [application1, application2]);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications").WithErrorMessage("Duplicate Application Numbers are not allowed.");
    }

    [Fact]
    public void Validate_ApplicationNumberContainingIllegalCharacters_ThrowsException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var application = new List<ApplicationModel>()
        {
            new()
            {
                Number = Constants.ILLEGAL_CHARACTERS[0].ToString(),
            }
        };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            application);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Number").WithErrorMessage("Application Number cannot contain illegal characters.");
    }

    [Fact]
    public void Validate_SubmissionNumberIsEmpty_ThrowsException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>()
        {
            new()
            {
                Number = Fake.Application.Number,
                Submissions =
                [
                    new SubmissionModel()
                ]
            }
        };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Submissions[0].Number").WithErrorMessage("'Number' must not be empty.");
    }

    [Fact]
    public void Validate_SubmissionNumberContainingIllegalCharacter_ThrowsException()
    {
        // Arrange
        var id = Fake.Communication.Id;
        var subject = Fake.Communication.Subject;
        var dateOfCommunication = DateTime.Now;
        var submissionTypeId = Fake.Communication.SubmissionTypeId;
        var countryId = Fake.Communication.CountryId;
        var applications = new List<ApplicationModel>()
        {
            new()
            {
                Number = Fake.Application.Number,
                Submissions = new List<SubmissionModel>()
                {
                    new() {
                        Number = Constants.ILLEGAL_CHARACTERS[0].ToString()
                    }
                }
            }
        };

        var request = new UpdateCommunicationCommandRequest(
            id,
            subject,
            dateOfCommunication,
            submissionTypeId,
            countryId,
            applications);

        countryRepo.ExistsAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(true);
        subTypeRepo.ExistsAsync(Arg.Any<Expression<Func<SubmissionType, bool>>>()).Returns(true);

        // Act
        var result = sut.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor("Applications[0].Submissions[0].Number").WithErrorMessage("Submission Number cannot contain illegal characters.");
    }
}
