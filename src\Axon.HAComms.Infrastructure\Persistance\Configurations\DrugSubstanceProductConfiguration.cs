﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class DrugSubstanceProductConfiguration : IEntityTypeConfiguration<DrugSubstanceDrugProduct>
{
    public void Configure(EntityTypeBuilder<DrugSubstanceDrugProduct> builder)
    {
        builder.ToTable("DrugSubstanceProducts");

        builder.HasKey(x => new { x.DrugSubstancesId, x.ProductsId });
    }
}
