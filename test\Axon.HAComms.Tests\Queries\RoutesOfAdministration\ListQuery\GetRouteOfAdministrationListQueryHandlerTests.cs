﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.RoutesOfAdministration.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.RoutesOfAdministration.ListQuery;

public class GetRouteOfAdministrationListQueryHandlerTests
{
    private readonly GetRouteOfAdministrationListQueryHandler handler;
    private readonly IRouteOfAdministrationRepository routeOfAdministrationRepo;

    public GetRouteOfAdministrationListQueryHandlerTests()
    {
        routeOfAdministrationRepo = Substitute.For<IRouteOfAdministrationRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetRouteOfAdministrationListQueryHandler(routeOfAdministrationRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = new List<RouteOfAdministration>
        {
            new RouteOfAdministrationBuilder().Build(),
            new RouteOfAdministrationBuilder().Build(),
            new RouteOfAdministrationBuilder().Build()
        };

        routeOfAdministrationRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetRouteOfAdministrationListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        var routeOfAdministrationName1 = "test1";
        var routeOfAdministrationName2 = "test2";
        var routeOfAdministrationName3 = "test3";

        //Arrange
        var queryableItems = new List<RouteOfAdministration>
        {
            new RouteOfAdministrationBuilder().WithName(routeOfAdministrationName3).Build(),
            new RouteOfAdministrationBuilder().WithName(routeOfAdministrationName2).Build(),
            new RouteOfAdministrationBuilder().WithName(routeOfAdministrationName1).Build()
        };

        routeOfAdministrationRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetRouteOfAdministrationListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data[0].Name.Should().Be(routeOfAdministrationName1);
        result.Data[1].Name.Should().Be(routeOfAdministrationName2);
        result.Data[2].Name.Should().Be(routeOfAdministrationName3);
    }
}
