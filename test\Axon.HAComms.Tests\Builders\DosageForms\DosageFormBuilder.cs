﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.DosageForms
{
    public class DosageFormBuilder : IBuilder<DosageForm>
    {
        private readonly int id = Fake.DosageForm.Id;
        private string name = Fake.DosageForm.Name;
        private DateTime createdDate = DateTime.Now;
        private DateTime lastUpdatedDate = DateTime.Now;
        private string lastUpdatedBy = Fake.DosageForm.LastUpdatedBy;
        private string createdBy = Fake.DosageForm.CreatedBy;

        public DosageFormBuilder WithName(string name)
        {
            this.name = name;
            return this;
        }

        public DosageFormBuilder WithCreatedDate(DateTime createdDate)
        {
            this.createdDate = createdDate;
            return this;
        }

        public DosageFormBuilder WithCreatedBy(string createdBy)
        {
            this.createdBy = createdBy;
            return this;
        }

        public DosageFormBuilder WithLastUpdatedDate(DateTime lastUpdatedDate)
        {
            this.lastUpdatedDate = lastUpdatedDate;
            return this;
        }

        public DosageFormBuilder WithLastUpdatedBy(string lastUpdatedBy)
        {
            this.lastUpdatedBy = lastUpdatedBy;
            return this;
        }

        public DosageForm Build()
        {
            return new(id)
            {
                Name = name,
                CreatedDate = createdDate,
                LastUpdatedDate = lastUpdatedDate,
                LastUpdatedBy = lastUpdatedBy,
                CreatedBy = createdBy,
            };
        }
    }
}
