﻿using AutoFixture;

namespace Axon.HAComms.Tests.Common
{
    public static class Fake
    {
        public static readonly Fixture fixture = new();
        private static Random random = new Random();

        public static string Tenant => Fake.GetRandomString(10);
        public static class DrugSubstance
        {
            public static int Id => fixture.Create<int>();
            public static string Code => GetRandomString(30);
            public static string Name => GetRandomString(100);
            public static string Description => fixture.Create<string>();
            public static string LastUpdatedBy => fixture.Create<string>();
        }

        public static class Product
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(30);
            public static bool IsActive => fixture.Create<bool>();
            public static List<int> DrugSubstanceIds => fixture.Create<List<int>>();
            public static List<int> ProductTypeIds => fixture.Create<List<int>>();
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class ProductExtension
        {
            public static int Id => fixture.Create<int>();
            public static string PCID => GetRandomString(15);
            public static bool IsActive => fixture.Create<bool>();
            public static bool IsMain => fixture.Create<bool>();
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class DosageForm
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class RouteOfAdministration
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class SubmissionType
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class Country
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
            public static string Region => fixture.Create<string>();
        }

        public static class Company
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
            public static int AxonCompanyId => fixture.Create<int>();
            public static string Description => fixture.Create<string>();
        }

        public static class Comment
        {
            public static int Id => fixture.Create<int>();
            public static bool IsGeneralGuidance => fixture.Create<bool>();
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
            public static string Description => fixture.Create<string>();
            public static string Question => fixture.Create<string>();
            public static string Response => fixture.Create<string>();
            public static int ProductExtensionId => fixture.Create<int>();
            public static string BIRDSLinkToBIResponse => "http://testUrl.com/" + GetRandomString(150);
            public static string BIRDSLinkToBISAMP => "http://anotherTestUrl.com/" + GetRandomString(150);
            public static List<int> DrugSubstanceIds => fixture.Create<List<int>>();
            public static List<int> ProductTypeIds => fixture.Create<List<int>>();
            public static List<int> RouteOfAdministrationIds => fixture.Create<List<int>>();
            public static List<int> TagIds => fixture.Create<List<int>>();
            public static bool IsDeleted => fixture.Create<bool>();
        }

        public static class Communication
        {
            public static int Id => fixture.Create<int>();
            public static string Subject => GetRandomString(100);
            public static bool IsCompleted => fixture.Create<bool>();
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
            public static int SubmissionTypeId => fixture.Create<int>();
            public static int CountryId => fixture.Create<int>();
        }

        public static class Tag
        {
            public static int Id => fixture.Create<int>();
            public static string Description => GetRandomString(200);
            public static string Name => GetRandomString(50);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class Application
        {
            public static int Id => fixture.Create<int>();
            public static string Number => fixture.Create<string>();
        }
        public static class Submission 
        {
            public static int Id => fixture.Create<int>();
            public static string Number => fixture.Create<string>();
        }

        public static class ProductType
        {
            public static int Id => fixture.Create<int>();
            public static string Name => GetRandomString(100);
            public static string LastUpdatedBy => fixture.Create<string>();
            public static string CreatedBy => fixture.Create<string>();
        }

        public static class SearchSettings
        {
            public static string ServiceName => fixture.Create<string>();
            public static string ApiKey => fixture.Create<string>();
            public static string IndexName => fixture.Create<string>();
            public static string IndexerName => fixture.Create<string>();
            public static string DataSourceName => fixture.Create<string>();
            public static string TableOrView => fixture.Create<string>();
            public static double Interval => fixture.Create<double>();
            public static string HighWatermarkColumn => fixture.Create<string>();
            public static string SoftDeleteColumn => fixture.Create<string>();
            public static string SoftDeleteMarker => fixture.Create<string>();
        }

        public static class DbSettings
        {
            public static string Default => fixture.Create<string>();
        }

        public static string GetRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public static int GetRandomInt(int from, int to)
        {
            return new Random().Next(from, to);
        }
    }
}
