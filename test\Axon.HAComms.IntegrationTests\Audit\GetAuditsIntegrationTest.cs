﻿using System.Net;
using AutoFixture;
using Axon.HAComms.IntegrationTests.Infrastructure;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Audit;

[Collection(TestCollectionIDs.IntegrationTests)]

public class GetAuditsIntegrationTest(ApiTestFixture fixture)
{
    [Fact]
    public async Task GetAudits_ReturnsOk()
    {
        fixture.AddHeaders("<EMAIL>");
        
        var response = await fixture.ApiClient.GetAsync("/pharmalex/v1/odata/Audits?$top=19&$orderby=StartDate");

        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
