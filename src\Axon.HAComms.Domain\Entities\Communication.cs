﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class Communication : MultiTenantEntity
{
    public Communication()
    {

    }

    public Communication(int id)
    {
        this.Id = id;
    }
    /// <summary>
    /// Subject
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// Country
    /// </summary>
    public Country? Country { get; set; }

    /// <summary>
    /// Country Id
    /// </summary>
    public int CountryId { get; set; }

    /// <summary>
    /// Date of communication
    /// </summary>
    public DateTime DateOfCommunication { get; set; }

    /// <summary>
    /// Submission Type
    /// </summary>
    public SubmissionType? SubmissionType { get; set; }

    /// <summary>
    /// Submission Type Id
    /// </summary>
    public int SubmissionTypeId { get; set; }

    /// <summary>
    /// Is completed communication
    /// </summary>
    public bool IsCompleted { get; set; }

    public int? ExternalId { get; set; }

    /// <summary>
    /// Comments
    /// </summary>
    public ICollection<Comment> Comments { get; set; } = new HashSet<Comment>();

    public ICollection<Application> Applications { get; set; } = new HashSet<Application>();


    public void Complete()
    {
        IsCompleted = true;
    }

    public void Reinstate()
    {
        IsCompleted = false;
    }
}
