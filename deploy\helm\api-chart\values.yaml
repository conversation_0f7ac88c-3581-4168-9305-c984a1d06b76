image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: latest

nameOverride: "axon-hacomms-api"
fullnameOverride: "axon-hacomms-api"

ingress:
  tls:
    - tlsSecretName: tls-app-dev-smartphlex-com
      hosts:
        - app-dev.smartphlex.com
  hosts:
    - host: app-dev.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)

resources:
  limits:
    cpu: 2000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

keyVaultName: hac-dev-kv-eun
aspNetCoreEnvironment: Production
azureSearch:
  isEnabled: true
  serviceName: hac-dev-ss-eun
  Interval: 5

azureWorkload:
  clientId: 02263ef5-d9c6-43f2-9129-f3789c89354b
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: "86400" # Token is valid for 1 day

newreliclicensekey: "#{newrelic-phlexglobal-api-key}#"


TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
GrpcHost: "http://axon-core-api-grpc.axon-core.svc.cluster.local:9090"
AppName: "hacomms"

nodeSelector:
  kubernetes.io/os: linux

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - NET_RAW
      - ALL
  runAsNonRoot: true
  runAsUser: 65534
  runAsGroup: 65534

podSecurityContext:
  seccompProfile:
    type: RuntimeDefault
  runAsUser: 65534
  runAsGroup: 65534
  runAsNonRoot: true
