﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Queries.Audit.GetEventConstantsQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/audits")]
[Tags("Audits")]
public class AuditController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Gets the event category and event type constants.
    /// </summary>
    /// <response code="200">List of event categories and event types.</response>
    /// <response code="401">The user not authenticated.</response>
    /// <response code="403">The user not authorised.</response>
    /// <response code="500">System error.</response>
    [HasPermissions(nameof(HacommsPermissions.ViewAudit))]
    [HttpGet("eventConstants", Name = "GetEventConstants")]
    [SwaggerOperation("GetEventConstants")]
    [ProducesResponseType(200, Type = typeof(GetEventConstantsQueryResponse))]
    [ProducesResponseType(401, Type = typeof(ErrorResult))]
    [ProducesResponseType(403, Type = typeof(ErrorResult))]
    [ProducesResponseType(500, Type = typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetAsync()
    {
        var filtersQueryResponse = await Mediator.Send(new GetEventConstantsQueryRequest());
        return Ok(filtersQueryResponse);
    }
}
