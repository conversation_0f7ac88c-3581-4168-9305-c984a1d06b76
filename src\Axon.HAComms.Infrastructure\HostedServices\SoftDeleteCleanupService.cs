using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Infrastructure.Persistance;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Infrastructure.HostedServices;

public class SoftDeleteCleanupService : BackgroundService
{
    private readonly ILogger<SoftDeleteCleanupService> logger;
    private readonly IEmailService emailService;
    private readonly IServiceProvider serviceProvider;
    private readonly TimeSpan interval;
    private readonly string environment;
    private readonly string receivers;

    private static int _instanceCount = 0;
    private static int _executionCount = 0;
    private readonly int _instanceId;

    public SoftDeleteCleanupService(IServiceProvider serviceProvider, TimeSpan interval, string receivers, string environment)
    {
        logger = serviceProvider.GetRequiredService<ILogger<SoftDeleteCleanupService>>();
        emailService = serviceProvider.GetRequiredService<IEmailService>();
        this.serviceProvider = serviceProvider;
        this.interval = interval;
        this.environment = environment;
        this.receivers = receivers;
        _instanceId = Interlocked.Increment(ref _instanceCount);
        logger.LogWarning("NEW SoftDeleteCleanupService instance #{InstanceId} created at {Time}. Total instances: {TotalInstances}",
            _instanceId, DateTime.Now, _instanceCount);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var executionId = Interlocked.Increment(ref _executionCount);
        logger.LogWarning("ExecuteAsync started - Instance #{InstanceId}, Execution #{ExecutionId} at {Time}",
            _instanceId, executionId, DateTime.Now);

        try
        {
            logger.LogInformation("SoftDeleteCleanupService running - Instance #{InstanceId}", _instanceId);

            while (!stoppingToken.IsCancellationRequested)
            {
                logger.LogDebug("Starting cleanup iteration - Instance #{InstanceId}, Execution #{ExecutionId} at {Time}",
                    _instanceId, executionId, DateTime.Now);

                try
                {
                    using var scope = serviceProvider.CreateScope();
                    var dbContext = scope.ServiceProvider.GetRequiredService<HACommsContext>();
                    await CleanupSoftDeletedRows(dbContext);

                    logger.LogDebug("Cleanup completed - Instance #{InstanceId}, Execution #{ExecutionId} at {Time}",
                        _instanceId, executionId, DateTime.Now);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error during cleanup - Instance #{InstanceId}, Execution #{ExecutionId}",
                        _instanceId, executionId);

                    await emailService.Send(receivers.Split(";"),
                        $"Soft Delete Hosted Service Error for {environment} environment",
                        $"An error occurred while deleting soft-deleted communications and comments. Instance: {_instanceId}, Execution: {executionId}, Exception: {ex.Message}");
                }

                logger.LogDebug("Waiting {Interval} before next iteration - Instance #{InstanceId}",
                    interval, _instanceId);

                await Task.Delay(interval, stoppingToken);
            }
        }
        catch (OperationCanceledException ex)
        {
            // Expected during shutdown - log and handle gracefully
            logger.LogError(ex, "SoftDeleteCleanupService cancelled - Instance #{InstanceId}, Execution #{ExecutionId}",
                _instanceId, executionId);
        }
        catch (Exception ex)
        {
            // Unexpected error - log and rethrow with context
            logger.LogError(ex, "Unexpected error in ExecuteAsync - Instance #{InstanceId}, Execution #{ExecutionId}",
                _instanceId, executionId);

            throw new InvalidOperationException(
                $"SoftDeleteCleanupService failed unexpectedly. Instance: {_instanceId}, Execution: {executionId}",
                ex);
        }
        finally
        {
            logger.LogWarning("ExecuteAsync ending - Instance #{InstanceId}, Execution #{ExecutionId} at {Time}",
                _instanceId, executionId, DateTime.Now);
        }
    }

    private async Task CleanupSoftDeletedRows(HACommsContext dbContext)
    {
        var thresholdDate = DateTime.UtcNow.AddHours(-2);

        var deletedCommentsRows = DeleteComments(dbContext, thresholdDate);
        logger.LogInformation("{RowsDeleted} rows comments have been permanently deleted.", deletedCommentsRows);

        var deletedCommunicationsRows = DeleteCommunications(dbContext, thresholdDate);
        logger.LogInformation("{RowsDeleted} rows communications have been permanently deleted.", deletedCommunicationsRows);

        if (deletedCommunicationsRows is not 0 && deletedCommentsRows is not 0)
        {
            await emailService.Send(receivers.Split(";"), $"Soft Delete Hosted Service in HaComms for {environment} environment",
            $"Soft Delete Hosted Service was executed successfully. Deleted {deletedCommunicationsRows} communications and {deletedCommentsRows} comments.");
        }        
    }

    private static int DeleteCommunications(HACommsContext dbContext, DateTime thresholdDate)
    {
        var rowsDeleted = dbContext.Database.ExecuteSql($"DELETE FROM [dbo].[Communications] WHERE IsDeleted = 1 AND LastUpdatedDate <= {thresholdDate}");
        return rowsDeleted;
    }

    private static int DeleteComments(HACommsContext dbContext, DateTime thresholdDate)
    {
        var rowsDeleted = dbContext.Database.ExecuteSql($"DELETE FROM [dbo].[Comments] WHERE IsDeleted = 1 AND LastUpdatedDate <= {thresholdDate}");
        return rowsDeleted;
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("SoftDeleteCleanupService is stopping.");
        await base.StopAsync(cancellationToken);
    }
}
