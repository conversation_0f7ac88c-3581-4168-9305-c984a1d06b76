﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Delete;

public class DeleteRouteOfAdministrationCommandHandlerTests
{
    private readonly DeleteRouteOfAdministrationCommandHandler sut;
    private readonly IRouteOfAdministrationRepository routeOfAdministrationRepository;
    private readonly IProductExtensionsRepository productExtensionsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteRouteOfAdministrationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");

        routeOfAdministrationRepository = Substitute.For<IRouteOfAdministrationRepository>();
        productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        auditService = Substitute.For<IAuditService>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<DeleteRouteOfAdministrationCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        sut = new DeleteRouteOfAdministrationCommandHandler(routeOfAdministrationRepository, productExtensionsRepo, logger, correlationIdProvider, clientDetailsProvider,
            userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var entity = new RouteOfAdministration
        {
            Name = Fake.RouteOfAdministration.Name
        };

        routeOfAdministrationRepository.GetItemAsync(entity.Id).Returns(entity);
        productExtensionsRepo.ExistsAsync(Arg.Any<Expression<Func<ProductExtension, bool>>>()).Returns(false);

        var request = new DeleteRouteOfAdministrationCommandRequest(entity.Id);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, default, false);
        auditService.Received(1).Log(correlationId, clientDetails, AuditEventType.ROUTE_OF_ADMINISTRATION_DELETED, AuditEventCategory.ROUTES_OF_ADMINISTRATION,
            AuditEventDescription.ROUTE_OF_ADMINISTRATION_DELETE, Arg.Any<RouteOfAdministration>());
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new RouteOfAdministration
        {
            Name = Fake.RouteOfAdministration.Name
        };
        var request = new DeleteRouteOfAdministrationCommandRequest(entity.Id);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public void Handle_WithAssociatedProducts_ThrowsAssociationException()
    {
        // Arrange
        var entity = new RouteOfAdministration
        {
            Name = Fake.RouteOfAdministration.Name
        };
        var request = new DeleteRouteOfAdministrationCommandRequest(entity.Id);
        routeOfAdministrationRepository.GetItemAsync(entity.Id).Returns(entity);
        productExtensionsRepo.ExistsAsync(Arg.Any<Expression<Func<ProductExtension, bool>>>()).Returns(true);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
