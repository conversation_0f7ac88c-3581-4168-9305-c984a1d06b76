﻿using Axon.HAComms.Application.Common.Interfaces;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.Applications.ListQuery;

public class GetApplicationNumberListQueryHandler(IApplicationsRepository repo) : IRequestHandler<GetApplicationNumberListQueryRequest, ApiListResult<string>>
{
    public async Task<ApiListResult<string>> Handle(GetApplicationNumberListQueryRequest request, CancellationToken cancellationToken)
    {
        var applicationNumbers = await repo.GetNumbersAsync();
        return new ApiListResult<string>(applicationNumbers);
    }
}
