﻿using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.ProductExtensions;
using Axon.HAComms.Application.Models.Tags;

namespace Axon.HAComms.Application.Models.Comments;

public class CommentDtoModel
{
    public int Id { get; set; }

    public string? Description { get; set; }

    public string? Question { get; set; }

    public string? Response { get; set; }

    public string? BIRDSLinkToBIResponse { get; set; }

    public string? BIRDSLinkToBISAMP { get; set; }

    public List<ProductExtensionModel>? ProductExtensions { get; set; }

    public string? ProductName { get; set; }

    public ICollection<DrugSubstanceModel> DrugSubstances { get; set; } = [];

    public ICollection<string>? ProductTypes { get; set; }

    public ICollection<TagModel> Tags { get; set; } = [];

    public bool IsGeneralGuidance { get; set; }

    public bool IsQuestionIncluded { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastUpdatedDate { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public string LastUpdatedBy { get; set; } = string.Empty;


}
