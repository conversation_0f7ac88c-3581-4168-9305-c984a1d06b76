﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Products.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders.Products;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Products.Delete;

public class DeleteProductsCommandHandlerTests
{
    private readonly DeleteProductCommandHandler handler;
    private readonly IProductsRepository productsRepo;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteProductsCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");

        productsRepo = Substitute.For<IProductsRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        auditService = Substitute.For<IAuditService>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<DeleteProductCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        handler = new DeleteProductCommandHandler(productsRepo, commentsRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);

        var entity = new ProductsBuilder().WithId(productId).WithProductExtensions(productExtensionEntity).Build();
        var request = new DeleteProductCommandRequest(productId);

        productsRepo.GetItemAsync(entity.Id).Returns(entity);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, default, false);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.DRUG_PRODUCT_DELETED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_DELETE,
                Arg.Any<Product>());
    }

    [Fact]
    public async Task Handle_WithAssociatedComments_ThrowsEntityNotFoundException()
    {
        // Arrange
        var productId = Fake.Product.Id;

        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);
        var entity = new ProductsBuilder().WithId(productId).WithProductExtensions(productExtensionEntity).Build();

        productsRepo.GetItemAsync(entity.Id).Returns(entity);

        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(true);

        var request = new DeleteProductCommandRequest(productId);

        // Act
        var result = () => handler.Handle(request, CancellationToken.None);

        // Assert
        await result.Should().ThrowAsync<AssociationExistsException>();
    }
}

