﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class UpdateCommentIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task UpdateComment_NonGenericComment_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct2.Id,
                RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product2.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));
        updatedComment.DrugSubstances.Count.Should().Be(product2.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product2.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_CommentOnly_NoChanges_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithDescription(request.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(2);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtension.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtension.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtension.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Name));
        updatedComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct2.Id,
                RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product2.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeTrue();
        updatedComment.Question.Should().Be(updateCommentRequest.Question);
        updatedComment.Response.Should().Be(updateCommentRequest.Response);
        updatedComment.Description.Should().BeNull();
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));
        updatedComment.DrugSubstances.Count.Should().Be(product2.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product2.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_GenericComment_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeTrue();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions.Should().BeNullOrEmpty();
        updatedComment.DrugSubstances.Should().BeEmpty();
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_GenericComment_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithId(commentResponse.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeTrue();
        updatedComment.IsQuestionIncluded.Should().BeTrue();
        updatedComment.Question.Should().Be(updateCommentRequest.Question);
        updatedComment.Response.Should().Be(updateCommentRequest.Response);
        updatedComment.Description.Should().BeNull();
        updatedComment.ProductExtensions.Should().BeNullOrEmpty();
        updatedComment.DrugSubstances.Should().BeEmpty();
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_CommentOnly_To_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct2.Id,
                RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product2.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeTrue();
        updatedComment.Question.Should().Be(updateCommentRequest.Question);
        updatedComment.Response.Should().Be(updateCommentRequest.Response);
        updatedComment.Description.Should().BeNull();
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));
        updatedComment.DrugSubstances.Count.Should().Be(product2.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product2.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_QuestionAndResponse_To_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct2.Id,
                RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product2.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));
        updatedComment.DrugSubstances.Count.Should().Be(product2.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product2.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_GenericComment_CommentOnly_To_QuestionAndResponse_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithId(commentResponse.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeTrue();
        updatedComment.IsQuestionIncluded.Should().BeTrue();
        updatedComment.Question.Should().Be(updateCommentRequest.Question);
        updatedComment.Response.Should().Be(updateCommentRequest.Response);
        updatedComment.Description.Should().BeNull();
        updatedComment.ProductExtensions.Should().BeNullOrEmpty();
        updatedComment.DrugSubstances.Should().BeEmpty();
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_GenericComment_QuestionAndResponse_To_CommentOnly_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeTrue();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions.Should().BeNullOrEmpty();
        updatedComment.DrugSubstances.Should().BeEmpty();
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_InvalidCommentId_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var invalidCommentId = commentResponse.Id + int.MaxValue;
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(invalidCommentId)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct2.Id,
                RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = () => commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);

        //Assert
        var exception = await updateCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Comment\\\" ({invalidCommentId}) was not found.");
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_MultipleProductExtensions_AddNewExtension_To_Comment_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product1.ProductExtensions[1];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtensionProduct1.Id,
                RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(2);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();

        updatedComment.ProductExtensions.Count.Should().Be(2);
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct1.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct1.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct1.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should()
            .BeEquivalentTo(productExtensionProduct1.RoutesOfAdministration.Select(r => r.Name));

        updatedComment.ProductExtensions[1].Id.Should().Be(updateCommentRequest.ProductExtensions[1].ProductExtensionId);
        updatedComment.ProductExtensions[1].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[1].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[1].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[1].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[1].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[1].RouteOfAdministrations.Select(r => r.Name).Should()
            .BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));

        updatedComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_MultipleProductExtensions_RemoveExtension_From_Comment_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product1.ProductExtensions[1];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtensionProduct2.Id, RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();
        var updateCommentResponse = await commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);
        updateCommentResponse.Should().NotBeNull();
        updateCommentResponse.Id.Should().Be(commentResponse.Id);

        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(2);
        var updatedComment = getCommentsResponse.Data.Single(comment => comment.Id == updateCommentResponse.Id);
        updatedComment.IsGeneralGuidance.Should().BeFalse();
        updatedComment.IsQuestionIncluded.Should().BeFalse();
        updatedComment.Description.Should().Be(updateCommentRequest.Description);
        updatedComment.Question.Should().BeNull();
        updatedComment.Response.Should().BeNull();
        updatedComment.ProductExtensions.Count.Should().Be(1);
        updatedComment.ProductExtensions[0].Id.Should().Be(updateCommentRequest.ProductExtensions[0].ProductExtensionId);
        updatedComment.ProductExtensions[0].Pcid.Should().Be(productExtensionProduct2.Pcid);
        updatedComment.ProductExtensions[0].DosageFormId.Should().Be(productExtensionProduct2.DosageForm.Id);
        updatedComment.ProductExtensions[0].DosageFormName.Should().Be(productExtensionProduct2.DosageForm.Name);
        updatedComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(updateCommentRequest.ProductExtensions[0].RouteOfAdministrationIds);
        updatedComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtensionProduct2.RoutesOfAdministration.Select(r => r.Name));       
        updatedComment.DrugSubstances.Count.Should().Be(product1.DrugSubstances.Count);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Id).Should().BeEquivalentTo(updateCommentRequest.DrugSubstanceIds);
        updatedComment.DrugSubstances.Select(drugSubstance => drugSubstance.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(s => s.Name));
        updatedComment.BirdsLinkToBIResponse.Should().Be(updateCommentRequest.BirdsLinkToBIResponse);
        updatedComment.BirdsLinkToBISAMP.Should().Be(updateCommentRequest.BirdsLinkToBISAMP);
        updatedComment.Tags.Select(tag => tag.Id).Should().BeEquivalentTo(updateCommentRequest.TagIds);
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_MultipleProductExtensions_RemoveAllExtensions_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product1.ProductExtensions[1];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithProductExtensions()
            .WithDescription(Fake.Comment.Description)
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();

        var updateCommentResponse = () => commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);

        //Assert
        var exception = await updateCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"'Product Extensions' must not be empty.");
               
    }

    [Fact]
    public async Task UpdateComment_NonGenericComment_MultipleProductExtensions_AddNewProductExtensions_ForDifferentProduct_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product1.ProductExtensions[1];
        var productExtensionProduct22 = product2.ProductExtensions[0];

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct22.Id,
                    RouteOfAdministrationIds = productExtensionProduct22.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDescription(Fake.Comment.Description)
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();

        var updateCommentResponse = () => commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);

        //Assert
        var exception = await updateCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Product Extensions per comment must be for the same product.");

    }
    [Fact]
    public async Task UpdateComment_NonGenericComment_MultipleProductExtensions_WithDuplicateProductExtensions_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 2);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);

        //Arrange
        var productExtensionProduct1 = product1.ProductExtensions[0];
        var productExtensionProduct2 = product1.ProductExtensions[1];

        var request = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithQuestion(Fake.Comment.Question)
            .WithResponse(Fake.Comment.Response)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct2.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var updateCommentRequest = UpdateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithId(commentResponse.Id)
            .WithProductExtensions(
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct1.RoutesOfAdministration.Select(r => r.Id).ToList()
                },
                new ProductExtensionCommentModel()
                {
                    ProductExtensionId = productExtensionProduct1.Id,
                    RouteOfAdministrationIds = productExtensionProduct2.RoutesOfAdministration.Select(r => r.Id).ToList()
                }
            )
            .WithDescription(Fake.Comment.Description)
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Skip(2).Take(2).ToArray()).Build();

        var updateCommentResponse = () => commentApi.UpdateCommentAsync(TenantConstants.DEFAULT_TENANT, updateCommentRequest);

        //Assert
        var exception = await updateCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Product Extensions must be unique.");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }
}
