﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class CommentProductExtension : BaseEntity
{
    public int CommentId { get; set; }

    public Comment? Comment { get; set; }
    public int ProductExtensionId { get; set; }

    public ProductExtension? ProductExtension { get; set; }

    public ICollection<CommentProductExtensionRoutesOfAdministration> CommentProductExtensionRoutesOfAdministrations { get; set; } =
        new HashSet<CommentProductExtensionRoutesOfAdministration>();
}
