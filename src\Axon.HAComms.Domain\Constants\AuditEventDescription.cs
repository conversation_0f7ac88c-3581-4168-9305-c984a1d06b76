﻿namespace Axon.HAComms.Domain.Constants;

public static class AuditEventDescription
{
    public const string SEARCH_EXECUTE = "User has performed a search with specific criteria";
    public const string SEARCH_OPEN_RESULT = "User has opened a specific search result";
    public const string SEARCH_FILE_EXPORT = "User has exported a file from the system";

    public const string DRUG_PRODUCT_CREATE = "User has created new product";
    public const string DRUG_PRODUCT_VIEW = "User has opened the product page";
    public const string DRUG_PRODUCT_DELETE = "User has deleted a product";
    public const string DRUG_PRODUCT_UPDATE = "User has updated a product";

    public const string DRUG_SUBSTANCE_CREATE = "User has created new drug substance";
    public const string DRUG_SUBSTANCE_DELETE = "User has deleted a drug substance";
    public const string DRUG_SUBSTANCE_UPDATE = "User has updated a drug substance";

    public const string COMMENT_CREATE = "User has created a comment";
    public const string COMMENT_UPDATE = "User has updated a comment";
    public const string COMMENT_DELETE = "User has deleted a comment";

    public const string COMMUNICATION_CREATE = "User has created a communication";
    public const string COMMUNICATION_UPDATE = "User has updated a communication";
    public const string COMMUNICATION_DELETE = "User has deleted a communication";
    public const string COMMUNICATION_COMPLETED = "User has completed a communication";
    public const string COMMUNICATION_REINSTATED = "User has reinstated a communication";

    public const string ROUTE_OF_ADMINISTRATION_CREATE = "User has created new route of administration";
    public const string ROUTE_OF_ADMINISTRATION_DELETE = "User has deleted a route of administration";
    public const string ROUTE_OF_ADMINISTRATION_UPDATE = "User has updated a route of administration";

    public const string DOSAGE_FORM_CREATE = "User has created new dosage form";
    public const string DOSAGE_FORM_DELETE = "User has deleted a dosage form";
    public const string DOSAGE_FORM_UPDATE = "User has updated a dosage form";

    public const string TAG_CREATE = "User has created a new tag";
    public const string TAG_DELETE = "User has deleted a tag";
    public const string TAG_UPDATE = "User has updated a tag";

}
