﻿using Axon.HAComms.Application.Queries.Comments.CommentsPagedList;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Comments.ProductCommentsPagedList
{
    public class GetCommentsByProductIdPagedListQueryValidatorTests
    {
        private readonly GetCommentsPagedListQueryValidator validator;
        public GetCommentsByProductIdPagedListQueryValidatorTests()
        {
            validator = new GetCommentsPagedListQueryValidator();
        }

        [Fact]
        public void Validate_CommunicationIdIsZero_ThrowsException()
        {
            var request = new GetCommentsPagedListQueryRequest(0, 5, null, 0, 10);

            var result = validator.TestValidate(request);
            result.ShouldHaveValidationErrorFor(x => x.CommunicationId);
            Assert.Contains("'Communication Id' must not be empty", result.Errors.First().ErrorMessage);
        }

        [Fact]
        public void Validate_CommunicationIdIsInvalid_ThrowsException()
        {
            var request = new GetCommentsPagedListQueryRequest(-9, 3, null, 0, 10);

            var result = validator.TestValidate(request);
            result.ShouldHaveValidationErrorFor(x => x.CommunicationId);
            Assert.Contains("'Communication Id' must be greater than '0'", result.Errors.First().ErrorMessage);
        }
    }
}
