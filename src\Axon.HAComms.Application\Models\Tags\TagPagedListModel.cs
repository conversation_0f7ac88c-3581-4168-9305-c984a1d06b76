namespace Axon.HAComms.Application.Models.Tags
{
	public class TagPagedListModel
	{
        public int Id { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsAssociatedToComment { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
		public string? LastUpdatedBy { get; set; }

		public TagPagedListModel(
			int id, 
			string name, 
			string? description,
			bool isAssociatedToComment,
            DateTime? createdDate,
			string? createdBy,
            DateTime? lastUpdatedDate, 
			string? lastUpdatedBy)
		{
            Id = id;
			Name = name;
			Description = description;
			IsAssociatedToComment = isAssociatedToComment;
            CreatedDate = createdDate;
			CreatedBy = createdBy;
            LastUpdatedDate = lastUpdatedDate;
			LastUpdatedBy = lastUpdatedBy;
		}
    }
}
