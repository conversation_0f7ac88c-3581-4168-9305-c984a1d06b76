﻿using Axon.HAComms.Domain.Entities.Base;
using Axon.HAComms.Domain.Interfaces;

namespace Axon.HAComms.Domain.Entities;

public class RouteOfAdministration : MultiTenantEntity, IEntityWithName
{
    public RouteOfAdministration()
    {
    }

    public RouteOfAdministration(int id)
    {
        Id = id;
    }

    public string Name { get; set; } = string.Empty;

    public int? ExternalId { get; set; }

    public ICollection<ProductExtension> ProductExtensions { get; set; } = new HashSet<ProductExtension>();

    public ICollection<ProductExtensionRouteOfAdministration> ProductExtensionRouteOfAdministrations { get; set; } = new HashSet<ProductExtensionRouteOfAdministration>();
}

