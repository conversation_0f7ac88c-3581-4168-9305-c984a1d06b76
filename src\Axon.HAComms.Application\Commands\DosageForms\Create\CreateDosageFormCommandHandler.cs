﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.DosageForms.Create;

public class CreateDosageFormCommandHandler(
    IDosageFormsRepository repoDosageForms,
    IMapper mapper,
    ILogger<CreateDosageFormCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<CreateDosageFormCommandRequest, CreateDosageFormCommandResponse>
{
    public async Task<CreateDosageFormCommandResponse> Handle(CreateDosageFormCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = new DosageForm();
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.DOSAGE_FORM_CREATED, AuditEventCategory.DOSAGE_FORMS, AuditEventDescription.DOSAGE_FORM_CREATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                repoDosageForms.AddItem(entity);
                await repoDosageForms.SaveChangesAsync(userProvider);
                logger.LogInformation("Dosage form {DosageForm} added successfully.", entity.Name);
            });

        return mapper.Map<CreateDosageFormCommandResponse>(entity);
    }
}
