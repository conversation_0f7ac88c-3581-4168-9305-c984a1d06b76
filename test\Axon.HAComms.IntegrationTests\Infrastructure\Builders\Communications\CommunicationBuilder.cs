using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;

public class CommunicationBuilder
{
    private string subject = Fake.Communication.Subject;
    private bool isCompleted = Fake.Communication.IsCompleted;
    private List<Domain.Entities.Application> applications = [];
    private readonly DateTime dateOfCommunication = DateTime.Now;
    private List<Comment> comments = [];
    private int submissionTypeId = Fake.Communication.SubmissionTypeId;
    private int countryId = Fake.Communication.CountryId;
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static CommunicationBuilder Default() => new();

    public Communication Build()
    {
        return new Communication()
        {
            Subject = subject,
            SubmissionTypeId = submissionTypeId,
            CountryId = countryId,
            DateOfCommunication = dateOfCommunication,
            Applications = applications,
            Comments = comments,
            IsCompleted = isCompleted,
            Tenant = tenant
        };
    }

    public CommunicationBuilder WithSubject(string subject)
    {
        this.subject = subject;
        return this;
    }

    public CommunicationBuilder WithIsCompleted(bool isCompleted)
    {
        this.isCompleted = isCompleted;
        return this;
    }
    public CommunicationBuilder WithSubmissionTypeId(int submissionTypeId)
    {
        this.submissionTypeId = submissionTypeId;
        return this;
    }

    public CommunicationBuilder WithCountryId(int countryId)
    {
        this.countryId = countryId;
        return this;
    }
    public CommunicationBuilder WithComments(params Comment[] comments)
    {
        this.comments = comments.ToList();
        return this;
    }

    public CommunicationBuilder WithApplications(params Domain.Entities.Application[] applications)
    {
        this.applications = applications.ToList();
        return this;
    }

    public CommunicationBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
