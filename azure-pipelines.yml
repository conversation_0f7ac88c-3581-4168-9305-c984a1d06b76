name: "$(Date:yyyyMMdd).$(Rev:r)"

pool: pv-pool

parameters:
  - name: Analyse_Packages
    displayName: Analyse Packages
    type: boolean
    default: false
  - name: Push_Docker_Image
    displayName: Push Docker Image
    type: boolean
    default: false

schedules:
- cron: '0 23 * * 4'
  displayName: "Weekly build thursday 2300"
  branches:
    include:
      - main
  always: true

#below just temporarily for testing on int
- cron: '0 23 * * 0'
  displayName: "Weekly build sunday 2300"
  branches:
    include:
      - develop
  always: true

trigger:
  - develop
  - main
  - release/*
  - hotfix/*

variables:
  - name: Source_Branch
    value: ${{ replace(variables['Build.SourceBranch'],'refs/heads/','') }}
  - name: Push_Docker_Image
    value: ${{ or(in(variables['Source_Branch'], 'main','develop'), startsWith(variables['Source_Branch'], 'release/'), eq(parameters.Push_Docker_Image, true)) }}
  - name: Long_Lived_Branch
    value: ${{ or(in(variables['Source_Branch'], 'main','master','develop'), startsWith(variables['Source_Branch'], 'release/')) }}
  - name: Version_Number
    value: "1.8.0"
  - name: Build_Number
    value: $[counter(variables['Version_Number'], 0)]
  - name: Build_Configuration
    value: "Release"
  #Shared Variable group
  - group: Nuget
  - name: NuGet_Source
    value: $[variables.Source]

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git
stages:
  - stage: Prerequisites
    jobs:
      - job: CalculateVersion
        displayName: "Calculate Version"
        steps:
          - template: General/calculate-version.yml@templates
            parameters:
              VersionNumber: "$(Version_Number)"
              BuildNumber: "$(Build_Number)"
              BranchName: "$(Source_Branch)"
          - template: WhiteSource/analyse.yml@templates
            parameters:
              SolutionName: 'Axon.HAComms.Api.sln'
              LongLivedBranch: "variables['Long_Lived_Branch']"
              WhiteSourceProductName: "Axon_HAComms_$(Build.SourceBranchName)"
  - template: Build/dotnet/dkr-build-test-analyse-push.yml@templates
    parameters:
      NugetSource: "$(NuGet_Source)"
      BuildConfiguration: "$(Build_Configuration)"
      SourceBranch: "variables['Source_Branch']"
      VersionNumber: "$(Version_Number)"
      SolutionName: "Axon.HAComms.Api.sln"
      DockerRepository: "axon-hacomms-api"
      DockerRepositoryTest: "axon-hacomms-api-tests sql"
      DockerTestContainer: "axon-hacomms-api_axon-hacomms-api-tests_1"
      SonarProjectKey: "Phlexglobal_Axon.HAComms.Api"
      SonarProjectName: "Axon.HAComms.Api"
      PrerequisiteStage: Prerequisites
      AnalysePackages: "${{ parameters.Analyse_Packages }}"
      LongLivedBranch: '${{ variables.Long_Lived_Branch }}'
      PushDockerImage: "variables['Push_Docker_Image']"
      DockerfilePath: "src/Axon.HAComms.Api/Dockerfile"
      CheckmarxTeam: "Code_Busters"
      CheckmarxProjectName: "Axon.HAComms.Api"

  - stage: MigrateDb
    jobs:
      - job: DatabaseMigration
        displayName: Database Migration
        pool:
          vmImage: 'ubuntu-latest'
        steps:
            - task: DotNetCoreCLI@2
              inputs:
                command: 'restore'
                projects: 'Axon.HAComms.Api.sln'
                feedsToUse: 'select'
                vstsFeed: '780c2041-bb89-4053-a6e6-13d4c16ae672'
            - task: DotNetCoreCLI@2
              inputs:
                command: 'build'
                projects: 'Axon.HAComms.Api.sln'
            - task: AzureCLI@1
              displayName: 'EF Migrations'
              inputs:
                    azureSubscription: 'Axon_Development'
                    scriptLocation: inlineScript
                    inlineScript: |

                        dotnet tool install -g dotnet-ef --version 6.0.6

                        dotnet ef migrations script --project "$(Build.SourcesDirectory)/src/Axon.HAComms.Infrastructure/Axon.HAComms.Infrastructure.csproj" --startup-project "$(Build.SourcesDirectory)/src/Axon.HAComms.Api/Axon.HAComms.Api.csproj" --context HACommsContext  --output $(Build.ArtifactStagingDirectory)/Migrations/migration.sql --idempotent --verbose --no-build --configuration Debug
            - task: PublishBuildArtifacts@1
              inputs:
                PathtoPublish: '$(Build.ArtifactStagingDirectory)'
                ArtifactName: 'drop'
                publishLocation: 'Container'
