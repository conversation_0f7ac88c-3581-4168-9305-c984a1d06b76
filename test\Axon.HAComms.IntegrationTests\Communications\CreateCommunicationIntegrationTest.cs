﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateCommunicationIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommentsApi commentsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions.Single().Number.Should().Be(application.Submissions.Single().Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(productExtension.RoutesOfAdministration.Select(r => r.Name));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }
        
    [Fact]
    public async Task CreateCommunication_WithOutTags_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = firstProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds([]).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Tag Ids' must not be empty.", exception.And.Message);
    }
        
    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_WithOneSubstance_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext,
            productApi,
            2,
            routeOfAdministrationList: [testRouteOfAdministrationEntities[0], testRouteOfAdministrationEntities[1]]);

        var drugSubstanceIds = product1.DrugSubstances.Select(d => d.Id).ToArray();

        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);
        var tagIdsForComment1 = selectedTags.Take(2).Select(t => t.Id).ToArray();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = product1.ProductExtensions[0].Id, RouteOfAdministrationIds = product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(drugSubstanceIds[0])
            .WithTagIds(tagIdsForComment1).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions.Single().Number.Should().Be(application.Submissions.Single().Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment1.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment1.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment1.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment1.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment1.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name));
        communicationComment.DrugSubstances.Should().HaveCount(comment1.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances[0].Id.Should().Be(comment1.DrugSubstanceIds[0]);
        communicationComment.DrugSubstances[0].Name.Should().Be(product1.DrugSubstances[0].Name);
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment1.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment1.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_WithSubsetOfSubstances_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext,
            productApi,
            2,
            10,
            3,
            [testRouteOfAdministrationEntities[0], testRouteOfAdministrationEntities[1]]);

        var drugSubstanceIds = product1.DrugSubstances.Select(d => d.Id).ToArray();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);
        var tagIdsForComment1 = selectedTags.Take(2).Select(t => t.Id).ToArray();
        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = product1.ProductExtensions[0].Id, RouteOfAdministrationIds = product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(drugSubstanceIds[0], drugSubstanceIds[1], drugSubstanceIds[2], drugSubstanceIds[3], drugSubstanceIds[4])
            .WithTagIds(tagIdsForComment1).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions.Single().Number.Should().Be(application.Submissions.Single().Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment1.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment1.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment1.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment1.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment1.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name));
        communicationComment.DrugSubstances.Should().HaveCount(comment1.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment1.DrugSubstanceIds);
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment1.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment1.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_WithSubsetOfRouteOfAdministration_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext,
            productApi,
            2,
            routeOfAdministrationList: testRouteOfAdministrationEntities);

        var tagIdsForComment1 = selectedTags.Select(x => x.Id).Take(3).ToArray();
        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = product1.ProductExtensions[0].Id, RouteOfAdministrationIds = product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Id).Take(4).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagIdsForComment1).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions.Single().Number.Should().Be(application.Submissions.Single().Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment1.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment1.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment1.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment1.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment1.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment1.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment1.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment1.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment1.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithoutCountry_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds([]).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(0)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Country Id' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task CreateCommunication_WithoutSubmissionType_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds([]).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(0)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Submission Type Id' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task CreateCommunication_WithoutDateOfCommunication_ThrowsException()
    {
        //Arrange
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(t => t.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(default)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Date Of Communication' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task CreateCommunication_WithoutSubject_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds([]).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubject(string.Empty)
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        Assert.Contains("'Subject' must not be empty.", exception.And.Message);
    }

    [Fact]
    public async Task CreateCommunication_InvalidCountry_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var maxCountryId = dbContext.Countries.Max(r => r.Id);
        var invalidCountryId = Fake.GetRandomInt(maxCountryId + 1, maxCountryId + 50);
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(t => t.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(invalidCountryId)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Country with id '{invalidCountryId}' does not exist");
    }

    [Fact]
    public async Task CreateCommunication_InvalidSubmissionType_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var maxSubmissionTypeId = dbContext.SubmissionTypes.Max(r => r.Id);
        var invalidSubmissionTypeId = Fake.GetRandomInt(maxSubmissionTypeId + 1, maxSubmissionTypeId + 999);
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTags.Select(t => t.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(invalidSubmissionTypeId)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Submission Type with id '{invalidSubmissionTypeId}' does not exist");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_InvalidTag_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();

        var generatedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);
        var maxTagId = generatedTags.Max(r => r.Id);
        var invalidTagId = Fake.GetRandomInt(maxTagId + 99, maxTagId + 999);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(invalidTagId).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Tag\\\" ({string.Join(", ", request.Comment.TagIds)}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_InvalidProductExtension_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = Fake.Comment.RouteOfAdministrationIds })
            .WithDrugSubstanceIds(drugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"ProductExtension\\\" ({string.Join(",", request.Comment.ProductExtensions.Select(x => x.ProductExtensionId))}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_OneValidAndOneInvalidProductExtension_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(
                new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = firstProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() },
                new ProductExtensionCommentModel() { ProductExtensionId = Fake.ProductExtension.Id, RouteOfAdministrationIds = Fake.Comment.RouteOfAdministrationIds })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"ProductExtension\\\" ({string.Join(",", request.Comment.ProductExtensions.Select(x => x.ProductExtensionId))}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_NoRouteOfAdministration_ThrowsArgumentException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = new List<int>() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Route Of Administration Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_InvalidRouteOfAdministration_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var maxRouteOfAdministrationId = dbContext.RouteOfAdministrations.Max(r => r.Id);
        var invalidRouteOfAdministrationsId = Fake.GetRandomInt(maxRouteOfAdministrationId + 99, maxRouteOfAdministrationId + 999);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = new List<int>() { invalidRouteOfAdministrationsId } })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"RouteOfAdministration\\\" ({string.Join(",", request.Comment.ProductExtensions.SelectMany(r => r.RouteOfAdministrationIds))}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_NoDrugSubstance_ThrowsArgumentException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = firstProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds([])
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Drug Substance Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_InvalidDrugSubstance_ThrowsEntityNotFoundException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var firstProductExtension = product1.ProductExtensions.First();

        var maxDrugSubstanceId = dbContext.DrugSubstances.Max(r => r.Id);
        var invalidDrugSubstanceId = Fake.GetRandomInt(maxDrugSubstanceId + 99, maxDrugSubstanceId + 999);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = firstProductExtension.Id, RouteOfAdministrationIds = firstProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(invalidDrugSubstanceId)
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"DrugSubstance\\\" ({string.Join(",", request.Comment.DrugSubstanceIds)}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_RouteOfAdministrationFromDifferentProductExtension_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();
        var product11 = await ApiTestHelper.CreateProductForTest(
            dbContext,
            productApi,
            routeOfAdministrationList: [testRouteOfAdministrationEntities[0]]);
        var product1ProductExtension = product11.ProductExtensions.First();

        var product12 = await ApiTestHelper.CreateProductForTest(
            dbContext,
            productApi,
            routeOfAdministrationList: [testRouteOfAdministrationEntities[1]]);
        var product2ProductExtension = product12.ProductExtensions.First();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = product1ProductExtension.Id, RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product11.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"RouteOfAdministration\\\" ({string.Join(",", request.Comment.ProductExtensions.SelectMany(r => r.RouteOfAdministrationIds))}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithNonGeneralGuidanceComment_DrugSubstanceFromDifferentProduct_ThrowsException()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product11 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product11.ProductExtensions.First();

        var product12 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = product1ProductExtension.Id, RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product12.DrugSubstances.Select(s => s.Id).ToArray())
            .WithTagIds(selectedTags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = () => communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);

        //Assert
        var exception = await response.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"DrugSubstance\\\" ({string.Join(",", request.Comment.DrugSubstanceIds)}) was not found.");
    }

    [Fact]
    public async Task CreateCommunication_WithGeneralGuidanceComment_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var selectedTags = await TagsTestEntitiesBuilder.Build(dbContext, 6);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var tagIdsForComment1 = selectedTags.Select(x => x.Id).Take(3).ToArray();
        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .AsGeneralGuidance()
            .WithProductExtensions([])
            .WithQuestionIncluded()
            .WithDrugSubstanceIds([])
            .WithTagIds(tagIdsForComment1).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions.Single().Number.Should().Be(application.Submissions.Single().Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().BeNull();
        communicationComment.Question.Should().Be(comment1.Question);
        communicationComment.Response.Should().Be(comment1.Response);
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Should().BeEmpty();
        communicationComment.DrugSubstances.Should().BeEmpty();
        communicationComment.IsGeneralGuidance.Should().BeTrue();
        communicationComment.IsQuestionIncluded.Should().BeTrue();
        communicationComment.Tags.Should().HaveCount(comment1.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment1.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithoutApplicationAndSubmissionNumbers_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithOneApplicationNumberWithoutAnySubmissionNumbers_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var application = ApplicationModelBuilder.Default().Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithMultipleApplicationNumbersWithoutAnySubmissionNumbers_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var application = ApplicationModelBuilder.Default().Build();
        var application1 = ApplicationModelBuilder.Default().Build();
        var application2 = ApplicationModelBuilder.Default().Build();
        var application3 = ApplicationModelBuilder.Default().Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application, application1, application2, application3)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications[0].Number.Should().Be(application.Number);
        communication.Applications[1].Number.Should().Be(application1.Number);
        communication.Applications[2].Number.Should().Be(application2.Number);
        communication.Applications[3].Number.Should().Be(application3.Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithOneApplicationNumberWithMultipleSubmissionNumbers_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var submission1 = SubmissionModelBuilder.Default().Build();
        var submission2 = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission, submission1, submission2).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications.Single().Number.Should().Be(application.Number);
        communication.Applications.Single().Submissions[0].Number.Should().Be(application.Submissions[0].Number);
        communication.Applications.Single().Submissions[1].Number.Should().Be(application.Submissions[1].Number);
        communication.Applications.Single().Submissions[2].Number.Should().Be(application.Submissions[2].Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }

    [Fact]
    public async Task CreateCommunication_WithMultipleApplicationNumbersWithMultipleSubmissionNumbers_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var productExtension = product1.ProductExtensions.First();
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var submission1 = SubmissionModelBuilder.Default().Build();
        var submission2 = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission, submission1, submission2).Build();
        var application1 = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();
        var application2 = ApplicationModelBuilder.Default()
            .WithSubmissions(submission1).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tagsForComment.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application, application1, application2)
            .WithComment(comment).Build();

        //Act
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        response.Id.Should().NotBe(0);

        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        communication.Id.Should().Be(response.Id);
        communication.Subject.Should().Be(request.Subject);
        communication.SubmissionType.Id.Should().Be(submissionType.Id);
        communication.SubmissionType.Name.Should().Be(submissionType.Name);
        communication.Country.Id.Should().Be(country.Id);
        communication.Country.Name.Should().Be(country.Name);
        communication.DateOfCommunication.Should().BeSameDateAs(dateOfCommunication);
        communication.Applications[0].Number.Should().Be(application.Number);
        communication.Applications[0].Submissions[0].Number.Should().Be(application.Submissions[0].Number);
        communication.Applications[0].Submissions[1].Number.Should().Be(application.Submissions[1].Number);
        communication.Applications[0].Submissions[2].Number.Should().Be(application.Submissions[2].Number);
        communication.Applications[1].Number.Should().Be(application1.Number);
        communication.Applications[1].Submissions[0].Number.Should().Be(application1.Submissions[0].Number);
        communication.Applications[2].Number.Should().Be(application2.Number);
        communication.Applications[2].Submissions[0].Number.Should().Be(application2.Submissions[0].Number);
        communication.IsCompleted.Should().BeFalse();

        var getCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var communicationComment = getCommentsResponse.Data.Single();
        communicationComment.Description.Should().Be(comment.Description);
        communicationComment.Question.Should().BeNull();
        communicationComment.Response.Should().BeNull();
        communicationComment.BirdsLinkToBIResponse.Should().Be(comment.BirdsLinkToBIResponse);
        communicationComment.BirdsLinkToBISAMP.Should().Be(comment.BirdsLinkToBISAMP);
        communicationComment.ProductExtensions.Count.Should().Be(comment.ProductExtensions.Count);
        communicationComment.ProductExtensions[0].Id.Should().Be(comment.ProductExtensions[0].ProductExtensionId);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().HaveCount(comment.ProductExtensions[0].RouteOfAdministrationIds.Count);
        communicationComment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(comment.ProductExtensions[0].RouteOfAdministrationIds);
        communicationComment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1.ProductExtensions[0].RoutesOfAdministration.Select(r => r.Name).Take(4));
        communicationComment.DrugSubstances.Should().HaveCount(comment.DrugSubstanceIds.Count);
        communicationComment.DrugSubstances.Select(d => d.Id).Should().BeEquivalentTo(comment.DrugSubstanceIds);
        communicationComment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        communicationComment.IsGeneralGuidance.Should().BeFalse();
        communicationComment.IsQuestionIncluded.Should().BeFalse();
        communicationComment.Tags.Should().HaveCount(comment.TagIds.Count);
        communicationComment.Tags.Select(t => t.Id).Should().BeEquivalentTo(comment.TagIds);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        dbContext.Tags.Clear();
        await dbContext.SaveChangesAsync();
    }
}
