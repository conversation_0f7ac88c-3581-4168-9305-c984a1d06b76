/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CommentDtoModel
    /// </summary>
    [DataContract(Name = "CommentDtoModel")]
    public partial class CommentDtoModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CommentDtoModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="description">description.</param>
        /// <param name="question">question.</param>
        /// <param name="response">response.</param>
        /// <param name="birdsLinkToBIResponse">birdsLinkToBIResponse.</param>
        /// <param name="birdsLinkToBISAMP">birdsLinkToBISAMP.</param>
        /// <param name="productExtensions">productExtensions.</param>
        /// <param name="productName">productName.</param>
        /// <param name="drugSubstances">drugSubstances.</param>
        /// <param name="productTypes">productTypes.</param>
        /// <param name="tags">tags.</param>
        /// <param name="isGeneralGuidance">isGeneralGuidance.</param>
        /// <param name="isQuestionIncluded">isQuestionIncluded.</param>
        /// <param name="createdDate">createdDate.</param>
        /// <param name="lastUpdatedDate">lastUpdatedDate.</param>
        /// <param name="createdBy">createdBy.</param>
        /// <param name="lastUpdatedBy">lastUpdatedBy.</param>
        public CommentDtoModel(int id = default(int), string description = default(string), string question = default(string), string response = default(string), string birdsLinkToBIResponse = default(string), string birdsLinkToBISAMP = default(string), List<ProductExtensionModel> productExtensions = default(List<ProductExtensionModel>), string productName = default(string), List<DrugSubstanceModel> drugSubstances = default(List<DrugSubstanceModel>), List<string> productTypes = default(List<string>), List<TagModel> tags = default(List<TagModel>), bool isGeneralGuidance = default(bool), bool isQuestionIncluded = default(bool), DateTime createdDate = default(DateTime), DateTime lastUpdatedDate = default(DateTime), string createdBy = default(string), string lastUpdatedBy = default(string))
        {
            this.Id = id;
            this.Description = description;
            this.Question = question;
            this.Response = response;
            this.BirdsLinkToBIResponse = birdsLinkToBIResponse;
            this.BirdsLinkToBISAMP = birdsLinkToBISAMP;
            this.ProductExtensions = productExtensions;
            this.ProductName = productName;
            this.DrugSubstances = drugSubstances;
            this.ProductTypes = productTypes;
            this.Tags = tags;
            this.IsGeneralGuidance = isGeneralGuidance;
            this.IsQuestionIncluded = isQuestionIncluded;
            this.CreatedDate = createdDate;
            this.LastUpdatedDate = lastUpdatedDate;
            this.CreatedBy = createdBy;
            this.LastUpdatedBy = lastUpdatedBy;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Description
        /// </summary>
        [DataMember(Name = "description", EmitDefaultValue = true)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets Question
        /// </summary>
        [DataMember(Name = "question", EmitDefaultValue = true)]
        public string Question { get; set; }

        /// <summary>
        /// Gets or Sets Response
        /// </summary>
        [DataMember(Name = "response", EmitDefaultValue = true)]
        public string Response { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBIResponse
        /// </summary>
        [DataMember(Name = "birdsLinkToBIResponse", EmitDefaultValue = true)]
        public string BirdsLinkToBIResponse { get; set; }

        /// <summary>
        /// Gets or Sets BirdsLinkToBISAMP
        /// </summary>
        [DataMember(Name = "birdsLinkToBISAMP", EmitDefaultValue = true)]
        public string BirdsLinkToBISAMP { get; set; }

        /// <summary>
        /// Gets or Sets ProductExtensions
        /// </summary>
        [DataMember(Name = "productExtensions", EmitDefaultValue = true)]
        public List<ProductExtensionModel> ProductExtensions { get; set; }

        /// <summary>
        /// Gets or Sets ProductName
        /// </summary>
        [DataMember(Name = "productName", EmitDefaultValue = true)]
        public string ProductName { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstances
        /// </summary>
        [DataMember(Name = "drugSubstances", EmitDefaultValue = true)]
        public List<DrugSubstanceModel> DrugSubstances { get; set; }

        /// <summary>
        /// Gets or Sets ProductTypes
        /// </summary>
        [DataMember(Name = "productTypes", EmitDefaultValue = true)]
        public List<string> ProductTypes { get; set; }

        /// <summary>
        /// Gets or Sets Tags
        /// </summary>
        [DataMember(Name = "tags", EmitDefaultValue = true)]
        public List<TagModel> Tags { get; set; }

        /// <summary>
        /// Gets or Sets IsGeneralGuidance
        /// </summary>
        [DataMember(Name = "isGeneralGuidance", EmitDefaultValue = true)]
        public bool IsGeneralGuidance { get; set; }

        /// <summary>
        /// Gets or Sets IsQuestionIncluded
        /// </summary>
        [DataMember(Name = "isQuestionIncluded", EmitDefaultValue = true)]
        public bool IsQuestionIncluded { get; set; }

        /// <summary>
        /// Gets or Sets CreatedDate
        /// </summary>
        [DataMember(Name = "createdDate", EmitDefaultValue = false)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedDate
        /// </summary>
        [DataMember(Name = "lastUpdatedDate", EmitDefaultValue = false)]
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or Sets CreatedBy
        /// </summary>
        [DataMember(Name = "createdBy", EmitDefaultValue = true)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedBy
        /// </summary>
        [DataMember(Name = "lastUpdatedBy", EmitDefaultValue = true)]
        public string LastUpdatedBy { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CommentDtoModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Description: ").Append(Description).Append("\n");
            sb.Append("  Question: ").Append(Question).Append("\n");
            sb.Append("  Response: ").Append(Response).Append("\n");
            sb.Append("  BirdsLinkToBIResponse: ").Append(BirdsLinkToBIResponse).Append("\n");
            sb.Append("  BirdsLinkToBISAMP: ").Append(BirdsLinkToBISAMP).Append("\n");
            sb.Append("  ProductExtensions: ").Append(ProductExtensions).Append("\n");
            sb.Append("  ProductName: ").Append(ProductName).Append("\n");
            sb.Append("  DrugSubstances: ").Append(DrugSubstances).Append("\n");
            sb.Append("  ProductTypes: ").Append(ProductTypes).Append("\n");
            sb.Append("  Tags: ").Append(Tags).Append("\n");
            sb.Append("  IsGeneralGuidance: ").Append(IsGeneralGuidance).Append("\n");
            sb.Append("  IsQuestionIncluded: ").Append(IsQuestionIncluded).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  LastUpdatedDate: ").Append(LastUpdatedDate).Append("\n");
            sb.Append("  CreatedBy: ").Append(CreatedBy).Append("\n");
            sb.Append("  LastUpdatedBy: ").Append(LastUpdatedBy).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
