﻿using Axon.HAComms.Application.Queries.Comments.SearchDetails;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Comments.SearchDetails;

public class GetSearchDetailsByCommentIdQueryValidatorTests
{
    private readonly GetSearchDetailsByCommentIdQueryValidator validator;

    public GetSearchDetailsByCommentIdQueryValidatorTests()
    {
        validator = new GetSearchDetailsByCommentIdQueryValidator();
    }

    [Fact]
    public void Validate_CommunicationIdIsZero_ThrowsException()
    {
        var request = new GetSearchDetailsByCommentIdQueryRequest(0, 5);

        var result = validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.CommunicationId);
        Assert.Contains("'Communication Id' must not be empty", result.Errors.First().ErrorMessage);
    }

    [Fact]
    public void Validate_CommunicationIdIsInvalid_ThrowsException()
    {
        var request = new GetSearchDetailsByCommentIdQueryRequest(-9, 3);

        var result = validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.CommunicationId);
        Assert.Contains("'Communication Id' must be greater than '0'", result.Errors.First().ErrorMessage);
    }

    [Fact]
    public void Validate_CommentIdIsZero_ThrowsException()
    {
        var request = new GetSearchDetailsByCommentIdQueryRequest(5, 0);

        var result = validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.CommentId);
        Assert.Contains("'Comment Id' must not be empty", result.Errors.First().ErrorMessage);
    }

    [Fact]
    public void Validate_CommentIdIsInvalid_ThrowsException()
    {
        var request = new GetSearchDetailsByCommentIdQueryRequest(6, -8);

        var result = validator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.CommentId);
        Assert.Contains("'Comment Id' must be greater than '0'", result.Errors.First().ErrorMessage);
    }
}
