﻿using Axon.HAComms.Application.Commands.DosageForms.Delete;
using FluentValidation.TestHelper;
using Xunit;

namespace Axon.HAComms.Tests.Commands.DosageForms.Delete
{
    public class DeleteDosageFormValidatorTests
    {
        private readonly DeleteDosageFormCommandValidator sut;

        public DeleteDosageFormValidatorTests()
        {
            sut = new DeleteDosageFormCommandValidator();
        }

        [Fact]
        public void Validate_IdIsEmpty_ThrowsException()
        {
            var result = sut.TestValidate(new DeleteDosageFormCommandRequest(0));
            result.ShouldHaveValidationErrorFor(x => x.Id);
        }

        [Fact]
        public void Validate_IdIsNotEmpty_DoesNotThrowException()
        {
            var result = sut.TestValidate(new DeleteDosageFormCommandRequest(1));
            result.ShouldNotHaveValidationErrorFor(x => x.Id);
        }
    }
}
