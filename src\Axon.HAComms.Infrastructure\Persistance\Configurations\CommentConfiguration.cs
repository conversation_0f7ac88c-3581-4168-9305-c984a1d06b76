﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class CommentConfiguration : IEntityTypeConfiguration<Comment>
{
    public void Configure(EntityTypeBuilder<Comment> builder)
    {
        builder.ToTable("Comments");
        builder.HasMany(c => c.DrugSubstances)
            .WithMany(s => s.Comments)
            .UsingEntity<CommentDrugSubstances>(t => t.ToTable("CommentDrugSubstances"));
        builder.HasMany(c => c.ProductExtensions)
            .WithMany(s => s.Comments)
            .UsingEntity<CommentProductExtension>(t => t.ToTable("CommentProductExtension"));
        builder.HasMany(c => c.Tags)
            .WithMany(t => t.Comments)
            .UsingEntity<CommentTags>(t => t.ToTable("CommentTags"));

        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
