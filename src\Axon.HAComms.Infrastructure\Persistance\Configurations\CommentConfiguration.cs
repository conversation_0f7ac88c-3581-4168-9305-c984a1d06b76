﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class CommentConfiguration : BaseEntityConfiguration<Comment>
{
    protected override void ConfigureEntity(EntityTypeBuilder<Comment> builder)
    {
        builder.ToTable("Comments");
        builder.HasMany(c => c.DrugSubstances)
            .WithMany(s => s.Comments)
            .UsingEntity<CommentDrugSubstances>(t => t.ToTable("CommentDrugSubstances"));
        builder.Has<PERSON>any(c => c.ProductExtensions)
            .WithMany(s => s.Comments)
            .UsingEntity<CommentProductExtension>(t => t.ToTable("CommentProductExtension"));
        builder.HasMany(c => c.Tags)
            .WithMany(t => t.Comments)
            .UsingEntity<CommentTags>(t => t.ToTable("CommentTags"));
    }
}
