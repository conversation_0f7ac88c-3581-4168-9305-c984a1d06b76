﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using Axon.HAComms.Tests.Common.Builders;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Products;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateProductIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateProduct_WithDrugSubstances_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();

        var getProductResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        getProductResponse.Should().NotBeNull();
        getProductResponse.Name.Should().Be(productName);
        getProductResponse.DrugSubstances.Should().HaveCount(3);
        getProductResponse.DrugSubstances[0].Id.Should().Be(drugSubstances[0].Id);
        getProductResponse.DrugSubstances[1].Id.Should().Be(drugSubstances[1].Id);
        getProductResponse.DrugSubstances[2].Id.Should().Be(drugSubstances[2].Id);

        getProductResponse.DrugSubstances[0].Code.Should().Be(drugSubstances[0].Code);
        getProductResponse.DrugSubstances[1].Code.Should().Be(drugSubstances[1].Code);
        getProductResponse.DrugSubstances[2].Code.Should().Be(drugSubstances[2].Code);

        getProductResponse.DrugSubstances[0].Name.Should().Be(drugSubstances[0].Name);
        getProductResponse.DrugSubstances[1].Name.Should().Be(drugSubstances[1].Name);
        getProductResponse.DrugSubstances[2].Name.Should().Be(drugSubstances[2].Name);

        getProductResponse.DrugSubstances[0].Description.Should().Be(drugSubstances[0].Description);
        getProductResponse.DrugSubstances[1].Description.Should().Be(drugSubstances[1].Description);
        getProductResponse.DrugSubstances[2].Description.Should().Be(drugSubstances[2].Description);
    }

    [Fact]
    public async Task CreateProduct_WithoutDrugSubstances_ThrowsArgumentException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithProductExtensions(productExtensionModel)
            .WithDrugSubstances([])
            .WithProductTypes(productTypeIds)
            .Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Drug Substance Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateProduct_WithDuplicateDrugSubstances_ReturnsOk()
    {
        //Arrange
        var productName1 = Fake.Product.Name;
        var productName2 = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var dosageForms = await dbContext.DosageForms.GetRandomEntities(2);
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForms[0].Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForms[0].Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest1 = new CreateProductCommandRequestBuilder()
            .WithName(productName1)
            .WithProductExtensions(productExtensionModel1)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .Build();

        var productRequest2 = new CreateProductCommandRequestBuilder()
            .WithName(productName2)
            .WithProductExtensions(productExtensionModel2)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .Build();

        //Act
        var createProductResponse1 = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest1);
        createProductResponse1.Should().NotBeNull();
        var productResponse1 = await productApi.GetProductAsync(createProductResponse1.Id, TenantConstants.DEFAULT_TENANT);

        var createProductResponse2 = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest2);
        createProductResponse2.Should().NotBeNull();
        var productResponse2 = await productApi.GetProductAsync(createProductResponse2.Id, TenantConstants.DEFAULT_TENANT);


        //Assert
        productResponse1.Should().NotBeNull();
        productResponse1.Name.Should().Be(productName1);
        productResponse1.DrugSubstances.Should().HaveCount(3);
        productResponse1.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);

        productResponse2.Should().NotBeNull();
        productResponse2.Name.Should().Be(productName2);
        productResponse2.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);
    }

    [Fact]
    public async Task CreateProduct_WithDrugSubstancesWithInvalidIds_ThrowsEntityNotFoundException()
    {
        //Arrange
        var productName1 = Fake.Product.Name;
        var productName2 = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 5);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(2);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var dosageForms = await dbContext.DosageForms.GetRandomEntities(2);
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForms[0].Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest1 = new CreateProductCommandRequestBuilder()
            .WithName(productName1)
            .WithProductExtensions(productExtensionModel1)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .Build();

        await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest1);

        var rng = new Random();
        drugSubstanceIds.Add(280);
        var shuffledDrugSubstanceIds = drugSubstanceIds.OrderBy(_ => rng.Next()).ToList();

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForms[0].Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest2 = new CreateProductCommandRequestBuilder()
            .WithName(productName2)
            .WithProductExtensions(productExtensionModel2)
            .WithDrugSubstances(shuffledDrugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest2);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("EntityNotFoundException");
    }

    [Fact]
    public async Task CreateProduct_PassDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest1 = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds.Take(1).ToList())
            .WithProductTypes(productTypeIds.Take(2).ToList())
            .WithProductExtensions(productExtensionModel1)
            .Build();

        await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest1);

        var productRequest2 = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds.Skip(1).Take(2).ToList())
            .WithProductTypes(productTypeIds.Skip(1).Take(2).ToList())
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest2);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        Assert.Contains("AlreadyExists", exception.And.Message);
    }

    [Fact]
    public async Task CreateProduct_WithoutName_ThrowsException()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(string.Empty)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("The Name field is required.");
    }

    [Fact]
    public async Task CreateProduct_WithInActive_ReturnsOk()
    {
        /*
         * Scenario:
         * Create product with inactive status
         * Product should still be created as active
         */

        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithIsActive(false)
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.IsActive.Should().BeTrue();
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(3);
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensions_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(3);
        productResponse.ProductExtensions.Should().HaveCount(2);
        productResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        productResponse.ProductExtensions[0].IsActive.Should().BeTrue();
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        productResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(1);
        productResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[0]);

        productResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        productResponse.ProductExtensions[1].IsActive.Should().BeTrue();
        productResponse.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);

        productResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(1);
        productResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().Be(productExtensionModel2.RouteOfAdministrationIds[0]);
    }

    [Fact]
    public async Task CreateProduct_WithoutProductExtensions_ThrowsException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithProductTypes(productTypeIds)
            .WithDrugSubstances(drugSubstanceIds)
            .Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Product Extensions' must not be empty.");
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionWithInvalidDosageForm_ThrowsException()
    {
        //Arrange
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var maxDosageFormsId = await dbContext.DosageForms.MaxAsync(d => d.Id);
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(Fake.GetRandomInt(maxDosageFormsId + 99, maxDosageFormsId + 999))
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("EntityNotFoundException");
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionWithoutRoutesOfAdministration_ThrowsException()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default().WithDosageFormId(dosageForm.Id).Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Route Of Administration Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionWithInvalidRoutesOfAdministration_ThrowsException()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var maxRouteId = await dbContext.RouteOfAdministrations.MaxAsync(r => r.Id);
        var invalidId = Fake.GetRandomInt(maxRouteId + 1, maxRouteId + 50);
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([invalidId])
            .Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("EntityNotFoundException");
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionsWithMultipleRoutesOfAdministration_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id, testRouteOfAdministrationEntities[2].Id])
            .Build();
        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(false)
            .WithDosageFormId(testDosageForms[1].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2).Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(3);
        productResponse.ProductExtensions.Should().HaveCount(2);
        productResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        productResponse.ProductExtensions[0].IsActive.Should().BeTrue();
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        productResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(3);
        productResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[0]);
        productResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[1]);
        productResponse.ProductExtensions[0].RoutesOfAdministration[2].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[2]);

        productResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        productResponse.ProductExtensions[1].IsActive.Should().BeFalse();
        productResponse.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);

        productResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(1);
        productResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().Be(productExtensionModel2.RouteOfAdministrationIds[0]);
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionsWithMultipleRoutesOfAdministrationIncludingDuplicates_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(2);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var testDosageForms = await dbContext.DosageForms.ToListAsync();
        var testRouteOfAdministrationEntities = await dbContext.RouteOfAdministrations.ToListAsync();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithDosageFormId(testDosageForms[0].Id)
            .WithRouteOfAdministrationIds([testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id,
                testRouteOfAdministrationEntities[0].Id, testRouteOfAdministrationEntities[1].Id])
            .Build();
        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1)
            .Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(2);
        productResponse.ProductExtensions.Should().HaveCount(1);
        productResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        productResponse.ProductExtensions[0].IsActive.Should().BeTrue();
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        productResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(2);
        productResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[0]);
        productResponse.ProductExtensions[0].RoutesOfAdministration[1].Id.Should().Be(productExtensionModel1.RouteOfAdministrationIds[1]);
    }

    [Fact]
    public async Task CreateProduct_WithActiveProductExtensionsWithExactDuplicateCombinations_ReturnsOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var routeOfAdministrationEntity = await dbContext.RouteOfAdministrations.GetRandomEntity();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntity.Id])
            .Build();

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntity.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2)
            .Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productRequest.Name);
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);
        productResponse.ProductTypes.Should().HaveCount(3);
        productResponse.ProductTypes.Select(x => x.Id).Should().BeEquivalentTo(productTypeIds);

        productResponse.ProductExtensions.Should().HaveCount(2);
        productResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel1.RouteOfAdministrationIds);
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        productResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        productResponse.ProductExtensions[1].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel2.RouteOfAdministrationIds);
        productResponse.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionsWithMatchingRoutesOfAdministrationCombinations_ReturnsOk()
    {
        //Arrange
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var routeOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(2);
        var routeOfAdministrationEntities1 = routeOfAdministrationEntities[0];
        var routeOfAdministrationEntities2 = routeOfAdministrationEntities[1];

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntities1.Id])
            .Build();

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntities1.Id, routeOfAdministrationEntities2.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2)
            .Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productRequest.Name);
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(3);
        productResponse.DrugSubstances.Select(x => x.Id).Should().BeEquivalentTo(drugSubstanceIds);

        productResponse.ProductExtensions.Should().HaveCount(2);
        productResponse.ProductExtensions[0].Pcid.Should().Be(productExtensionModel1.Pcid);
        productResponse.ProductExtensions[0].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel1.RouteOfAdministrationIds);
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(productExtensionModel1.DosageFormId);

        productResponse.ProductExtensions[1].Pcid.Should().Be(productExtensionModel2.Pcid);
        productResponse.ProductExtensions[1].RoutesOfAdministration.Select(x => x.Id).Should().BeEquivalentTo(productExtensionModel2.RouteOfAdministrationIds);
        productResponse.ProductExtensions[1].DosageForm.Id.Should().Be(productExtensionModel2.DosageFormId);
    }

    [Fact]
    public async Task CreateProduct_WithProductExtensionsWithDifferentCombinations_ReturnsOk()
    {
        //Arrange
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 2);
        var dosageForm = await dbContext.DosageForms.GetRandomEntities(2);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var routeOfAdministrationEntities = await dbContext.RouteOfAdministrations.GetRandomEntities(2);
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).GetRandomEntities(3);
        var productTypeIds = productTypes.Select(p => p.Id).ToList();

        var productExtensionModel1 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm[0].Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntities[0].Id])
            .Build();

        var productExtensionModel2 = ProductExtensionSdkModelBuilder.Default()
            .WithIsActive(true)
            .WithPcid(Fake.ProductExtension.PCID)
            .WithDosageFormId(dosageForm[1].Id)
            .WithRouteOfAdministrationIds([routeOfAdministrationEntities[1].Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(Fake.Product.Name)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel1, productExtensionModel2)
            .Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.DrugSubstances.Should().HaveCount(2);
        productResponse.ProductTypes.Should().HaveCount(3);
        productResponse.ProductExtensions.Should().HaveCount(2);
        productResponse.ProductExtensions[0].IsActive.Should().Be(true);
        productResponse.ProductExtensions[0].DosageForm.Id.Should().Be(dosageForm[0].Id);
        productResponse.ProductExtensions[0].RoutesOfAdministration.Should().HaveCount(1);
        productResponse.ProductExtensions[0].RoutesOfAdministration[0].Id.Should().Be(routeOfAdministrationEntities[0].Id);

        productResponse.ProductExtensions[1].IsActive.Should().Be(true);
        productResponse.ProductExtensions[1].DosageForm.Id.Should().Be(dosageForm[1].Id);
        productResponse.ProductExtensions[1].RoutesOfAdministration.Should().HaveCount(1);
        productResponse.ProductExtensions[1].RoutesOfAdministration[0].Id.Should().Be(routeOfAdministrationEntities[1].Id);
    }

    [Fact]
    public async Task CreateProduct_WithProductTypes_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.ProductTypes.Should().HaveCount(5);

        productResponse.ProductTypes[0].Id.Should().Be(productTypes[0].Id);
        productResponse.ProductTypes[1].Id.Should().Be(productTypes[1].Id);
        productResponse.ProductTypes[2].Id.Should().Be(productTypes[2].Id);
        productResponse.ProductTypes[3].Id.Should().Be(productTypes[3].Id);
        productResponse.ProductTypes[4].Id.Should().Be(productTypes[4].Id);

        productResponse.ProductTypes[0].Name.Should().Be(productTypes[0].Name);
        productResponse.ProductTypes[1].Name.Should().Be(productTypes[1].Name);
        productResponse.ProductTypes[2].Name.Should().Be(productTypes[2].Name);
        productResponse.ProductTypes[3].Name.Should().Be(productTypes[3].Name);
        productResponse.ProductTypes[4].Name.Should().Be(productTypes[4].Name);
    }

    [Fact]
    public async Task CreateProduct_WithoutProductTypes_ThrowsArgumentException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithProductExtensions(productExtensionModel)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes([])
            .Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("'Product Type Ids' must not be empty.");
    }

    [Fact]
    public async Task CreateProduct_WithNotCategorizedProductType_ReturnsOk()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var notCategorizedProductType = ApiTestHelper.NotCategorizedProductType(dbContext);

        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds.Take(1).ToList())
            .WithProductTypes([notCategorizedProductType.Id])
            .WithProductExtensions(productExtensionModel)
            .Build();

        //Act
        var createProductResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);
        createProductResponse.Should().NotBeNull();
        var productResponse = await productApi.GetProductAsync(createProductResponse.Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        productResponse.Should().NotBeNull();
        productResponse.Name.Should().Be(productName);
        productResponse.ProductTypes.Should().HaveCount(1);

        productResponse.ProductTypes[0].Id.Should().Be(notCategorizedProductType.Id);
    }

    [Fact]
    public async Task CreateProduct_WithInvalidCombinationOfProductTypes_ThrowsInvalidCombinationException()
    {
        //Arrange
        var productName = Fake.Product.Name;
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 3);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await ApiTestHelper.CategorizedProductTypes(dbContext).Take(5).ToListAsync();
        var productTypeIds = productTypes.Select(p => p.Id).ToList();
        var notCategorizedProduct = ApiTestHelper.NotCategorizedProductType(dbContext);
        productTypeIds.Add(notCategorizedProduct.Id);
        var dosageForm = await dbContext.DosageForms.GetRandomEntity();
        var routeOfAdministration = await dbContext.RouteOfAdministrations.GetRandomEntity();
        var productExtensionModel = ProductExtensionSdkModelBuilder.Default()
            .WithDosageFormId(dosageForm.Id)
            .WithRouteOfAdministrationIds([routeOfAdministration.Id])
            .Build();

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithName(productName)
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions(productExtensionModel).Build();

        //Act
        var productResponse = () => productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        //Assert
        var exception = await productResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity with \\\"ProductTypeIds\\\" = ({string.Join(',', productTypeIds)}) is not a valid combination.");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
