﻿using AutoMapper;
using Axon.HAComms.Application.Commands.DrugSubstances.Create;
using Axon.HAComms.Application.Commands.DrugSubstances.Update;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings;

public class DrugSubstancesMappingProfile : Profile
{
    public DrugSubstancesMappingProfile()
    {
        CreateMap<DrugSubstance, DrugSubstanceModel>()
            .ForMember(dest => dest.IsAssociatedToComment, o => o.Ignore());
        CreateMap<DrugSubstanceModel, DrugSubstance>()
            .ForMember(dest => dest.DrugSubstanceProducts, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.Products, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore());
        CreateMap<CreateDrugSubstanceCommandRequest, DrugSubstance>()
            .ForMember(dest => dest.Name, o => o.MapFrom(src => string.IsNullOrEmpty(src.Name.Trim()) ? Constants.NotAssigned : src.Name))
            .ForMember(dest => dest.DrugSubstanceProducts, o => o.Ignore())
            .ForMember(dest => dest.Products, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore());
        CreateMap<UpdateDrugSubstanceCommandRequest, DrugSubstance>()
            .ForMember(dest => dest.Name, o => o.MapFrom(src => string.IsNullOrEmpty(src.Name.Trim()) ? Constants.NotAssigned : src.Name))
            .ForMember(dest => dest.DrugSubstanceProducts, o => o.Ignore())
            .ForMember(dest => dest.Products, o => o.Ignore())
            .ForMember(dest => dest.Comments, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore());
        CreateMap<DrugSubstance, CreateDrugSubstanceCommandResponse>();
        CreateMap<DrugSubstance, UpdateDrugSubstanceCommandRequest>();
        CreateMap<DrugSubstance, UpdateDrugSubstanceCommandResponse>();
    }
}
