﻿using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Queries.DrugSubstances.PagedListQuery;

internal class GetDrugSubstancesPagedListQueryHandler(IDrugSubstancesRepository drugSubstancesRepo) :
    IRequestHandler<GetDrugSubstancesPagedListQueryRequest, ApiPagedListResult<DrugSubstancePagedListModel>>
{
    private readonly Dictionary<string, Expression<Func<DrugSubstance, object>>> sortExpressions =
        new()
        {
            { TableFilterConstants.Code, x => x.Code },
            { TableFilterConstants.Name, x => x.Name ?? string.Empty },
            { TableFilterConstants.CreatedDate, x => x.CreatedDate },
            { TableFilterConstants.LastUpdatedDate, x => x.LastUpdatedDate },
            { TableFilterConstants.LastUpdatedBy, x => x.LastUpdatedBy }
        };

    public async Task<ApiPagedListResult<DrugSubstancePagedListModel>> Handle(GetDrugSubstancesPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildDrugSubstance(request.Filters);
        var predicate = BuildSortExpression(request);
        var query = drugSubstancesRepo.GetQueryableItems();

        var entities = await query
            .FilterItems(expression, predicate, request.Skip, request.Take)
            .Select(x => new DrugSubstancePagedListModel(x.Id, x.Name, x.Code, x.Description, x.Comments.Count != 0, x.CreatedDate, x.LastUpdatedDate, x.LastUpdatedBy))
            .ToListAsync(cancellationToken: cancellationToken);

        return new ApiPagedListResult<DrugSubstancePagedListModel>(
            entities,
            new()
            {
                TotalItemCount = expression == null ? await query.CountAsync(cancellationToken) : await query.CountAsync(expression, cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }

    private Func<IQueryable<DrugSubstance>, IOrderedQueryable<DrugSubstance>> BuildSortExpression(GetDrugSubstancesPagedListQueryRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Order))
        {
            return sub => sub.OrderBy(x => x.Code);
        }

        var orderSegments = request.Order.Split("=>");
        var propName = orderSegments[0];
        var order = orderSegments[1];
        var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

        if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
        {
            return CompareAndOrderBy(func);
        }

        return sub => sub.OrderBy(x => x.Name);

        Func<IQueryable<DrugSubstance>, IOrderedQueryable<DrugSubstance>> CompareAndOrderBy<TKey>(
            Expression<Func<DrugSubstance, TKey>> expression)
        {
            return isAsc ?
                sub => sub.OrderBy(expression) :
                sub => sub.OrderByDescending(expression);
        }
    }
}
