﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;

public class CreateCommunicationCommandRequestBuilder
{
    private string subject = Fake.Communication.Subject;
    private int submissionTypeId = Fake.SubmissionType.Id;
    private int countryId = Fake.Country.Id;
    private DateTime dateOfCommunication = DateTime.Now;
    private IList<ApplicationModel> applications = [];
    private CreateCommentCommandRequest comment = new();

    public static CreateCommunicationCommandRequestBuilder Default() => new();

    public CreateCommunicationCommandRequest Build()
    {
        return new CreateCommunicationCommandRequest(
            subject,
            submissionTypeId,
            countryId,
            dateOfCommunication,
            [.. applications],
            comment);
    }

    public CreateCommunicationCommandRequestBuilder WithSubject(string subject)
    {
        this.subject = subject;
        return this;
    }

    public CreateCommunicationCommandRequestBuilder WithComment(CreateCommentCommandRequest comment)
    {
        this.comment = comment;
        return this;
    }

    public CreateCommunicationCommandRequestBuilder WithSubmissionTypeId(int submissionTypeId)
    {
        this.submissionTypeId = submissionTypeId;
        return this;
    }

    public CreateCommunicationCommandRequestBuilder WithCountryId(int countryId)
    {
        this.countryId = countryId;
        return this;
    }

    public CreateCommunicationCommandRequestBuilder WithDateOfCommunication(DateTime dateOfCommunication)
    {
        this.dateOfCommunication = dateOfCommunication;
        return this;
    }

    public CreateCommunicationCommandRequestBuilder WithApplications(params ApplicationModel[] applications)
    {
        this.applications = [.. applications];
        return this;
    }
}
