﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class CommentProductExtensionRoutesOfAdministration : BaseEntity
{
    public int CommentProductExtensionId { get; set; }
    public CommentProductExtension CommentProductExtension { get; set; } = new();

    public int RouteOfAdministrationId { get; set; }
    public RouteOfAdministration? RouteOfAdministration { get; set; }
}
