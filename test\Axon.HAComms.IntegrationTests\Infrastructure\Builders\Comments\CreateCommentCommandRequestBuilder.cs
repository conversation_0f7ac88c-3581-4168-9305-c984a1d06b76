﻿using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;

public class CreateCommentCommandRequestBuilder
{
    private readonly string birdsLinkToBiResponse = Fake.Comment.BIRDSLinkToBIResponse;
    private readonly string birdsLinkToBisamp = Fake.Comment.BIRDSLinkToBISAMP;

    private int communicationId = Fake.Communication.Id;
    private string description = Fake.Comment.Description;
    private string question = Fake.Comment.Question;
    private string response = Fake.Comment.Response;
    private List<ProductExtensionCommentModel> productExtensions = new List<ProductExtensionCommentModel>() { new ProductExtensionCommentModel() { RouteOfAdministrationIds = Fake.Comment.RouteOfAdministrationIds, ProductExtensionId = Fake.Comment.ProductExtensionId } };
    private List<int> drugSubstanceIds = Fake.Comment.DrugSubstanceIds;
    private List<int> tagIds = Fake.Comment.TagIds;
    private bool isGeneralGuidance;
    private bool isQuestionIncluded;

    public static CreateCommentCommandRequestBuilder Default() => new();

    public CreateCommentCommandRequest Build()
    {
        return new CreateCommentCommandRequest(communicationId, description, question, response, birdsLinkToBiResponse, birdsLinkToBisamp, productExtensions,
            drugSubstanceIds, tagIds, isGeneralGuidance, isQuestionIncluded);
    }

    public CreateCommentCommandRequestBuilder WithDescription(string commentDescription)
    {
        description = commentDescription;
        return this;
    }

    public CreateCommentCommandRequestBuilder WithQuestion(string commentQuestion)
    {
        question = commentQuestion;
        return this;
    }

    public CreateCommentCommandRequestBuilder WithResponse(string commentResponse)
    {
        response = commentResponse;
        return this;
    }

    public CreateCommentCommandRequestBuilder WithProductExtensions(params ProductExtensionCommentModel[] commentProductExtensions)
    {
        productExtensions = commentProductExtensions.ToList();
        return this;
    }

    public CreateCommentCommandRequestBuilder WithDrugSubstanceIds(params int[] commentDrugSubstanceIds)
    {
        drugSubstanceIds = [.. commentDrugSubstanceIds];
        return this;
    }

    public CreateCommentCommandRequestBuilder WithTagIds(params int[] commentTagIds)
    {
        tagIds = [.. commentTagIds];
        return this;
    }

    public CreateCommentCommandRequestBuilder WithCommunicationId(int commentCommunicationId)
    {
        communicationId = commentCommunicationId;
        return this;
    }

    public CreateCommentCommandRequestBuilder AsGeneralGuidance()
    {
        this.isGeneralGuidance = true;
        return this;
    }

    public CreateCommentCommandRequestBuilder AsNonGeneralGuidance()
    {
        this.isGeneralGuidance = false;
        return this;
    }

    public CreateCommentCommandRequestBuilder AsCommentOnly()
    {
        this.isQuestionIncluded = false;
        return this;
    }

    public CreateCommentCommandRequestBuilder WithQuestionIncluded()
    {
        this.isQuestionIncluded = true;
        return this;
    }
}
