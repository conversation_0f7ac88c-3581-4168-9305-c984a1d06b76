﻿using Axon.HAComms.Application.Models.ProductExtensions;
using MediatR;
using Axon.HAComms.Application.Models.Products;

namespace Axon.HAComms.Application.Commands.Products.Create;

public class CreateProductCommandRequest(
    string name,
    bool isActive,
    ICollection<ProductExtensionModel> productExtensions,
    List<int> drugSubstanceIds,
    List<int> productTypeIds,
    string notAssigned = default!)
    : ProductRequestModel(name, isActive, productExtensions, drugSubstanceIds, productTypeIds, notAssigned), IRequest<CreateProductCommandResponse>;
