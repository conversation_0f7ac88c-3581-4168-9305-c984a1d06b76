﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using System.Reflection;

namespace Axon.HAComms.ArchTests;

public class PermissionValidator
{
    private readonly Dictionary<string, string[]> correctPermissions = new()
    {
        { "ApplicationsController:GetAllApplicationNumbersAsync", new[] { nameof(HacommsPermissions.ViewApplicationNumber) } },

        { "AuditController:GetAsync", new[] { nameof(HacommsPermissions.ViewAudit) } },

        { "CommentsController:SearchAsync", new[] { nameof(HacommsPermissions.CanSearchComments) } },
        { "CommentsController:GetSearchDetailsByCommunicationIdAndCommentIdAsync", new[] { nameof(HacommsPermissions.CanSearchComments) } },
        { "CommentsController:GetCommentsByCommunicationIdAsync", new[] { nameof(HacommsPermissions.CanSearchComments) } },
        { "CommentsController:CreateCommentAsync", new[] { nameof(HacommsPermissions.CreateComment) } },
        { "CommentsController:UpdateCommentAsync", new[] { nameof(HacommsPermissions.EditComment) } },
        { "CommentsController:DeleteCommentAsync", new[] { nameof(HacommsPermissions.DeleteComment) } },
        { "CommentsController:DeleteCommentsByCommunicationAndProductAsync", new[] { nameof(HacommsPermissions.DeleteComment) } },

        { "CommunicationsController:GetPagedAsync", new[] { nameof(HacommsPermissions.ViewCommunication) } },
        { "CommunicationsController:GetCommunicationByIdAsync", new[] { nameof(HacommsPermissions.ViewCommunication) } },
        { "CommunicationsController:CreateCommunicationAsync", new[] { nameof(HacommsPermissions.CreateCommunication) } },
        { "CommunicationsController:UpdateCommunicationAsync", new[] { nameof(HacommsPermissions.EditCommunication) } },
        { "CommunicationsController:CompleteCommunicationAsync", new[] { nameof(HacommsPermissions.EditCommunication) } },
        { "CommunicationsController:ReinstateCommunicationAsync", new[] { nameof(HacommsPermissions.EditCommunication) } },
        { "CommunicationsController:DeleteCommunicationAsync", new[] { nameof(HacommsPermissions.DeleteCommunication) } },

        { "CountriesController:GetCountryListAsync", new[] { nameof(HacommsPermissions.ViewCountry) } },

        { "DosageFormsController:GetDosageFormListAsync", new[] { nameof(HacommsPermissions.ViewDosageFormList) } },
        { "DosageFormsController:GetPagedAsync", new[] { nameof(HacommsPermissions.ViewDosageForm) } },
        { "DosageFormsController:CreateDosageFormAsync", new[] { nameof(HacommsPermissions.CreateDosageForm) } },
        { "DosageFormsController:UpdateDosageFormAsync", new[] { nameof(HacommsPermissions.EditDosageForm) } },
        { "DosageFormsController:DeleteDosageFormAsync", new[] { nameof(HacommsPermissions.DeleteDosageForm) } },

        { "DrugSubstancesController:GetDrugSubstancesAsync", new[] { nameof(HacommsPermissions.ViewSubstanceList) } },
        { "DrugSubstancesController:GetPagedAsync", new[] { nameof(HacommsPermissions.ViewSubstance) } },
        { "DrugSubstancesController:CreateDrugSubstanceAsync", new[] { nameof(HacommsPermissions.CreateSubstance) } },
        { "DrugSubstancesController:UpdateDrugSubstanceAsync", new[] { nameof(HacommsPermissions.EditSubstance) } },
        { "DrugSubstancesController:DeleteDrugSubstanceAsync", new[] { nameof(HacommsPermissions.DeleteSubstance) } },

        { "ProductsController:GetPagedAsync", new[] { nameof(HacommsPermissions.ViewProduct) } },
        { "ProductsController:GetByIdAsync", new[] { nameof(HacommsPermissions.ViewProduct) } },
        { "ProductsController:GetProductsAsync", new[] { nameof(HacommsPermissions.ViewProductList) } },
        { "ProductsController:GetAllProductCodesAsync", new[] { nameof(HacommsPermissions.ViewProductCode) } },
        { "ProductsController:CreateProductAsync", new[] { nameof(HacommsPermissions.CreateProduct) } },
        { "ProductsController:UpdateProductAsync", new[] { nameof(HacommsPermissions.EditProduct) } },
        { "ProductsController:DeleteProductAsync", new[] { nameof(HacommsPermissions.DeleteProduct) } },

        { "ProductTypesController:GetAllAsync", new[] { nameof(HacommsPermissions.ViewProductType) } },

        { "RouteOfAdministrationController:GetRouteOfAdministrationListAsync", new[] { nameof(HacommsPermissions.ViewRouteOfAdministrationList) } },
        { "RouteOfAdministrationController:GetPagedAsync", new [] { nameof(HacommsPermissions.ViewRouteOfAdministration) } },
        { "RouteOfAdministrationController:CreateRouteOfAdministrationAsync", new[] { nameof(HacommsPermissions.CreateRouteOfAdministration) } },
        { "RouteOfAdministrationController:UpdateRouteOfAdministrationAsync", new[] { nameof(HacommsPermissions.EditRouteOfAdministration) } },
        { "RouteOfAdministrationController:DeleteRouteOfAdministrationAsync", new[] { nameof(HacommsPermissions.DeleteRouteOfAdministration) } },

        { "SubmissionsController:GetAllSubmissionNumbersAsync", new[] { nameof(HacommsPermissions.ViewSubmissionNumber) } },

        { "SubmissionTypesController:GetSubmissionTypeListAsync", new[] { nameof(HacommsPermissions.ViewSubmissionType) } },

        { "TagController:GetTagsListAsync", new[] { nameof(HacommsPermissions.ViewTagList) } },
        { "TagController:GetPagedAsync", new[] { nameof(HacommsPermissions.ViewTag) } },
        { "TagController:CreateTagAsync", new[] { nameof(HacommsPermissions.CreateTag) } },
        { "TagController:UpdateTagAsync", new[] { nameof(HacommsPermissions.EditTag) } },
        { "TagController:DeleteTagAsync", new[] { nameof(HacommsPermissions.DeleteTag) } },
    };

    public IEnumerable<string> Validate(MethodInfo mi)
    {
        var controller = mi.DeclaringType?.Name ?? "(method-info-null)";
        var action = mi.Name;
        var endpoint = $"{controller}:{action}";
        var failures = new List<string>();
        var hpa = mi.GetCustomAttributes<HasPermissionsAttribute>().ToArray();
        if (correctPermissions.TryGetValue(endpoint, out var perms))
        {
            //TODO: implement when HasPermissionsAttribute exposes the Permission property in Axon.Core.Shared 
        }
        else
        {
            failures.Add($"INCORRECT: {endpoint} : HasPermissionsAttribute found but no expected permissions available for testing.");
        }

        return failures;
    }
}
