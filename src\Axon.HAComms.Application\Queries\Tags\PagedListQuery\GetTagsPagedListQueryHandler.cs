﻿using Axon.HAComms.Application.Builders;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Extensions;
using Axon.HAComms.Application.Models.Tags;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Phlex.Core.Api.Abstractions.Models;
using System.Linq.Expressions;

namespace Axon.HAComms.Application.Queries.Tags.PagedListQuery;

internal class GetTagsPagedListQueryHandler(ITagRepository tagsRepo) :
    IRequestHandler<GetTagsPagedListQueryRequest, ApiPagedListResult<TagPagedListModel>>
{
    private readonly ITagRepository tagsRepo = tagsRepo ?? throw new ArgumentNullException(nameof(tagsRepo));
    private readonly Dictionary<string, Expression<Func<Tag, object>>> sortExpressions =
        new()
        {
            { TableFilterConstants.Name, x => x.Name ?? string.Empty },
            { TableFilterConstants.Description, x => x.Description ?? string.Empty },
            { TableFilterConstants.CreatedDate, x => x.CreatedDate },
            { TableFilterConstants.CreatedBy, x => x.CreatedBy },
            { TableFilterConstants.LastUpdatedDate, x => x.LastUpdatedDate },
            { TableFilterConstants.LastUpdatedBy, x => x.LastUpdatedBy }
        };

    public async Task<ApiPagedListResult<TagPagedListModel>> Handle(GetTagsPagedListQueryRequest request, CancellationToken cancellationToken)
    {
        var expression = request.Filters == null ? null : ExpressionBuilder.BuildTags(request.Filters);
        var predicate = BuildSortExpression(request);
        var query = tagsRepo.GetQueryableItems();

        var entities = await query
            .FilterItems(expression, predicate, request.Skip, request.Take)
            .Select(x => new TagPagedListModel(x.Id, x.Name, x.Description, x.Comments != null && x.Comments.Count != 0, x.CreatedDate, x.CreatedBy, x.LastUpdatedDate, x.LastUpdatedBy))
            .ToListAsync(cancellationToken: cancellationToken);

        return new ApiPagedListResult<TagPagedListModel>(
            entities,
            new()
            {
                TotalItemCount = expression == null ? await query.CountAsync(cancellationToken) : await query.CountAsync(expression, cancellationToken),
                Offset = request.Skip,
                Limit = request.Take,
            });
    }

    private Func<IQueryable<Tag>, IOrderedQueryable<Tag>> BuildSortExpression(GetTagsPagedListQueryRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Order))
        {
            return sub => sub.OrderBy(x => x.Name);
        }

        var orderSegments = request.Order.Split("=>");
        var propName = orderSegments[0];
        var order = orderSegments[1];
        var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

        if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
        {
            return CompareAndOrderBy(func);
        }

        return sub => sub.OrderBy(x => x.Name);

        Func<IQueryable<Tag>, IOrderedQueryable<Tag>> CompareAndOrderBy<TKey>(
            Expression<Func<Tag, TKey>> expression)
        {
            return isAsc ?
                sub => sub.OrderBy(expression) :
                sub => sub.OrderByDescending(expression);
        }
    }
}
