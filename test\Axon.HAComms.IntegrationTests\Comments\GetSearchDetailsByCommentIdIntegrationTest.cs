﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetSearchDetailsByCommentIdIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetSearchDetailsByCommentId_NonGeneralGuidanceComment_HasOtherComments_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product1ProductExtension = product1.ProductExtensions[0];
        var product11ProductExtension = product1.ProductExtensions[1];
        var product12ProductExtension = product1.ProductExtensions[2];
        var product2ProductExtension = product2.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var product1DrugSubstances = product1.DrugSubstances.Select(d => d.Id).ToArray();
        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1DrugSubstances)
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();
        
        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment11 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product11ProductExtension.Id,
                RouteOfAdministrationIds = product11ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1DrugSubstances)
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment11);

        var comment12 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product12ProductExtension.Id,
                RouteOfAdministrationIds = product12ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1DrugSubstances)
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment12);

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product2ProductExtension.Id,
                RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        var commentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Act
        var responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponse.Data[^1].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(2);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.AllProducts[1].Id.Should().Be(product2.Id);
        responseObj.AllProducts[1].Name.Should().Be(product2.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().Be(product1.Name);
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment1.Description);
        responseObj.Comment.ProductExtensions.Should().NotBeNull();
        responseObj.Comment.ProductExtensions[0].Pcid.Should().Be(product1ProductExtension.Pcid);
        responseObj.Comment.ProductExtensions[0].DosageFormId.Should().Be(product1ProductExtension.DosageForm.Id);
        responseObj.Comment.ProductExtensions[0].DosageFormName.Should().Be(product1ProductExtension.DosageForm.Name);
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Id));
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Name));
        responseObj.Comment.DrugSubstances.Should().HaveCount(product1.DrugSubstances.Count);
        responseObj.Comment.DrugSubstances[0].Name.Should().Be(product1.DrugSubstances[0].Name);
        responseObj.Comment.DrugSubstances[0].Code.Should().Be(product1.DrugSubstances[0].Code);
        responseObj.Comment.DrugSubstances[1].Name.Should().Be(product1.DrugSubstances[1].Name);
        responseObj.Comment.DrugSubstances[1].Code.Should().Be(product1.DrugSubstances[1].Code);
        responseObj.Comment.DrugSubstances[2].Name.Should().Be(product1.DrugSubstances[2].Name);
        responseObj.Comment.DrugSubstances[2].Code.Should().Be(product1.DrugSubstances[2].Code);
        responseObj.Comment.ProductTypes.Should().HaveCount(product1.ProductTypes.Count);
        responseObj.Comment.ProductTypes[0].Should().Be(product1.ProductTypes[0].Name);
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment1.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment1.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));
    }

    [Fact]
    public async Task GetSearchDetailsByCommentId_NonGeneralGuidanceComment_NoOtherComments_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi, 3);
        var product1ProductExtension = product1.ProductExtensions[0];
        var product2ProductExtension = product2.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product2ProductExtension.Id,
                RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        var commentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Act
        var responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponse.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(2);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.AllProducts[1].Id.Should().Be(product2.Id);
        responseObj.AllProducts[1].Name.Should().Be(product2.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().Be(product1.Name);
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment1.Description);
        responseObj.Comment.ProductExtensions.Should().NotBeNull();
        responseObj.Comment.ProductExtensions[0].Pcid.Should().Be(product1ProductExtension.Pcid);
        responseObj.Comment.ProductExtensions[0].DosageFormId.Should().Be(product1ProductExtension.DosageForm.Id);
        responseObj.Comment.ProductExtensions[0].DosageFormName.Should().Be(product1ProductExtension.DosageForm.Name);
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Id));
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Name));
        responseObj.Comment.DrugSubstances.Should().HaveCount(product1.DrugSubstances.Count);
        responseObj.Comment.DrugSubstances[0].Name.Should().Be(product1.DrugSubstances[0].Name);
        responseObj.Comment.DrugSubstances[0].Code.Should().Be(product1.DrugSubstances[0].Code);
        responseObj.Comment.DrugSubstances[1].Name.Should().Be(product1.DrugSubstances[1].Name);
        responseObj.Comment.DrugSubstances[1].Code.Should().Be(product1.DrugSubstances[1].Code);
        responseObj.Comment.DrugSubstances[2].Name.Should().Be(product1.DrugSubstances[2].Name);
        responseObj.Comment.DrugSubstances[2].Code.Should().Be(product1.DrugSubstances[2].Code);
        responseObj.Comment.ProductTypes.Should().HaveCount(product1.ProductTypes.Count);
        responseObj.Comment.ProductTypes[0].Should().Be(product1.ProductTypes[0].Name);
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment1.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment1.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));
    }

    [Fact]
    public async Task GetSearchDetailsByCommentId_GeneralGuidanceComment_NoOtherComments_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithDrugSubstanceIds()
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithQuestionIncluded()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        var commentsResponseGeneralGuidance = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        commentsResponseGeneralGuidance.Should().NotBeNull();
        commentsResponseGeneralGuidance.Data.Should().NotBeNull();
        commentsResponseGeneralGuidance.Data.Should().HaveCount(1);

        //Act
        var responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponseGeneralGuidance.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(1);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().BeNullOrEmpty();
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment1.Description);
        responseObj.Comment.ProductExtensions.Should().BeNullOrEmpty();
        responseObj.Comment.DrugSubstances.Should().BeNullOrEmpty();
        responseObj.Comment.ProductTypes.Should().BeNullOrEmpty();
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment1.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment1.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));

        var getProductCommentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);
        getProductCommentsResponse.Should().NotBeNull();
        getProductCommentsResponse.Data.Should().NotBeNull();
        getProductCommentsResponse.Data.Should().HaveCount(1);
        responseObj = await commentsApi.SearchDetailsAsync(communicationId, getProductCommentsResponse.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(1);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().Be(product1.Name);
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment2.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment2.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().BeNull();
        responseObj.Comment.Question.Should().Be(comment2.Question);
        responseObj.Comment.Response.Should().Be(comment2.Response);
        responseObj.Comment.ProductExtensions.Should().NotBeNull();
        responseObj.Comment.ProductExtensions[0].Pcid.Should().Be(product1ProductExtension.Pcid);
        responseObj.Comment.ProductExtensions[0].DosageFormId.Should().Be(product1ProductExtension.DosageForm.Id);
        responseObj.Comment.ProductExtensions[0].DosageFormName.Should().Be(product1ProductExtension.DosageForm.Name);
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrationIds.Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Id));
        responseObj.Comment.ProductExtensions[0].RouteOfAdministrations.Select(r => r.Name).Should().BeEquivalentTo(product1ProductExtension.RoutesOfAdministration.Select(r => r.Name));
        responseObj.Comment.DrugSubstances.Select(d => d.Name).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Name));
        responseObj.Comment.DrugSubstances.Select(d => d.Code).Should().BeEquivalentTo(product1.DrugSubstances.Select(d => d.Code));
        responseObj.Comment.ProductTypes.Should().HaveCount(product1.ProductTypes.Count);
        responseObj.Comment.ProductTypes.Should().BeEquivalentTo(product1.ProductTypes.Select(t => t.Name));
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment2.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment2.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));
    }

    [Fact]
    public async Task GetSearchDetailsByCommentId_GeneralGuidanceComment_HasOtherComments_ValidRequest_ReturnsOk()
    {
        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product1.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithDrugSubstanceIds()
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment11 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithDrugSubstanceIds()
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment11);

        var comment12 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .AsGeneralGuidance()
            .WithDrugSubstanceIds()
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment12);

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithQuestionIncluded()
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        var commentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT);
        commentsResponse.Should().NotBeNull();
        commentsResponse.Data.Should().NotBeNull();
        commentsResponse.Data.Should().HaveCount(3);

        //Act
        var responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponse.Data[2].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(1);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().BeNullOrEmpty();
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment1.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment1.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment1.Description);
        responseObj.Comment.ProductExtensions.Should().BeNullOrEmpty();
        responseObj.Comment.DrugSubstances.Should().BeNullOrEmpty();
        responseObj.Comment.ProductTypes.Should().BeNullOrEmpty();
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment1.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment1.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));

        responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponse.Data[1].Id, TenantConstants.DEFAULT_TENANT);

        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(1);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().BeNullOrEmpty();
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment11.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment11.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment11.Description);
        responseObj.Comment.Question.Should().BeNull();
        responseObj.Comment.Response.Should().BeNull();
        responseObj.Comment.ProductExtensions.Should().BeNullOrEmpty();
        responseObj.Comment.DrugSubstances.Should().BeNullOrEmpty();
        responseObj.Comment.ProductTypes.Should().BeNullOrEmpty();
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment11.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment11.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));

        responseObj = await commentsApi.SearchDetailsAsync(communicationId, commentsResponse.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        responseObj.Applications.Single().Number.Should().Be(request.Applications.Single().Number);
        responseObj.Applications.Single().Submissions.Single().Number.Should().Be(request.Applications.Single().Submissions.Single().Number);
        responseObj.Country.Should().Be(country.Name);
        responseObj.DateOfCommunication.Should().BeSameDateAs(request.DateOfCommunication);
        responseObj.IsCompleted.Should().BeFalse();
        responseObj.AllProducts.Should().HaveCount(1);
        responseObj.AllProducts[0].Id.Should().Be(product1.Id);
        responseObj.AllProducts[0].Name.Should().Be(product1.Name);
        responseObj.Subject.Should().Be(request.Subject);
        responseObj.Comment.Should().NotBeNull();
        responseObj.Comment.ProductName.Should().BeNullOrEmpty();
        responseObj.Comment.BirdsLinkToBIResponse.Should().Be(comment12.BirdsLinkToBIResponse);
        responseObj.Comment.BirdsLinkToBISAMP.Should().Be(comment12.BirdsLinkToBISAMP);
        responseObj.Comment.Description.Should().Be(comment12.Description);
        responseObj.Comment.Question.Should().BeNull();
        responseObj.Comment.Response.Should().BeNull();
        responseObj.Comment.ProductExtensions.Should().BeNullOrEmpty();
        responseObj.Comment.DrugSubstances.Should().BeNullOrEmpty();
        responseObj.Comment.ProductTypes.Should().BeNullOrEmpty();
        responseObj.Comment.IsGeneralGuidance.Should().Be(comment12.IsGeneralGuidance);
        responseObj.Comment.IsQuestionIncluded.Should().Be(comment12.IsQuestionIncluded);
        responseObj.Comment.Tags.Should().HaveCount(tagsForComment.Count);
        responseObj.Comment.Tags.Select(t => t.Id).Should().BeEquivalentTo(tagsForComment.Select(t => t.Id));
        responseObj.Comment.Tags.Select(t => t.Name).Should().BeEquivalentTo(tagsForComment.Select(t => t.Name));
    }

    [Fact]
    public async Task GetSearchDetailsByCommentId_InvalidCommunicationId_ThrowsEntityNotFoundException()
    {
        //Act
        var fakeCommunicationId = Fake.Communication.Id;

        //Arrange
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product1.ProductExtensions[0];
        var product2ProductExtension = product2.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product2ProductExtension.Id,
                RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        //Act
        var commentsResponse = await commentsApi.GetCommentsByCommunicationIdAsync(communicationId, TenantConstants.DEFAULT_TENANT, product1.Id);
        commentsResponse.Should().NotBeNull();
        commentsResponse.Data.Should().NotBeNull();
        commentsResponse.Data.Should().HaveCount(1);
        var responseObj = () => commentsApi.SearchDetailsAsync(fakeCommunicationId, commentsResponse.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await responseObj.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"EntityNotFoundException: Entity \\\"Communication\\\" ({fakeCommunicationId}) was not found.");
    }

    [Fact]
    public async Task GetSearchDetailsByCommentId_InvalidCommentId_ThrowsEntityNotFoundException()
    {
        //Arrange
        var fakeCommentId = Fake.Comment.Id;
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product1ProductExtension = product1.ProductExtensions[0];
        var product2ProductExtension = product2.ProductExtensions[0];
        var tagsForComment = await TagsTestEntitiesBuilder.Build(dbContext, 2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment1 = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product1ProductExtension.Id,
                RouteOfAdministrationIds = product1ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment1).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();
        var communicationId = response.Id;

        var comment2 = CreateCommentCommandRequestBuilder.Default()
            .WithCommunicationId(communicationId)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = product2ProductExtension.Id,
                RouteOfAdministrationIds = product2ProductExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();
        await commentsApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, comment2);

        //Act
        var responseObj = () => commentsApi.SearchDetailsAsync(communicationId, fakeCommentId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await responseObj.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"EntityNotFoundException: Entity \\\"Comment\\\" ({fakeCommentId}) was not found.");
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.Comments.Clear();
        dbContext.Communications.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
