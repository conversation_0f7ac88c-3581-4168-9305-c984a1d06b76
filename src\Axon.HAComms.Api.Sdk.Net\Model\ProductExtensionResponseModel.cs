/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// ProductExtensionResponseModel
    /// </summary>
    [DataContract(Name = "ProductExtensionResponseModel")]
    public partial class ProductExtensionResponseModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductExtensionResponseModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="pcid">pcid.</param>
        /// <param name="dosageForm">dosageForm.</param>
        /// <param name="routesOfAdministration">routesOfAdministration.</param>
        /// <param name="isActive">isActive.</param>
        /// <param name="isAssociatedToComment">isAssociatedToComment.</param>
        public ProductExtensionResponseModel(int id = default(int), string pcid = default(string), DosageFormModel dosageForm = default(DosageFormModel), List<RouteOfAdministrationModel> routesOfAdministration = default(List<RouteOfAdministrationModel>), bool isActive = default(bool), bool isAssociatedToComment = default(bool))
        {
            this.Id = id;
            this.Pcid = pcid;
            this.DosageForm = dosageForm;
            this.RoutesOfAdministration = routesOfAdministration;
            this.IsActive = isActive;
            this.IsAssociatedToComment = isAssociatedToComment;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Pcid
        /// </summary>
        [DataMember(Name = "pcid", EmitDefaultValue = true)]
        public string Pcid { get; set; }

        /// <summary>
        /// Gets or Sets DosageForm
        /// </summary>
        [DataMember(Name = "dosageForm", EmitDefaultValue = false)]
        public DosageFormModel DosageForm { get; set; }

        /// <summary>
        /// Gets or Sets RoutesOfAdministration
        /// </summary>
        [DataMember(Name = "routesOfAdministration", EmitDefaultValue = true)]
        public List<RouteOfAdministrationModel> RoutesOfAdministration { get; set; }

        /// <summary>
        /// Gets or Sets IsActive
        /// </summary>
        [DataMember(Name = "isActive", EmitDefaultValue = true)]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or Sets IsAssociatedToComment
        /// </summary>
        [DataMember(Name = "isAssociatedToComment", EmitDefaultValue = true)]
        public bool IsAssociatedToComment { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class ProductExtensionResponseModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Pcid: ").Append(Pcid).Append("\n");
            sb.Append("  DosageForm: ").Append(DosageForm).Append("\n");
            sb.Append("  RoutesOfAdministration: ").Append(RoutesOfAdministration).Append("\n");
            sb.Append("  IsActive: ").Append(IsActive).Append("\n");
            sb.Append("  IsAssociatedToComment: ").Append(IsAssociatedToComment).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
