﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.RoutesOfAdministration.Update;

public class UpdateRouteOfAdministrationCommandHandlerTests
{
    private readonly UpdateRouteOfAdministrationCommandHandler sut;
    private readonly IRouteOfAdministrationRepository routeOfAdministrationRepository;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public UpdateRouteOfAdministrationCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        routeOfAdministrationRepository = Substitute.For<IRouteOfAdministrationRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<UpdateRouteOfAdministrationCommandHandler>>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();
        auditService.When(a => a.LogAsync(correlationId, clientDetails, AuditEventType.ROUTE_OF_ADMINISTRATION_UPDATED, AuditEventCategory.ROUTES_OF_ADMINISTRATION,
                AuditEventDescription.ROUTE_OF_ADMINISTRATION_UPDATE, Arg.Any<RouteOfAdministration>(), Arg.Any<Func<Task>>()))
            .Do(callInfo => callInfo.Arg<Func<Task>>().Invoke());
        sut = new UpdateRouteOfAdministrationCommandHandler(routeOfAdministrationRepository, mapper, logger, correlationIdProvider, clientDetailsProvider, userProvider,
            auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var routeOfAdministrationName = Fake.RouteOfAdministration.Name;

        var entity = new RouteOfAdministration { Name = routeOfAdministrationName };
        routeOfAdministrationRepository.GetItemAsync(1).Returns(entity);

        var request = new UpdateRouteOfAdministrationCommandRequest(1, entity.Name);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Id.Should().Be(1);

        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService.Received(1).LogAsync(correlationId, clientDetails, AuditEventType.ROUTE_OF_ADMINISTRATION_UPDATED, AuditEventCategory.ROUTES_OF_ADMINISTRATION,
            AuditEventDescription.ROUTE_OF_ADMINISTRATION_UPDATE, Arg.Any<RouteOfAdministration>(), Arg.Any<Func<Task>>());
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new RouteOfAdministration { Name = Fake.RouteOfAdministration.Name };
        var request = new UpdateRouteOfAdministrationCommandRequest(Fake.RouteOfAdministration.Id, entity.Name);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
