﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common.Builders;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders;

public static class ApiTestHelper
{
    public static async Task<ProductModel> CreateProductForTest(
        HACommsContext dbContext,
        IProductsApi productApi,
        int numberOfExtensions = 1,
        int numberOfDrugSubstances = 3,
        int numberOfProductTypes = 3,
        List<RouteOfAdministration>? routeOfAdministrationList = null)
    {
        var drugSubstances = await DrugSubstancesTestEntitiesBuilder.Build(dbContext, numberOfDrugSubstances);
        var drugSubstanceIds = drugSubstances.Select(x => x.Id).ToList();
        var productTypes = await CategorizedProductTypes(dbContext).GetRandomEntities(numberOfProductTypes);
        var productTypeIds = productTypes.Select(x => x.Id).ToList();
        var dosageForms = await DosageFormsTestEntitiesBuilder.Build(dbContext, numberOfExtensions);
        routeOfAdministrationList ??= await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, numberOfExtensions);
        var productExtensions = new List<ProductExtensionModel>();

        for (var i = 0; i < numberOfExtensions; i++)
        {
            productExtensions.Add(ProductExtensionSdkModelBuilder.Default()
                .WithDosageFormId(dosageForms[i].Id)
                .WithRouteOfAdministrationIds(routeOfAdministrationList.Select(r => r.Id).ToList())
                .Build());
        }

        var productRequest = new CreateProductCommandRequestBuilder()
            .WithDrugSubstances(drugSubstanceIds)
            .WithProductTypes(productTypeIds)
            .WithProductExtensions([.. productExtensions]).Build();

        var productResponse = await productApi.CreateProductAsync(TenantConstants.DEFAULT_TENANT, productRequest);

        return await productApi.GetProductAsync(productResponse.Id, TenantConstants.DEFAULT_TENANT);
    }

    public static IQueryable<ProductType> CategorizedProductTypes(HACommsContext dbContext) =>
        dbContext.ProductTypes.Where(x => !x.Name.ToLower().Equals(Constants.NotCategorized.ToLower()));

    public static ProductType NotCategorizedProductType(HACommsContext dbContext) =>
        dbContext.ProductTypes.SingleOrDefault(x => x.Name.ToLower().Equals(Constants.NotCategorized.ToLower()))!;

    public static async Task<List<CreateCommentCommandRequest>> GenerateCommentsForProduct(HACommsContext dbContext, ProductModel product, int count = 15)
    {
        var comments = new List<CreateCommentCommandRequest>();

        var productExtension = product.ProductExtensions[0];
        var tagsForComment = await dbContext.Tags.GetRandomEntities(2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        for (var i = 0; i < count; i++)
        {
            comments.Add(CreateCommentCommandRequestBuilder.Default()
                .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
                .WithDrugSubstanceIds(product.DrugSubstances.Select(d => d.Id).ToArray())
                .WithTagIds(selectedTagIds)
                .Build()
            );
        }

        return comments;
    }

    public static async Task<CommunicationModel> CreateRandomCommunication(CommunicationsApi communicationApi, ProductModel product, HACommsContext dbContext)
    {
        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var productExtension = product.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 2);

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();

        var comment = CreateCommentCommandRequestBuilder.Default()
            .AsNonGeneralGuidance()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        var response = await communicationApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        var communication = await communicationApi.GetCommunicationAsync(response.Id, TenantConstants.DEFAULT_TENANT);
        return communication;
    }

    public static async Task<List<CreateCommentCommandRequest>> GenerateGeneralGuidanceComments(HACommsContext dbContext, int count = 15)
    {
        var comments = new List<CreateCommentCommandRequest>();
        var tagsForComment = await dbContext.Tags.GetRandomEntities(2);
        var selectedTagIds = tagsForComment.Select(x => x.Id).ToArray();

        for (var i = 0; i < count; i++)
        {
            comments.Add(CreateCommentCommandRequestBuilder.Default()
                .AsGeneralGuidance()
                .WithProductExtensions([])
                .WithDrugSubstanceIds([])
                .WithTagIds(selectedTagIds)
                .Build()
            );
        }

        return comments;
    }
}
