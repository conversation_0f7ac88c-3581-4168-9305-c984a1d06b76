{"users": [{"email": "<EMAIL>", "permissions": [{"Permission": "CanSearchComments", "HasScopes": false, "Permitted": true}, {"Permission": "CreateComment", "HasScopes": false, "Permitted": true}, {"Permission": "EditComment", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteComment", "HasScopes": false, "Permitted": true}, {"Permission": "ViewCommunication", "HasScopes": false, "Permitted": true}, {"Permission": "EditCommunication", "HasScopes": false, "Permitted": true}, {"Permission": "CreateCommunication", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteCommunication", "HasScopes": false, "Permitted": true}, {"Permission": "ViewCountry", "HasScopes": false, "Permitted": true}, {"Permission": "ViewProduct", "HasScopes": false, "Permitted": true}, {"Permission": "ViewProductList", "HasScopes": false, "Permitted": true}, {"Permission": "EditProduct", "HasScopes": false, "Permitted": true}, {"Permission": "CreateProduct", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteProduct", "HasScopes": false, "Permitted": true}, {"Permission": "ViewProductCode", "HasScopes": false, "Permitted": true}, {"Permission": "ViewRouteOfAdministration", "HasScopes": false, "Permitted": true}, {"Permission": "ViewRouteOfAdministrationList", "HasScopes": false, "Permitted": true}, {"Permission": "CreateRouteOfAdministration", "HasScopes": false, "Permitted": true}, {"Permission": "EditRouteOfAdministration", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteRouteOfAdministration", "HasScopes": false, "Permitted": true}, {"Permission": "ViewDosageForm", "HasScopes": false, "Permitted": true}, {"Permission": "ViewDosageFormList", "HasScopes": false, "Permitted": true}, {"Permission": "CreateDosageForm", "HasScopes": false, "Permitted": true}, {"Permission": "EditDosageForm", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteDosageForm", "HasScopes": false, "Permitted": true}, {"Permission": "ViewTag", "HasScopes": false, "Permitted": true}, {"Permission": "ViewTagList", "HasScopes": false, "Permitted": true}, {"Permission": "CreateTag", "HasScopes": false, "Permitted": true}, {"Permission": "EditTag", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteTag", "HasScopes": false, "Permitted": true}, {"Permission": "ViewSubstance", "HasScopes": false, "Permitted": true}, {"Permission": "ViewSubstanceList", "HasScopes": false, "Permitted": true}, {"Permission": "EditSubstance", "HasScopes": false, "Permitted": true}, {"Permission": "CreateSubstance", "HasScopes": false, "Permitted": true}, {"Permission": "DeleteSubstance", "HasScopes": false, "Permitted": true}, {"Permission": "ViewApplicationNumber", "HasScopes": false, "Permitted": true}, {"Permission": "ViewSubmissionNumber", "HasScopes": false, "Permitted": true}, {"Permission": "ViewSubmissionType", "HasScopes": false, "Permitted": true}, {"Permission": "ViewProductType", "HasScopes": false, "Permitted": true}, {"Permission": "ViewAudit", "HasScopes": false, "Permitted": true}]}]}