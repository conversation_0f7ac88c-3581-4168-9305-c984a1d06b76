﻿using Axon.HAComms.Api.Sdk.Net.Model;

namespace Axon.HAComms.Tests.Common.Builders
{
    public class ProductExtensionSdkModelBuilder
    {
        public static ProductExtensionSdkModelBuilder Default() => new();

        private int id;
        private string pcid = Fake.ProductExtension.PCID;
        private int productId;
        private int dosageFormId;
        private List<int> routeOfAdministrationIds;
        private bool isActive;

        public ProductExtensionSdkModelBuilder()
        {
            productId = Fake.Product.Id;
            dosageFormId=Fake.DosageForm.Id;
            routeOfAdministrationIds = new List<int>();
        }

        public ProductExtensionSdkModelBuilder WithId(int extensionId)
        {
            id = extensionId;
            return this;
        }

        public ProductExtensionSdkModelBuilder WithPcid(string productCode)
        {
            pcid = productCode;
            return this;
        }

        public ProductExtensionSdkModelBuilder WithDosageFormId(int dosageFormId)
        {
            this.dosageFormId = dosageFormId;
            return this;
        }

        public ProductExtensionSdkModelBuilder WithIsActive(bool extensionIsActive)
        {
            isActive = extensionIsActive;
            return this;
        }

        public ProductExtensionSdkModelBuilder WithRouteOfAdministrationIds(List<int> ids)
        {
            this.routeOfAdministrationIds = ids;
            return this;
        }

        public ProductExtensionModel Build()
        {
            var model = new ProductExtensionModel(id, productId, pcid, dosageFormId, routeOfAdministrationIds: routeOfAdministrationIds, isActive: isActive);
            return model;
        }
    }
}
