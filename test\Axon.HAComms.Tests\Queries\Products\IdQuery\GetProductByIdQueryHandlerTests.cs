﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Application.Queries.Products.IdQuery;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DosageForms;
using Axon.HAComms.Tests.Builders.Products;
using Axon.HAComms.Tests.Builders.RoutesOfAdministration;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Products.IdQuery;

public class GetProductByIdQueryHandlerTests
{
    private readonly GetProductByIdQueryHandler handler;
    private readonly IProductsRepository productsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public GetProductByIdQueryHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");

        productsRepo = Substitute.For<IProductsRepository>();
        var commentsRepo = Substitute.For<ICommentsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new ProductsMappingProfile());
            cfg.AddProfile(new DrugSubstancesMappingProfile());
            cfg.AddProfile(new ProductExtensionsMappingProfile());
            cfg.AddProfile(new DosageFormsMappingProfile());
            cfg.AddProfile(new RouteOfAdministrationsMappingProfile());
        });

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        auditService = Substitute.For<IAuditService>();

        var mapper = mockMapper.CreateMapper();
        handler = new GetProductByIdQueryHandler(productsRepo, commentsRepo, mapper,
            correlationIdProvider, clientDetailsProvider, auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsItem()
    {
        // Arrange
        var productId = Fake.Product.Id;
        var dosageForm = new DosageFormBuilder().Build();
        var routeOfAdministration = TestEntitiesGenerator<RouteOfAdministration, RouteOfAdministrationBuilder>.Generate(1).Single();
        var productExtensionEntity = ProductExtension.Create(
            Fake.ProductExtension.PCID,
            dosageForm,
            Fake.ProductExtension.IsActive,
            [routeOfAdministration]);

        var entity = new ProductsBuilder().WithId(productId).WithProductExtensions(productExtensionEntity).Build();
        var request = new GetProductByIdQueryRequest(productId);

        productsRepo.GetItemAsync(entity.Id).Returns(entity);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, default, false);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.DRUG_PRODUCT_VIEWED, AuditEventCategory.DRUG_PRODUCTS, AuditEventDescription.DRUG_PRODUCT_VIEW,
                Arg.Any<ProductModel>());
    }
}
