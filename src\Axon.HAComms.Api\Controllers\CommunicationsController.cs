﻿using Axon.Core.Shared.Authorisation;
using Axon.HAComms.Api.Services.Authorization;
using Axon.HAComms.Application.Commands.Communications.Complete;
using Axon.HAComms.Application.Commands.Communications.Create;
using Axon.HAComms.Application.Commands.Communications.Delete;
using Axon.HAComms.Application.Commands.Communications.Reinstate;
using Axon.HAComms.Application.Commands.Communications.Update;
using Axon.HAComms.Application.Models.Communications;
using Axon.HAComms.Application.Queries.Communications.IdQuery;
using Axon.HAComms.Application.Queries.Communications.PagedListQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Controllers;

[ApiController]
[Produces("application/json", "application/xml")]
[Route("{tenant}/v{version:apiVersion}/Communications")]
public class CommunicationsController(IMediator mediator) : ApiControllerBase(mediator)
{
    /// <summary>
    /// Get paged Communications
    /// </summary>
    /// <returns>All communications</returns>
    [HttpGet(Name = "GetPagedCommunicationsList")]
    //[HasPermissions(nameof(HacommsPermissions.ViewCommunication))]
    [ProducesResponseType(200, Type = typeof(ApiPagedListResult<CommunicationPagedListModel>))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetPagedAsync([FromQuery] string[]? filters, [FromQuery] int skip = 0, [FromQuery] int take = 20, [FromQuery] string? order = "")
    {
        return await Send(new GetCommunicationsPagedListQueryRequest(filters, skip, take, order));
    }

    /// <summary>
    /// Get a single Communication by id
    /// </summary>
    /// <response code="200">Single communication returned.</response>
    [HttpGet("{id}", Name = "GetCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.ViewCommunication))]
    [ProducesResponseType(200, Type = typeof(CommunicationModel))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IActionResult> GetCommunicationByIdAsync(int id) =>
        await Send(new GetCommunicationByIdQueryRequest(id));

    /// <summary>
    /// Creates a new Communication
    /// </summary>
    [HttpPost(Name = "CreateCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.CreateCommunication))]
    [ProducesResponseType(200, Type = typeof(CreateCommunicationCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<IActionResult> CreateCommunicationAsync([FromBody] CreateCommunicationCommandRequest command) =>
        await Send(command);

    /// <summary>
    /// Updates a Communication
    /// </summary>
    [HttpPut(Name = "UpdateCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.EditCommunication))]
    [ProducesResponseType(200, Type = typeof(UpdateCommunicationCommandResponse))]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> UpdateCommunicationAsync([FromBody] UpdateCommunicationCommandRequest command) =>
        await Send(command);

    /// <summary>
    /// Complete a Communication
    /// </summary>
    [HttpPut("{id}/complete", Name = "CompleteCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.EditCommunication))]
    [ProducesResponseType(200)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> CompleteCommunicationAsync(int id) =>
        await Send(new CompleteCommunicationCommandRequest(id));

    /// <summary>
    /// Reinstate a Communication
    /// </summary>
    [HttpPut("{id}/reinstate", Name = "ReinstateCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.EditCommunication))]
    [ProducesResponseType(200)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<IActionResult> ReinstateCommunicationAsync(int id) =>
        await Send(new ReinstateCommunicationCommandRequest(id));

    /// <summary>
    /// Deletes a Communication 
    /// </summary>
    [HttpDelete("{id}", Name = "DeleteCommunication")]
    //[HasPermissions(nameof(HacommsPermissions.DeleteCommunication))]
    [ProducesResponseType(204)]
    [ProducesErrorResponseType(typeof(ErrorResult))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<IActionResult> DeleteCommunicationAsync(int id) =>
        await Send(new DeleteCommunicationCommandRequest(id));
}
