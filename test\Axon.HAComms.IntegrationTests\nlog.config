﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
	</extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- IMPORTANT: Console JSON output is picked up by new relic in production environments, we log the json
			to a file in dev etc.
		-->
		<target xsi:type="File" name="new-relic-json" fileName="/app/logs/api-${shortdate}.json.log">
			<layout type="JsonLayout" includeMdlc="true">
				<attribute name="time" layout="${longdate}" />
				<attribute name="level" layout="${uppercase:${level}}" />
				<attribute name="traceId" layout="${activityid:whenEmpty=${mdlc:item=RequestId:whenEmpty=${aspnet-TraceIdentifier}}}" />
				<attribute name="threadId" layout="${threadid}" />
				<attribute name="logger" layout="${logger}" />
				<attribute name="errorCode" layout="${event-properties:item=ErrorCode}" />
				<attribute name="errorClass" layout="${exception:format=Type}" />
				<attribute name="errorMessage" layout="${exception:format=Message}" />
				<attribute name="errorStack" layout="${exception:format=StackTrace}" />
				<attribute name="message" layout="${event-properties:item=Message:whenEmpty=${message}}" />
				<attribute name="applicationName" layout="${appdomain:format={1\}}" />
				<attribute name="area" layout="${event-properties:item=Area:whenEmpty=${logger}}" />
			</layout>
		</target>

		<!-- IMPORTANT: File output is only used by devs locally so is in a more human readable format.
				NOTE: new lines in the layout is ignored (use ${newline}),
				whitespace is not ignored and is used for indentation in some layouts below
				see appsettings.json for per namespace filtering options
				
				NOTE: we log exactly the same format to the logs folder as well, 
				console windows can be hard to search!
		-->
		<target xsi:type="Console" name="human-readable-detailed-console"
		        layout="${level:padding=-6} | ${message:padding=-150}| ${logger} | ${time}
${onexception:${newline}EXCEPTION\: ${exception:format=message,stacktrace}}" />

		<target xsi:type="File" name="human-readable-detailed-file" fileName="/app/logs/api-${shortdate}.log"
				layout="${level:padding=-6} | ${message:padding=-150}| ${logger} | ${longdate}
${onexception:${newline}EXCEPTION\: ${exception:format=message,stacktrace}}" />
	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<!-- the minLevel is filters at the lowest level that will be output setting higher than `Trace` will stop some
		LogLevels being logged at all.
	  -->
		<logger name="*" minlevel="Trace" writeTo="human-readable-detailed-console" />
		<logger name="*" minlevel="Trace" writeTo="human-readable-detailed-file" />
		<!-- <logger name="*" minlevel="Trace" writeTo="human-readable-compact" /> -->
	</rules>


</nlog>