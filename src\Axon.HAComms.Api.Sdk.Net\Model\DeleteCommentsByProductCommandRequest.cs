/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// DeleteCommentsByProductCommandRequest
    /// </summary>
    [DataContract(Name = "DeleteCommentsByProductCommandRequest")]
    public partial class DeleteCommentsByProductCommandRequest : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteCommentsByProductCommandRequest" /> class.
        /// </summary>
        /// <param name="communicationId">communicationId.</param>
        /// <param name="productId">productId.</param>
        public DeleteCommentsByProductCommandRequest(int communicationId = default(int), int productId = default(int))
        {
            this.CommunicationId = communicationId;
            this.ProductId = productId;
        }

        /// <summary>
        /// Gets or Sets CommunicationId
        /// </summary>
        [DataMember(Name = "communicationId", EmitDefaultValue = false)]
        public int CommunicationId { get; set; }

        /// <summary>
        /// Gets or Sets ProductId
        /// </summary>
        [DataMember(Name = "productId", EmitDefaultValue = false)]
        public int ProductId { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class DeleteCommentsByProductCommandRequest {\n");
            sb.Append("  CommunicationId: ").Append(CommunicationId).Append("\n");
            sb.Append("  ProductId: ").Append(ProductId).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
