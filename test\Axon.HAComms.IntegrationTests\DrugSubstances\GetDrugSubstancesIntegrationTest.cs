﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DrugSubstances;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetDrugSubstancesIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DrugSubstancesApi drugSubstanceApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetPagedDrugSubstances_ValidRequest_ReturnsOk()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 56);

        //Act
        var responseObj = await drugSubstanceApi.GetPagedDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT, null, 50, 10);
			
        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedDrugSubstances_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 77);
        var substance = await dbContext.DrugSubstances.GetRandomEntity();

        //Act
        var responseObj = await drugSubstanceApi.GetPagedDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT,
            new List<string> { $"code=>{substance.Code}", $"name=>{substance.Name}" }, 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Code.Equals(substance.Code) && x.Name.Equals(substance.Name));
    }

    [Fact]
    public async Task GetPagedDrugSubstances_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 25);

        //Act
        var responseObj = await drugSubstanceApi.GetPagedDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT,
            null, 0, 10, "name=>desc");

        //Assert
        responseObj.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task GetPagedDrugSubstances_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 55);

        //Act
        var responseObj = await drugSubstanceApi.GetPagedDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15);

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    [Fact]
    public async Task GetPagedDrugSubstances_WithAssociatedComments_ReturnsOk()
    {
        //Arrange
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 3);
        var productWithComments = products.Take(1).ToList();
        var productsWithoutComments = products.Skip(1).Take(2).ToList();

        await CommunicationsTestEntitiesBuilder.Build(dbContext, productWithComments, 3);
        //Act
        var responseObj = await drugSubstanceApi.GetPagedDrugSubstancesListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Where(x => x.IsAssociatedToComment).Should().NotBeEmpty();
        responseObj.Data.Where(x => productsWithoutComments.SelectMany(p => p.DrugSubstances).Select(d => d.Id).Contains(x.Id)).Select(x => x.IsAssociatedToComment).Should().Contain(false);
        responseObj.Data.Where(x => productWithComments.SelectMany(p => p.DrugSubstances).Select(d => d.Id).Contains(x.Id)).Select(x => x.IsAssociatedToComment).Should().Contain(true);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }
}
