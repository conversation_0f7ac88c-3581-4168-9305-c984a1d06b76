using AutoMapper;
using Axon.HAComms.Application.Commands.Comments.Create;
using Axon.HAComms.Application.Commands.Comments.Update;
using Axon.HAComms.Application.Models.Comments;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings;

public class CommentsMappingProfile : Profile
{
    public CommentsMappingProfile()
    {
        CreateMap<Comment, CommentDtoModel>()
            .ForMember(dest => dest.ProductExtensions, o => o.MapFrom(s => s.CommentProductExtensions))
            .ForMember(dest => dest.ProductName, o => o.MapFrom(s => !s.IsGeneralGuidance && s.CommentProductExtensions != null ? s.CommentProductExtensions.First().ProductExtension!.Product.Name: string.Empty))
            .ForMember(dest => dest.ProductTypes, o => o.MapFrom(s => !s.IsGeneralGuidance && s.CommentProductExtensions != null ? s.CommentProductExtensions.First().ProductExtension!.Product.ProductTypes.Select(p => p.Name) : null));
        CreateMap<Comment, CreateCommentCommandResponse>();
        CreateMap<Comment, UpdateCommentCommandResponse>();
        CreateMap<Comment, Comment>()
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.Communication, o => o.Ignore())
            .ForMember(dest => dest.CommunicationId, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore());
    }
}
