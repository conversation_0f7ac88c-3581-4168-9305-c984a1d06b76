﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DosageForms;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.DrugSubstances;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.RoutesOfAdministration;
using Axon.HAComms.IntegrationTests.Multitenancy;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Communications;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetCommunicationsPagedListIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetPagedCommunications_ValidRequest_ReturnsOk()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 5);
        await CommunicationsTestEntitiesBuilder.Build(dbContext, products, 56);

        //Act
        var responseObj = await communicationApi.GetPagedCommunicationsListAsync(TenantConstants.DEFAULT_TENANT, null, 50, 10, "subject=>desc");
			
        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedCommunications_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 10);
        var communications = await CommunicationsTestEntitiesBuilder.Build(dbContext, products, 77);
        var firstCommunication = communications[0];

        //Act
        var responseObj =
            await communicationApi.GetPagedCommunicationsListAsync(TenantConstants.DEFAULT_TENANT, [$"subject=>{firstCommunication.Subject}"], 0, 10, "subject=>desc");

        //Assert
        responseObj.Data.Should().Contain(x => x.IsCompleted == firstCommunication.IsCompleted && x.Subject.Equals(firstCommunication.Subject));
    }

    [Fact]
    public async Task GetPagedCommunications_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 10);
        await CommunicationsTestEntitiesBuilder.Build(dbContext, products, 25);

        //Act
        var responseObj = await communicationApi.GetPagedCommunicationsListAsync(TenantConstants.DEFAULT_TENANT, null, 0, 10, "subject=>desc");

        //Assert
        responseObj.Data.Should().BeInDescendingOrder(x => x.Subject);
    }

    [Fact]
    public async Task GetPagedCommunications_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await DrugSubstancesTestEntitiesBuilder.Build(dbContext, 4);
        var products = await ProductsTestEntitiesBuilder.Build(dbContext, 10);
        await CommunicationsTestEntitiesBuilder.Build(dbContext, products, 55);

        //Act
        var responseObj = await communicationApi.GetPagedCommunicationsListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15, "subject=>desc");

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    public async Task InitializeAsync()
    {
        await RoutesOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.Communications.Clear();
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();
			
        await dbContext.SaveChangesAsync();
    }
}
