﻿using Axon.Core.Shared.Audit;
using MediatR;
using Microsoft.AspNetCore.OData.Query;

namespace Axon.HAComms.Application.Queries.Audit.GetAuditsQuery;

public class GetAuditsQueryRequest(string tenant, ODataQueryOptions<ApplicationAudit> oDataOptions) : IRequest<GetAuditsQueryResponse>
{
    public string Tenant { get; } = tenant;

    public ODataQueryOptions<ApplicationAudit> OdataOptions { get; } = oDataOptions;
}
