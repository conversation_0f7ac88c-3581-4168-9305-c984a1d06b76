﻿using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Communication;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.Comments.Create;

internal class CreateCommentCommandHandler(
    ICommunicationService communicationService,
    ICommentsRepository commentsRepo,
    ICommunicationsRepository communicationsRepo,
    IMapper mapper,
    ILogger<CreateCommentCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<CreateCommentCommandRequest, CreateCommentCommandResponse>
{
    public async Task<CreateCommentCommandResponse> Handle(CreateCommentCommandRequest request, CancellationToken cancellationToken)
    {
        var correlationId = correlationIdProvider.Provide();
        var comment = new Comment();
        var communication = await communicationsRepo.GetItemAsync(request.CommunicationId);

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.COMMENT_CREATED, AuditEventCategory.COMMENTS, AuditEventDescription.COMMENT_CREATE, comment,
            async () =>
            {
                await communicationService.ValidateAndCreateCommentAsync(request, comment);
                comment.CommunicationId = communication.Id;
                commentsRepo.AddItem(comment);
                await commentsRepo.SaveChangesAsync(userProvider);
                logger.LogInformation("Comment {Id} added successfully.", comment.Id);
            });

        var response = mapper.Map<CreateCommentCommandResponse>(comment);
        return response;
    }
}
