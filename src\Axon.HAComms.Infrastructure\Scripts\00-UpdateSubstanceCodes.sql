begin transaction;

Update DrugSubstances set Code = '9015' where Name = 'Leptospira interrogans,serogroup icterohaemorrhagi'
Update DrugSubstances set Code = '9012' where Name = 'cefacetrile sodium'
Update DrugSubstances set Code = '9011' where Name = 'oxytetracycline dihydrate'
Update DrugSubstances set Code = '8983' where Name = 'amoxicillin trihydrate'
Update DrugSubstances set Code = '8941' where Name = 'newcastle disease virus'
Update DrugSubstances set Code = '8847' where Name = 'phenylephrine'
Update DrugSubstances set Code = '8826' where Name = 'prednisolone'
Update DrugSubstances set Code = '8795' where Name = 'pimobendan'
Update DrugSubstances set Code = '8774' where Name = 'sulfadiazine'
Update DrugSubstances set Code = '8763' where Name = 'Haemophilus parasuis'
Update DrugSubstances set Code = '8615' where Name = 'dl-methionine'
Update DrugSubstances set Code = '8570' where Name = 'hyocine'
Update DrugSubstances set Code = '8380' where Name = 'tipranavir'
Update DrugSubstances set Code = '8377' where Name = 'inclusion body hepatitis virus'
Update DrugSubstances set Code = '8370' where Name = 'Canine coronavirus'
Update DrugSubstances set Code = '8369' where Name = 'Canine adenovirus Type 1,Cornell'
Update DrugSubstances set Code = '8368' where Name = 'streptococcus pyogenes'
Update DrugSubstances set Code = '8367' where Name = 'salmonella schottmuelleri'
Update DrugSubstances set Code = '8340' where Name = 'croscarmellose sodium'
Update DrugSubstances set Code = '8282' where Name = 'dl-tryptophan'
Update DrugSubstances set Code = '8123' where Name = 'vervain herb'
Update DrugSubstances set Code = '8122' where Name = 'common sorrel, herb'
Update DrugSubstances set Code = '8039' where Name = 'pseudoephedrine sulphate'
Update DrugSubstances set Code = '8038' where Name = 'tristearin'
Update DrugSubstances set Code = '8028' where Name = 'Feline herpesvirus (Feline rhinotracheitis virus)'
Update DrugSubstances set Code = '8027' where Name = 'Leptospira bratislava,JEZ'
Update DrugSubstances set Code = '8022' where Name = 'erav'
Update DrugSubstances set Code = '8021' where Name = 'Lawsonia intracellularis'
Update DrugSubstances set Code = '8017' where Name = 'Leptospira hardjo'
Update DrugSubstances set Code = '8016' where Name = 'Leptospira kirschneri,serogroup grippotyphosa'
Update DrugSubstances set Code = '8000' where Name = 'glucagon'
Update DrugSubstances set Code = '7997' where Name = 'Escherichia coli'
Update DrugSubstances set Code = '7994' where Name = 'Salmonella choleraesuis'
Update DrugSubstances set Code = '7993' where Name = 'rabies virus'
Update DrugSubstances set Code = '7989' where Name = 'infectious bronchitis virus'
Update DrugSubstances set Code = '7982' where Name = 'clofenvinfos'
Update DrugSubstances set Code = '7975' where Name = 'povidone iodine'
Update DrugSubstances set Code = '7961' where Name = 'canine parainfluenza virus'
Update DrugSubstances set Code = '7938' where Name = 'avian encephalomyelitis virus, calnek'
Update DrugSubstances set Code = '7921' where Name = 'bromhexine'
Update DrugSubstances set Code = '7918' where Name = 'clhaemolyticum'
Update DrugSubstances set Code = '7909' where Name = 'porcine parvovirus'
Update DrugSubstances set Code = '7904' where Name = 'Pasteurella multocida'
Update DrugSubstances set Code = '7900' where Name = 'Erysipelothrix rhusiopathiae'
Update DrugSubstances set Code = '7879' where Name = 'propofol'
Update DrugSubstances set Code = '7877' where Name = 'tiamulin fumarate'
Update DrugSubstances set Code = '7876' where Name = 'bunazosin hydrochloride'
Update DrugSubstances set Code = '7875' where Name = 'lysine'
Update DrugSubstances set Code = '7874' where Name = 'lysine hydrochloride'
Update DrugSubstances set Code = '7871' where Name = 'miconazole'
Update DrugSubstances set Code = '7870' where Name = 'tylosin'
Update DrugSubstances set Code = '7864' where Name = 'arginine'
Update DrugSubstances set Code = '7862' where Name = 'phenylbutazone calcium dihydrate'
Update DrugSubstances set Code = '7859' where Name = 'menadione'
Update DrugSubstances set Code = '7857' where Name = 'phenoxymethylpenicillin'
Update DrugSubstances set Code = '7856' where Name = 'benethamine penicillin'
Update DrugSubstances set Code = '7854' where Name = 'benzathine benzylpenicillin'
Update DrugSubstances set Code = '7853' where Name = 'benzylpenicillin potassium'
Update DrugSubstances set Code = '7852' where Name = 'estradiol benzoate'
Update DrugSubstances set Code = '7851' where Name = 'estradiol enanthate'
Update DrugSubstances set Code = '7847' where Name = 'dexamethasone_hp'
Update DrugSubstances set Code = '7842' where Name = 'clonidine'
Update DrugSubstances set Code = '7838' where Name = 'ampicillin sodium'
Update DrugSubstances set Code = '7837' where Name = 'sulfathiazole'
Update DrugSubstances set Code = '7834' where Name = 'framycetin sulphate'
Update DrugSubstances set Code = '7818' where Name = 'chlortetracycline'
Update DrugSubstances set Code = '7801' where Name = 'hydrocortisone butyrate'
Update DrugSubstances set Code = '7794' where Name = 'thiamine hydrochloride'
Update DrugSubstances set Code = '7777' where Name = 'ketamine hydrochloride'
Update DrugSubstances set Code = '7776' where Name = 'tenecteplase'
Update DrugSubstances set Code = '7758' where Name = 'miripirium chloride'
Update DrugSubstances set Code = '7748' where Name = 'clarithromycin'
Update DrugSubstances set Code = '7687' where Name = 'methylprednisolone'
Update DrugSubstances set Code = '7550' where Name = 'flibanserin'
Update DrugSubstances set Code = '7505' where Name = 'taurolidine'
Update DrugSubstances set Code = '7504' where Name = 'tamsulosin hydrochloride'
Update DrugSubstances set Code = '7490' where Name = 'sucralfate'
Update DrugSubstances set Code = '7477' where Name = 'ramifenazone'
Update DrugSubstances set Code = '7470' where Name = 'omeprazole_hp'
Update DrugSubstances set Code = '7469' where Name = 'nimesulide'
Update DrugSubstances set Code = '7465' where Name = 'nystatin'
Update DrugSubstances set Code = '7463' where Name = 'tasonermin'
Update DrugSubstances set Code = '7458' where Name = 'oxytocin'
Update DrugSubstances set Code = '7456' where Name = 'paromomycin sulphate'
Update DrugSubstances set Code = '7435' where Name = 'primidone'
Update DrugSubstances set Code = '7434' where Name = 'progesterone'
Update DrugSubstances set Code = '7427' where Name = 'naphazoline hydrochloride'
Update DrugSubstances set Code = '7412' where Name = 'miconazole nitrate'
Update DrugSubstances set Code = '7408' where Name = 'metformin hydrochloride'
Update DrugSubstances set Code = '7398' where Name = 'mebendazole'
Update DrugSubstances set Code = '7392' where Name = 'levamisole hydrochloride'
Update DrugSubstances set Code = '7387' where Name = 'josamycin'
Update DrugSubstances set Code = '7384' where Name = 'interferon gamma-1b'
Update DrugSubstances set Code = '7376' where Name = 'tiotropium bromide monohydrate'
Update DrugSubstances set Code = '7354' where Name = 'fenoxazoline hydrochloride'
Update DrugSubstances set Code = '7328' where Name = 'griseofulvin'
Update DrugSubstances set Code = '7320' where Name = 'diazinon'
Update DrugSubstances set Code = '7316' where Name = 'flubendazole'
Update DrugSubstances set Code = '7299' where Name = 'furosemide'
Update DrugSubstances set Code = '7291' where Name = 'cresol'
Update DrugSubstances set Code = '7271' where Name = 'vine leaf extract'
Update DrugSubstances set Code = '7249' where Name = 'guaifenesin'
Update DrugSubstances set Code = '7217' where Name = 'menbutone'
Update DrugSubstances set Code = '7191' where Name = 'sodium thiosulphate'
Update DrugSubstances set Code = '7150' where Name = 'thimerosal'
Update DrugSubstances set Code = '7148' where Name = 'romifidine hydrochloride'
Update DrugSubstances set Code = '7143' where Name = 'simvastatin'
Update DrugSubstances set Code = '7122' where Name = 'nonivamide'
Update DrugSubstances set Code = '7049' where Name = 'coconut oil'
Update DrugSubstances set Code = '7039' where Name = 'dihydrostreptomycin'
Update DrugSubstances set Code = '7031' where Name = 'diethylcarbamazine citrate'
Update DrugSubstances set Code = '7009' where Name = 'xylazine'
Update DrugSubstances set Code = '7001' where Name = 'deslorelin'
Update DrugSubstances set Code = '6994' where Name = 'diclazuril'
Update DrugSubstances set Code = '6992' where Name = 'dextran'
Update DrugSubstances set Code = '6990' where Name = 'cythioate'
Update DrugSubstances set Code = '6947' where Name = 'cimaterol'
Update DrugSubstances set Code = '6946' where Name = 'chlorobutanol'
Update DrugSubstances set Code = '6943' where Name = 'cefadroxil'
Update DrugSubstances set Code = '6941' where Name = 'cimetropium bromide'
Update DrugSubstances set Code = '6926' where Name = 'budesonide'
Update DrugSubstances set Code = '6924' where Name = 'carnidazole'
Update DrugSubstances set Code = '6912' where Name = 'bacitracin zinc'
Update DrugSubstances set Code = '6907' where Name = 'dichlorvos'
Update DrugSubstances set Code = '6892' where Name = 'cabergoline'
Update DrugSubstances set Code = '6875' where Name = 'telmisartan'
Update DrugSubstances set Code = '6863' where Name = 'cloxacillin benzathine'
Update DrugSubstances set Code = '6862' where Name = 'cloxacillin sodium'
Update DrugSubstances set Code = '6851' where Name = 'talipexole dihydrochloride'
Update DrugSubstances set Code = '6833' where Name = 'pramipexole dihydrochloride monohydrate'
Update DrugSubstances set Code = '6812' where Name = 'insulin human'
Update DrugSubstances set Code = '6809' where Name = 'zinc sulphate monohydrate'
Update DrugSubstances set Code = '6792' where Name = 'biotin'
Update DrugSubstances set Code = '6777' where Name = 'eleutherococcus extract'
Update DrugSubstances set Code = '6774' where Name = 'doxycycline hyclate'
Update DrugSubstances set Code = '6773' where Name = 'docusate sodium'
Update DrugSubstances set Code = '6755' where Name = 'rosemary'
Update DrugSubstances set Code = '6699' where Name = 'alum potassium anhydrous'
Update DrugSubstances set Code = '6685' where Name = 'nicoboxil'
Update DrugSubstances set Code = '6677' where Name = 'tiamulin'
Update DrugSubstances set Code = '6676' where Name = 'vetrabutine hydrochloride'
Update DrugSubstances set Code = '6672' where Name = 'choline chloride'
Update DrugSubstances set Code = '6660' where Name = 'lacidipine'
Update DrugSubstances set Code = '6659' where Name = 'metamizole magnesium'
Update DrugSubstances set Code = '6658' where Name = 'chlorocresol'
Update DrugSubstances set Code = '6654' where Name = 'nevirapine'
Update DrugSubstances set Code = '6648' where Name = 'sorbitan oleate'
Update DrugSubstances set Code = '6645' where Name = 'pirenzepine dihydrochloride monohydrate'
Update DrugSubstances set Code = '6642' where Name = 'benzethonium chloride'
Update DrugSubstances set Code = '6639' where Name = 'lansoprazole'
Update DrugSubstances set Code = '6637' where Name = 'isosorbide mononitrate'
Update DrugSubstances set Code = '6636' where Name = 'nitrofurantoin'
Update DrugSubstances set Code = '6633' where Name = 'enilconazole'
Update DrugSubstances set Code = '6632' where Name = 'epinastine hydrochloride'
Update DrugSubstances set Code = '6608' where Name = 'menthol'
Update DrugSubstances set Code = '6606' where Name = 'sorbitan trioleate'
Update DrugSubstances set Code = '6601' where Name = 'amphotericin b'
Update DrugSubstances set Code = '6594' where Name = 'amantadine hydrochloride'
Update DrugSubstances set Code = '6589' where Name = 'alteplase'
Update DrugSubstances set Code = '6588' where Name = 'alfaprostol'
Update DrugSubstances set Code = '6579' where Name = 'triamcinolone acetonide'
Update DrugSubstances set Code = '6573' where Name = 'prednisolone acetate'
Update DrugSubstances set Code = '6565' where Name = 'nicotinic acid'
Update DrugSubstances set Code = '6561' where Name = 'medroxyprogesterone acetate'
Update DrugSubstances set Code = '6550' where Name = 'heparin sodium'
Update DrugSubstances set Code = '6548' where Name = 'sodium fusidate'
Update DrugSubstances set Code = '6521' where Name = 'ademetionine'
Update DrugSubstances set Code = '6518' where Name = 'aciclovir'
Update DrugSubstances set Code = '6513' where Name = 'acepromazine maleate'
Update DrugSubstances set Code = '6503' where Name = 'carboxymethylcellulose sodium'
Update DrugSubstances set Code = '6501' where Name = 'diclofenac sodium'
Update DrugSubstances set Code = '6480' where Name = 'ergocalciferol'
Update DrugSubstances set Code = '6465' where Name = 'triacetin'
Update DrugSubstances set Code = '6452' where Name = 'azaperone'
Update DrugSubstances set Code = '6447' where Name = 'algestone acetophenide'
Update DrugSubstances set Code = '6446' where Name = 'threonine'
Update DrugSubstances set Code = '6445' where Name = 'pyridoxine hydrochloride'
Update DrugSubstances set Code = '6444' where Name = 'l-methionine'
Update DrugSubstances set Code = '6443' where Name = 'arginine hydrochloride'
Update DrugSubstances set Code = '6441' where Name = 'l-histidine monohydrochloride'
Update DrugSubstances set Code = '6436' where Name = 'sulfamethoxazole'
Update DrugSubstances set Code = '6419' where Name = 'prothipendyl'
Update DrugSubstances set Code = '6415' where Name = 'polysorbate 80'
Update DrugSubstances set Code = '6412' where Name = 'hypromellose'
Update DrugSubstances set Code = '6411' where Name = 'hydroxypropylcellulose'
Update DrugSubstances set Code = '6408' where Name = 'ibuprofen'
Update DrugSubstances set Code = '6405' where Name = 'piroxicam'
Update DrugSubstances set Code = '6404' where Name = 'cefalexin monohydrate'
Update DrugSubstances set Code = '6402' where Name = 'trimethoprim'
Update DrugSubstances set Code = '6401' where Name = 'dexpanthenol'
Update DrugSubstances set Code = '6395' where Name = 'sulfamethoxypyridazine'
Update DrugSubstances set Code = '6393' where Name = 'phenylbutazone_hp'
Update DrugSubstances set Code = '6390' where Name = 'tylosin tartrate'
Update DrugSubstances set Code = '6389' where Name = 'oxymetazoline hydrochloride'
Update DrugSubstances set Code = '6388' where Name = 'diltiazem hydrochloride'
Update DrugSubstances set Code = '6386' where Name = 'glutamic acid'
Update DrugSubstances set Code = '6381' where Name = 'cysteine hydrochloride anhydrous'
Update DrugSubstances set Code = '6366' where Name = 'prolintane hydrochloride'
Update DrugSubstances set Code = '6365' where Name = 'kanamycin monosulphate'
Update DrugSubstances set Code = '6359' where Name = 'nicotinamide'
Update DrugSubstances set Code = '6357' where Name = 'vitamin a'
Update DrugSubstances set Code = '6352' where Name = 'cryofluorane'
Update DrugSubstances set Code = '6346' where Name = 'thiamine'
Update DrugSubstances set Code = '6342' where Name = 'chloramphenicol'
Update DrugSubstances set Code = '6334' where Name = 'cellacefate'
Update DrugSubstances set Code = '6329' where Name = 'metamizole sodium  monohydrate'
Update DrugSubstances set Code = '6327' where Name = 'chlortetracycline hydrochloride'
Update DrugSubstances set Code = '6323' where Name = 'carprofen'
Update DrugSubstances set Code = '6320' where Name = 'furazolidone_hp'
Update DrugSubstances set Code = '6317' where Name = 'tramazoline hydrochloride monohydrate'
Update DrugSubstances set Code = '6301' where Name = 'calcium pantothenate'
Update DrugSubstances set Code = '6290' where Name = 'bendroflumethiazide'
Update DrugSubstances set Code = '6281' where Name = 'clonidine hydrochloride'
Update DrugSubstances set Code = '6262' where Name = 'benzyl alcohol'
Update DrugSubstances set Code = '6258' where Name = 'dembrexine hydrochloride'
Update DrugSubstances set Code = '6250' where Name = 'oxytetracycline hydrochloride'
Update DrugSubstances set Code = '6216' where Name = 'gliquidone'
Update DrugSubstances set Code = '6205' where Name = 'benzoxonium chloride'
Update DrugSubstances set Code = '6198' where Name = 'tartrazine'
Update DrugSubstances set Code = '6196' where Name = 'ferric oxide yellow'
Update DrugSubstances set Code = '6194' where Name = 'riboflavin'
Update DrugSubstances set Code = '6191' where Name = 'folic acid'
Update DrugSubstances set Code = '6190' where Name = 'cyanocobalamin'
Update DrugSubstances set Code = '6188' where Name = 'meglumine'
Update DrugSubstances set Code = '6187' where Name = 'poloxamer'
Update DrugSubstances set Code = '6186' where Name = 'repaglinide'
Update DrugSubstances set Code = '6184' where Name = 'oxazepam'
Update DrugSubstances set Code = '6182' where Name = 'sodium citrate anhydrous'
Update DrugSubstances set Code = '6181' where Name = 'aspartame'
Update DrugSubstances set Code = '6178' where Name = 'glycerol'
Update DrugSubstances set Code = '6168' where Name = 'methylcellulose'
Update DrugSubstances set Code = '6166' where Name = 'doxylamine succinate'
Update DrugSubstances set Code = '6158' where Name = 'sertraline hydrochloride'
Update DrugSubstances set Code = '6137' where Name = 'ethylcellulose'
Update DrugSubstances set Code = '6116' where Name = 'sodium acetate, anhydrous'
Update DrugSubstances set Code = '6113' where Name = 'glucose anhydrous'
Update DrugSubstances set Code = '6109' where Name = 'calcium chloride dihydrate'
Update DrugSubstances set Code = '6102' where Name = 'orange flavour'
Update DrugSubstances set Code = '6099' where Name = 'sodium picosulfate monohydrate'
Update DrugSubstances set Code = '6098' where Name = 'dipyridamole'
Update DrugSubstances set Code = '6095' where Name = 'chlorphenamine maleate'
Update DrugSubstances set Code = '6093' where Name = 'isoprenaline sulphate dihydrate'
Update DrugSubstances set Code = '6091' where Name = 'ipratropium bromide monohydrate'
Update DrugSubstances set Code = '6090' where Name = 'macrogol'
Update DrugSubstances set Code = '6088' where Name = 'betacarotene'
Update DrugSubstances set Code = '6086' where Name = 'sodium cyclamate'
Update DrugSubstances set Code = '6081' where Name = 'ampicillin'
Update DrugSubstances set Code = '6079' where Name = 'saccharin sodium, dihydrate'
Update DrugSubstances set Code = '6075' where Name = 'citric acid anhydrous'
Update DrugSubstances set Code = '6073' where Name = 'ascorbic acid'
Update DrugSubstances set Code = '6065' where Name = 'edetic acid'
Update DrugSubstances set Code = '6063' where Name = 'povidone'
Update DrugSubstances set Code = '6060' where Name = 'dimeticone'
Update DrugSubstances set Code = '6056' where Name = 'bisacodyl'
Update DrugSubstances set Code = '6055' where Name = 'bromhexine hydrochloride'
Update DrugSubstances set Code = '6052' where Name = 'salbutamol sulfate'
Update DrugSubstances set Code = '6050' where Name = 'sulfadiazine sodium'
Update DrugSubstances set Code = '6045' where Name = 'orciprenaline sulphate'
Update DrugSubstances set Code = '6043' where Name = 'oxyclozanide'
Update DrugSubstances set Code = '6038' where Name = 'meloxicam'
Update DrugSubstances set Code = '6035' where Name = 'brotizolam'
Update DrugSubstances set Code = '6033' where Name = 'clenbuterol hydrochloride'
Update DrugSubstances set Code = '6032' where Name = 'mexiletine hydrochloride'
Update DrugSubstances set Code = '6031' where Name = 'paracetamol'
Update DrugSubstances set Code = '6028' where Name = 'oxitropium bromide'
Update DrugSubstances set Code = '6027' where Name = 'phenylephrine hydrochloride'
Update DrugSubstances set Code = '6025' where Name = 'hydrochlorothiazide_hp'
Update DrugSubstances set Code = '6024' where Name = 'enalapril maleate'
Update DrugSubstances set Code = '6023' where Name = 'fenoterol hydrobromide'
Update DrugSubstances set Code = '6021' where Name = 'trazodone hydrochloride'
Update DrugSubstances set Code = '6016' where Name = 'etilefrine hydrochloride'
Update DrugSubstances set Code = '6014' where Name = 'morphine sulfate'
Update DrugSubstances set Code = '6009' where Name = 'sucrose'
Update DrugSubstances set Code = '6006' where Name = 'ambroxol hydrochloride'
Update DrugSubstances set Code = '6002' where Name = 'lactose monohydrate'
Update DrugSubstances set Code = '5526214' where Name = 'placebo dabigatran etexilate'
Update DrugSubstances set Code = '5526213' where Name = 'placebo nintedanib'
Update DrugSubstances set Code = '5526212' where Name = 'placebo afatinib dimaleate'
Update DrugSubstances set Code = '5520341' where Name = 'trilaciclib'
Update DrugSubstances set Code = '5520338' where Name = 'Insulin glargine'
Update DrugSubstances set Code = '5477016' where Name = 'erlotinib'
Update DrugSubstances set Code = '5472256' where Name = 'alpelisib'
Update DrugSubstances set Code = '5462365' where Name = 'butylscopolamine'
Update DrugSubstances set Code = '5459231' where Name = 'classical swine fever, c-strain_hp'
Update DrugSubstances set Code = '5456455' where Name = 'survodutide'
Update DrugSubstances set Code = '5452570' where Name = 'poziotinib'
Update DrugSubstances set Code = '5443557' where Name = 'kanamycin'
Update DrugSubstances set Code = '5443555' where Name = 'irinotecan'
Update DrugSubstances set Code = '5443551' where Name = 'duloxetine'
Update DrugSubstances set Code = '5443550' where Name = 'docusate'
Update DrugSubstances set Code = '5443549' where Name = 'deleobuvir'
Update DrugSubstances set Code = '5439347' where Name = 'bevacizumab'
Update DrugSubstances set Code = '5423739' where Name = 'morphine'
Update DrugSubstances set Code = '5423738' where Name = 'epinastine'
Update DrugSubstances set Code = '5423737' where Name = 'diltiazem'
Update DrugSubstances set Code = '5423736' where Name = 'pirenzepine'
Update DrugSubstances set Code = '5423735' where Name = 'algestone'
Update DrugSubstances set Code = '5423734' where Name = 'prolintane'
Update DrugSubstances set Code = '5423733' where Name = 'talipexole'
Update DrugSubstances set Code = '5422482' where Name = 'nannizzia'
Update DrugSubstances set Code = '5407648' where Name = 'tamsulosin'
Update DrugSubstances set Code = '5407636' where Name = 'tiotropium bromide'
Update DrugSubstances set Code = '5407635' where Name = 'afatinib'
Update DrugSubstances set Code = '5407628' where Name = 'pramipexole dihydrochloride'
Update DrugSubstances set Code = '5407627' where Name = 'pramipexole'
Update DrugSubstances set Code = '5407626' where Name = 'metamizole'
Update DrugSubstances set Code = '5407622' where Name = 'ipratropium'
Update DrugSubstances set Code = '5407621' where Name = 'clenbuterol'
Update DrugSubstances set Code = '5407620' where Name = 'fenoterol'
Update DrugSubstances set Code = '5407617' where Name = 'ambroxol'
Update DrugSubstances set Code = '5403595' where Name = 'zongertinib'
Update DrugSubstances set Code = '5402503' where Name = 'olmutinib'
Update DrugSubstances set Code = '5379483' where Name = 'calcium phosphinate'
Update DrugSubstances set Code = '5368871' where Name = 'trametinib'
Update DrugSubstances set Code = '5311109' where Name = 'velagliflozin'
Update DrugSubstances set Code = '5268991' where Name = 'stabilizer a'
Update DrugSubstances set Code = '5240881' where Name = 'Chromium'
Update DrugSubstances set Code = '5240215' where Name = 'CALCIUM IODATE'
Update DrugSubstances set Code = '5211524' where Name = 'green lip mussel extract'
Update DrugSubstances set Code = '5211519' where Name = 'Icaridin'
Update DrugSubstances set Code = '5210068' where Name = 'Enterococcous Faecium'
Update DrugSubstances set Code = '5186305' where Name = 'oxaliplatin'
Update DrugSubstances set Code = '5182969' where Name = 'Dulbecco''s Phosphate Buffered Saline (DPBS)'
Update DrugSubstances set Code = '5182965' where Name = 'montanide isa 207 VG'
Update DrugSubstances set Code = '5166846' where Name = 'streptomycin sulfate solution'
Update DrugSubstances set Code = '5156212' where Name = 'pembrolizumab'
Update DrugSubstances set Code = '4997244' where Name = 'Cloprostenol'
Update DrugSubstances set Code = '4995408' where Name = 'tylosin phosphate'
Update DrugSubstances set Code = '4861539' where Name = 'brigimadlin'
Update DrugSubstances set Code = '4820291' where Name = 'ezabenlimab'
Update DrugSubstances set Code = '4658787' where Name = 'Effer-Soda 12'
Update DrugSubstances set Code = '4637877' where Name = 'montanide isa 708'
Update DrugSubstances set Code = '4507000' where Name = 'lactose'
Update DrugSubstances set Code = '4444617' where Name = 'stearoyl macrogolglycerides'
Update DrugSubstances set Code = '4300593' where Name = 'DMEM'
Update DrugSubstances set Code = '4279007' where Name = 'spesolimab'
Update DrugSubstances set Code = '4179777' where Name = 'red vine leaf extract'
Update DrugSubstances set Code = '30293166' where Name = 'sotorasib'
Update DrugSubstances set Code = '30292991' where Name = 'ketamine'
Update DrugSubstances set Code = '30292376' where Name = 'trazodone'
Update DrugSubstances set Code = '30292375' where Name = 'tramazoline'
Update DrugSubstances set Code = '30292374' where Name = 'sertraline'
Update DrugSubstances set Code = '30292373' where Name = 'isoprenaline'
Update DrugSubstances set Code = '30292372' where Name = 'faldaprevir'
Update DrugSubstances set Code = '30292371' where Name = 'doxylamine'
Update DrugSubstances set Code = '30292370' where Name = 'diclofenac'
Update DrugSubstances set Code = '30292368' where Name = 'cefalexin'
Update DrugSubstances set Code = '30292366' where Name = 'bunazosin'
Update DrugSubstances set Code = '30292365' where Name = 'amoxicillin'
Update DrugSubstances set Code = '30292363' where Name = 'diphenhydramine'
Update DrugSubstances set Code = '30292361' where Name = 'dembrexine'
Update DrugSubstances set Code = '30291703' where Name = 'methocarbamol'
Update DrugSubstances set Code = '30291700' where Name = 'fradafiban'
Update DrugSubstances set Code = '30291148' where Name = 'tesofensine'
Update DrugSubstances set Code = '30291147' where Name = 'terbutaline'
Update DrugSubstances set Code = '30291146' where Name = 'temoporfin'
Update DrugSubstances set Code = '30291145' where Name = 'pivmecillinam'
Update DrugSubstances set Code = '30291143' where Name = 'methadone'
Update DrugSubstances set Code = '30291142' where Name = 'meclocycline'
Update DrugSubstances set Code = '30291141' where Name = 'thiethylperazine'
Update DrugSubstances set Code = '30291140' where Name = 'tiapride'
Update DrugSubstances set Code = '30291139' where Name = 'tioxolone'
Update DrugSubstances set Code = '30291138' where Name = 'tolciclate'
Update DrugSubstances set Code = '30291137' where Name = 'mabuterol'
Update DrugSubstances set Code = '30291136' where Name = 'tretinoin'
Update DrugSubstances set Code = '30291135' where Name = 'vinorelbine'
Update DrugSubstances set Code = '30291134' where Name = 'zafirlukast'
Update DrugSubstances set Code = '30291132' where Name = 'loxoprofen'
Update DrugSubstances set Code = '30291125' where Name = 'lodoxamide'
Update DrugSubstances set Code = '30291124' where Name = 'levodropropizine'
Update DrugSubstances set Code = '30291123' where Name = 'ifosfamide'
Update DrugSubstances set Code = '30291122' where Name = 'idoxuridine'
Update DrugSubstances set Code = '30291121' where Name = 'hydrargaphen'
Update DrugSubstances set Code = '30291120' where Name = 'fusidic acid'
Update DrugSubstances set Code = '30291119' where Name = 'fominoben'
Update DrugSubstances set Code = '30291118' where Name = 'flutoprazepam'
Update DrugSubstances set Code = '30291117' where Name = 'famotidine'
Update DrugSubstances set Code = '30291116' where Name = 'etofenamate'
Update DrugSubstances set Code = '30291100' where Name = 'estriol'
Update DrugSubstances set Code = '30291099' where Name = 'estradiol valerate'
Update DrugSubstances set Code = '30291098' where Name = 'erythromycin'
Update DrugSubstances set Code = '30291093' where Name = 'epalrestat'
Update DrugSubstances set Code = '30291091' where Name = 'dydrogesterone'
Update DrugSubstances set Code = '30291089' where Name = 'doxepin'
Update DrugSubstances set Code = '30291087' where Name = 'dihydroergotamine'
Update DrugSubstances set Code = '30291086' where Name = 'diflorasone'
Update DrugSubstances set Code = '30291085' where Name = 'dibekacin'
Update DrugSubstances set Code = '30291083' where Name = 'dextromethorphan'
Update DrugSubstances set Code = '30291082' where Name = 'desogestrel'
Update DrugSubstances set Code = '30291081' where Name = 'talsaclidine'
Update DrugSubstances set Code = '30291054' where Name = 'roxatidine'
Update DrugSubstances set Code = '30291053' where Name = 'ramipril'
Update DrugSubstances set Code = '30291050' where Name = 'procaterol'
Update DrugSubstances set Code = '30291049' where Name = 'porfiromycin'
Update DrugSubstances set Code = '30291048' where Name = 'pentoxyverine'
Update DrugSubstances set Code = '30291047' where Name = 'orphenadrine'
Update DrugSubstances set Code = '30291046' where Name = 'natamycin'
Update DrugSubstances set Code = '30291045' where Name = 'zatebradine'
Update DrugSubstances set Code = '30291038' where Name = 'semorphone'
Update DrugSubstances set Code = '30291035' where Name = 'fenipentol'
Update DrugSubstances set Code = '30291034' where Name = 'draquinolol'
Update DrugSubstances set Code = '30291032' where Name = 'itasetron'
Update DrugSubstances set Code = '30291031' where Name = 'feprazone'
Update DrugSubstances set Code = '30291028' where Name = 'doramapimod'
Update DrugSubstances set Code = '30291027' where Name = 'sarakalim'
Update DrugSubstances set Code = '30291026' where Name = 'irampanel'
Update DrugSubstances set Code = '30291025' where Name = 'salmeterol'
Update DrugSubstances set Code = '30291021' where Name = 'falnidamol'
Update DrugSubstances set Code = '30291010' where Name = 'lefradafiban'
Update DrugSubstances set Code = '30291009' where Name = 'tanogitran'
Update DrugSubstances set Code = '30290991' where Name = 'vipoglanstat'
Update DrugSubstances set Code = '30290990' where Name = 'nebracetam'
Update DrugSubstances set Code = '30290989' where Name = 'piroxicillin'
Update DrugSubstances set Code = '30290988' where Name = 'pinokalant'
Update DrugSubstances set Code = '30290987' where Name = 'ontazolast'
Update DrugSubstances set Code = '30290984' where Name = 'palinavir'
Update DrugSubstances set Code = '30290983' where Name = 'sabiporide'
Update DrugSubstances set Code = '30290982' where Name = 'olcegepant'
Update DrugSubstances set Code = '30290978' where Name = 'pioglitazone'
Update DrugSubstances set Code = '30290975' where Name = 'otenzepad'
Update DrugSubstances set Code = '30290974' where Name = 'dirithromycin'
Update DrugSubstances set Code = '30290973' where Name = 'sulmazole'
Update DrugSubstances set Code = '30290972' where Name = 'falipamil'
Update DrugSubstances set Code = '30290971' where Name = 'mebeverine'
Update DrugSubstances set Code = '30290970' where Name = 'hydroxyzine'
Update DrugSubstances set Code = '30290959' where Name = 'levacetylmethadol'
Update DrugSubstances set Code = '30290952' where Name = 'enlimomab pegol'
Update DrugSubstances set Code = '30290950' where Name = 'bivatuzumab'
Update DrugSubstances set Code = '30290948' where Name = 'enlimomab'
Update DrugSubstances set Code = '30290947' where Name = 'sibrotuzumab'
Update DrugSubstances set Code = '30290922' where Name = 'mopidamol'
Update DrugSubstances set Code = '30290920' where Name = 'nardeterol'
Update DrugSubstances set Code = '30290919' where Name = 'mifentidine'
Update DrugSubstances set Code = '30290918' where Name = 'mexenone'
Update DrugSubstances set Code = '30290917' where Name = 'cyclandelate'
Update DrugSubstances set Code = '30290916' where Name = 'cromoglicic acid'
Update DrugSubstances set Code = '30290915' where Name = 'crobenetine'
Update DrugSubstances set Code = '30290912' where Name = 'clotrimazole'
Update DrugSubstances set Code = '30290911' where Name = 'clofibrate'
Update DrugSubstances set Code = '30290908' where Name = 'clodronic acid'
Update DrugSubstances set Code = '30290907' where Name = 'clobutinol'
Update DrugSubstances set Code = '30290906' where Name = 'clebopride'
Update DrugSubstances set Code = '30290880' where Name = 'cimetidine'
Update DrugSubstances set Code = '30290879' where Name = 'ciluprevir'
Update DrugSubstances set Code = '30290878' where Name = 'cilobradine'
Update DrugSubstances set Code = '30290877' where Name = 'cilnidipine'
Update DrugSubstances set Code = '30290876' where Name = 'chlortalidone'
Update DrugSubstances set Code = '30290875' where Name = 'ceftizoxime'
Update DrugSubstances set Code = '30290874' where Name = 'cefixime'
Update DrugSubstances set Code = '30290873' where Name = 'bunitrolol'
Update DrugSubstances set Code = '30290872' where Name = 'bumetanide'
Update DrugSubstances set Code = '30290870' where Name = 'brodimoprim'
Update DrugSubstances set Code = '30290865' where Name = 'bisoxatin'
Update DrugSubstances set Code = '30290861' where Name = 'betahistine'
Update DrugSubstances set Code = '30290858' where Name = 'bepafant'
Update DrugSubstances set Code = '30290857' where Name = 'benzydamine'
Update DrugSubstances set Code = '30290856' where Name = 'beclobrate'
Update DrugSubstances set Code = '30290855' where Name = 'bamethan'
Update DrugSubstances set Code = '30290854' where Name = 'azepexole'
Update DrugSubstances set Code = '30290853' where Name = 'argatroban'
Update DrugSubstances set Code = '30290850' where Name = 'aptiganel'
Update DrugSubstances set Code = '30290846' where Name = 'apaxifylline'
Update DrugSubstances set Code = '30290826' where Name = 'terbogrel'
Update DrugSubstances set Code = '30290825' where Name = 'naproxen'
Update DrugSubstances set Code = '30290824' where Name = 'flurbiprofen'
Update DrugSubstances set Code = '30290822' where Name = 'dalteparin'
Update DrugSubstances set Code = '30290820' where Name = 'samixogrel'
Update DrugSubstances set Code = '30290819' where Name = 'pomisartan'
Update DrugSubstances set Code = '30290818' where Name = 'doqualast'
Update DrugSubstances set Code = '30290817' where Name = 'alacepril'
Update DrugSubstances set Code = '30290813' where Name = 'interferon-alfa-2c'
Update DrugSubstances set Code = '30290812' where Name = 'alicaforsen'
Update DrugSubstances set Code = '30290811' where Name = 'tremacamra'
Update DrugSubstances set Code = '30290784' where Name = 'figopitant'
Update DrugSubstances set Code = '30290776' where Name = 'apalcillin'
Update DrugSubstances set Code = '30290775' where Name = 'apafant'
Update DrugSubstances set Code = '30290774' where Name = 'aminophylline'
Update DrugSubstances set Code = '30290773' where Name = 'amelubant'
Update DrugSubstances set Code = '30290772' where Name = 'alovudine'
Update DrugSubstances set Code = '30290771' where Name = 'alinidine'
Update DrugSubstances set Code = '30290769' where Name = 'alfatradiol'
Update DrugSubstances set Code = '30290768' where Name = 'alfacalcidol'
Update DrugSubstances set Code = '30290767' where Name = 'adimolol'
Update DrugSubstances set Code = '30290766' where Name = 'acitemate'
Update DrugSubstances set Code = '30290765' where Name = 'acemetacin'
Update DrugSubstances set Code = '30289951' where Name = 'danitracen'
Update DrugSubstances set Code = '30284699' where Name = 'pergolide'
Update DrugSubstances set Code = '30284558' where Name = 'flunisolide'
Update DrugSubstances set Code = '30284557' where Name = 'etilefrine'
Update DrugSubstances set Code = '30284555' where Name = 'mexiletine'
Update DrugSubstances set Code = '30284552' where Name = 'oxymetazoline'
Update DrugSubstances set Code = '30284551' where Name = 'isosorbide'
Update DrugSubstances set Code = '30284549' where Name = 'fenoxazoline'
Update DrugSubstances set Code = '30284548' where Name = 'naphazoline'
Update DrugSubstances set Code = '30282557' where Name = 'nerandomilast'
Update DrugSubstances set Code = '30270713' where Name = 'hyaluronic acid'
Update DrugSubstances set Code = '30269827' where Name = 'xentuzumab'
Update DrugSubstances set Code = '30209755' where Name = 'melarsomine'
Update DrugSubstances set Code = '30206803' where Name = 'ketoprofen'
Update DrugSubstances set Code = '30000739' where Name = 'diphenhydramine hydrochloride'
Update DrugSubstances set Code = '30000652' where Name = 'lidocaine hydrochloride monohydrate'
Update DrugSubstances set Code = '30000298' where Name = 'chlorhexidine'
Update DrugSubstances set Code = '30000203' where Name = 'lidocaine hydrochloride'
Update DrugSubstances set Code = '29511' where Name = 'n.z. amine as'
Update DrugSubstances set Code = '29390' where Name = 'gallibacterium anatis'
Update DrugSubstances set Code = '29350' where Name = 'avibacterium (haemophilus) paragallinarum'
Update DrugSubstances set Code = '29290' where Name = 'stabilizer'
Update DrugSubstances set Code = '28870' where Name = 'metformin'
Update DrugSubstances set Code = '28830' where Name = 'nintedanib'
Update DrugSubstances set Code = '28750' where Name = 'carmine'
Update DrugSubstances set Code = '28572' where Name = 'pergolide mesilate'
Update DrugSubstances set Code = '28531' where Name = 'faldaprevir sodium'
Update DrugSubstances set Code = '28530' where Name = 'ivermectin'
Update DrugSubstances set Code = '28430' where Name = 'irinotecan hydrochloride'
Update DrugSubstances set Code = '28030' where Name = 'tiotropium'
Update DrugSubstances set Code = '27873' where Name = 'protamine sulphate'
Update DrugSubstances set Code = '27792' where Name = 'ranitidine'
Update DrugSubstances set Code = '27690' where Name = 'chondroitin sulphate'
Update DrugSubstances set Code = '27630' where Name = 'amlodipine'
Update DrugSubstances set Code = '27471' where Name = 'doxycycline'
Update DrugSubstances set Code = '27232' where Name = 'flunisolide hemihydrate'
Update DrugSubstances set Code = '27211' where Name = 'ciclesonide'
Update DrugSubstances set Code = '26959' where Name = 'glucomannan'
Update DrugSubstances set Code = '26890' where Name = 'velagliflozin proline monohydrate'
Update DrugSubstances set Code = '26733' where Name = 'ammonium hydroxide'
Update DrugSubstances set Code = '26331' where Name = 'fetal bovine serum'
Update DrugSubstances set Code = '2632901' where Name = 'bme liquid gibco'
Update DrugSubstances set Code = '2629948' where Name = 'avenciguat'
Update DrugSubstances set Code = '2624662' where Name = 'emulsigen iii'
Update DrugSubstances set Code = '2617824' where Name = 'metamizole sodium'
Update DrugSubstances set Code = '2603310' where Name = 'iclepertin'
Update DrugSubstances set Code = '2598560' where Name = 'equine serum'
Update DrugSubstances set Code = '2596227' where Name = 'sodium citrate'
Update DrugSubstances set Code = '2595669' where Name = 'buffered thimerosal'
Update DrugSubstances set Code = '2595569' where Name = 'adjuvant-2'
Update DrugSubstances set Code = '2595568' where Name = 'adjuvant-1'
Update DrugSubstances set Code = '2595263' where Name = 'saline'
Update DrugSubstances set Code = '2595197' where Name = 'stabilizer - nzsg'
Update DrugSubstances set Code = '2594956' where Name = 'neomycin'
Update DrugSubstances set Code = '2594124' where Name = 'purified water'
Update DrugSubstances set Code = '2594123' where Name = 'thimerosal soln 5%'
Update DrugSubstances set Code = '2594122' where Name = 'mem'
Update DrugSubstances set Code = '2593424' where Name = 'raccoon poxvirus-rabies'
Update DrugSubstances set Code = '2593409' where Name = 'feline immunodeficiency virus'
Update DrugSubstances set Code = '2592229' where Name = 'tryptic (trypticase) soy broth'
Update DrugSubstances set Code = '2592225' where Name = 'MDBK cells'
Update DrugSubstances set Code = '2591667' where Name = 'montanide 888'
Update DrugSubstances set Code = '2591665' where Name = 'gentamicin'
Update DrugSubstances set Code = '2591329' where Name = 'alum'
Update DrugSubstances set Code = '2590942' where Name = 'bbronchiseptica'
Update DrugSubstances set Code = '2590539' where Name = 'sodium bisulfite'
Update DrugSubstances set Code = '2589699' where Name = 'potassium phosphate monobasic'
Update DrugSubstances set Code = '2589692' where Name = 'EDTA'
Update DrugSubstances set Code = '25887' where Name = 'orciprenaline'
Update DrugSubstances set Code = '25871' where Name = 'salbutamol'
Update DrugSubstances set Code = '2586205' where Name = 'l-glutamic acid'
Update DrugSubstances set Code = '2584926' where Name = 'quil-a'
Update DrugSubstances set Code = '2584827' where Name = 'physiological saline'
Update DrugSubstances set Code = '2584746' where Name = 'Mycobacterium paratuberculosis,18 (ST-18)'
Update DrugSubstances set Code = '2583752' where Name = 'emulsigen sa'
Update DrugSubstances set Code = '2583744' where Name = 'neocryl'
Update DrugSubstances set Code = '2583619' where Name = 'Chlamydia psittaci,Cello'
Update DrugSubstances set Code = '2583617' where Name = 'feline leukemia virus'
Update DrugSubstances set Code = '2583583' where Name = 'oil'
Update DrugSubstances set Code = '2583509' where Name = 'balanced salt solution'
Update DrugSubstances set Code = '2581612' where Name = 'bi 1057885'
Update DrugSubstances set Code = '2581053' where Name = 'Borrelia burgdorferi'
Update DrugSubstances set Code = '2577849' where Name = 'maltitol'
Update DrugSubstances set Code = '25350' where Name = 'oxytetracycline'
Update DrugSubstances set Code = '25251' where Name = 'phenylbutazone calcium'
Update DrugSubstances set Code = '25192' where Name = 'empagliflozin'
Update DrugSubstances set Code = '25050' where Name = 'sodium selenite'
Update DrugSubstances set Code = '2500631' where Name = 'bi 655906'
Update DrugSubstances set Code = '2498470' where Name = 'benazepril hydrochloride'
Update DrugSubstances set Code = '2497957' where Name = 'polypropylene glycol'
Update DrugSubstances set Code = '24970' where Name = 'deleobuvir sodium'
Update DrugSubstances set Code = '2495413' where Name = 'hedera helix extract'
Update DrugSubstances set Code = '2495312' where Name = 'propylene glycol dicaprylate-caprate'
Update DrugSubstances set Code = '2495010' where Name = 'aluminium distearat'
Update DrugSubstances set Code = '2488219' where Name = 'difloxacin'
Update DrugSubstances set Code = '2487743' where Name = 'hetacillin potassium'
Update DrugSubstances set Code = '2487693' where Name = 'difloxacin hydrochlorid'
Update DrugSubstances set Code = '2487432' where Name = 'idarucizumab'
Update DrugSubstances set Code = '2487431' where Name = 'risankizumab'
Update DrugSubstances set Code = '2487150' where Name = 'moxidectin'
Update DrugSubstances set Code = '2484686' where Name = 'polyethylene glycol monoisostearate'
Update DrugSubstances set Code = '2484680' where Name = 'oxfendazole'
Update DrugSubstances set Code = '2482972' where Name = 'racecadotril'
Update DrugSubstances set Code = '2481648' where Name = 'sodium sulfachlorpyridazine'
Update DrugSubstances set Code = '2481647' where Name = 'sulfachlorpyridazine'
Update DrugSubstances set Code = '2477018' where Name = 'equine herpesvirus,1:HRA'
Update DrugSubstances set Code = '24770' where Name = 'ampicillin trihydrate'
Update DrugSubstances set Code = '2474167' where Name = 'bevacizumab'
Update DrugSubstances set Code = '2471067' where Name = 'adalimumab'
Update DrugSubstances set Code = '2469884' where Name = 'd-alpha tocopheryl acetate'
Update DrugSubstances set Code = '2460585' where Name = 'nerandomilast'
Update DrugSubstances set Code = '24390' where Name = 'simeticone'
Update DrugSubstances set Code = '24370' where Name = 'olodaterol hydrochloride'
Update DrugSubstances set Code = '24231' where Name = 'sodium citrate dihydrate'
Update DrugSubstances set Code = '24150' where Name = 'codeine phosphate hemihydrate'
Update DrugSubstances set Code = '23970' where Name = 'panax ginseng extract (redirun)'
Update DrugSubstances set Code = '23732' where Name = 'volasertib'
Update DrugSubstances set Code = '23393' where Name = 'olodaterol'
Update DrugSubstances set Code = '23350' where Name = 'tetrasodium edetate'
Update DrugSubstances set Code = '23051' where Name = 'linagliptin'
Update DrugSubstances set Code = '22950' where Name = 'glucose monohydrate'
Update DrugSubstances set Code = '22891' where Name = 'citric acid monohydrate'
Update DrugSubstances set Code = '22871' where Name = 'disodium edetate dihydrate'
Update DrugSubstances set Code = '22870' where Name = 'ipratropium bromide anhydrous'
Update DrugSubstances set Code = '22750' where Name = 'lactose anhydrous'
Update DrugSubstances set Code = '22350' where Name = 'Dulbecco''s modified Eagle Medium with Ham''s foritfied F12'
Update DrugSubstances set Code = '22332' where Name = 'copper complexes of chlorophylls'
Update DrugSubstances set Code = '22290' where Name = 'antifoam'
Update DrugSubstances set Code = '22270' where Name = 'butorphanol tartrate'
Update DrugSubstances set Code = '21690' where Name = 'ritobegron, KUC7483'
Update DrugSubstances set Code = '21570' where Name = 'glucosamine hydrochloride'
Update DrugSubstances set Code = '20970' where Name = 'afatinib dimaleate'
Update DrugSubstances set Code = '20791' where Name = 'crospovidone'
Update DrugSubstances set Code = '20750' where Name = 'polysorbate 20'
Update DrugSubstances set Code = '20671' where Name = 'nintedanib esylate'
Update DrugSubstances set Code = '20650' where Name = 'sodium dihydrogen phosphate dihydrate'
Update DrugSubstances set Code = '20422' where Name = 'cilobradine hydrochloride'
Update DrugSubstances set Code = '19914' where Name = 'dabigatran'
Update DrugSubstances set Code = '19901' where Name = 'dabigatran etexilate'
Update DrugSubstances set Code = '19573' where Name = 'chlorphenamine'
Update DrugSubstances set Code = '19508' where Name = 'climazolam'
Update DrugSubstances set Code = '19112' where Name = 'panax ginseng extract (g 115)'
Update DrugSubstances set Code = '18451' where Name = 'tetracycline hydrochloride'

commit transaction;