﻿using Axon.HAComms.Application.Common.Interfaces;
using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace Axon.HAComms.Application.Helpers;

public class AzureSearchHelper(
    SearchClient searchClient,
    SearchIndexClient searchIndexClient,
    SearchIndexerClient searchIndexerClient,
    IOptionsMonitor<SearchSettings> option,
    ILogger<AzureSearchHelper> logger)
    : IAzureSearchHelper
{
    private readonly SearchSettings settings = option.CurrentValue;
    private const string AZURE_COGNITIVE_SEARCH = "Azure Cognitive Search methods - no need to test";
    
    public async Task<SearchResults<T>> TextSearchAsync<T>(
        string searchText,
        int? skip,
        int? take,
        string? sort,
        string[]? selectFields = null,
        string? filter = null,
        bool fuzzy = false)

    {
        var options = new SearchOptions()
        {
            Filter = filter,
            SearchMode = SearchMode.All,
            QueryType = SearchQueryType.Full,
            Skip = skip,
            Size = take,
            IncludeTotalCount = true
        };

        options.OrderBy.Add(sort);

        if (selectFields is not null)
        {
            foreach (var field in selectFields)
            {
                options.Select.Add(field);
            }
        }

        if (fuzzy) searchText = ConvertToFuzzySearchString(searchText);

        return await searchClient.SearchAsync<T>(searchText, options);
    }

    public static string ConvertToFuzzySearchString(string searchText)
    {
        searchText = searchText.Replace(" ", "~ ");
        searchText += "~";

        return searchText;
    }

    public async Task CreateIndexAsync<T>()
    {
        var analyzer = new CustomAnalyzer("custom-analyzer", LexicalTokenizerName.MicrosoftLanguageStemmingTokenizer)
        {
            TokenFilters = { TokenFilterName.AsciiFolding, TokenFilterName.Lowercase }
        };

        var options = new SearchClientOptions()
        {
            Serializer = new JsonObjectSerializer(
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                })
        };

        var fieldBuilder = new FieldBuilder
        {
            Serializer = options.Serializer
        };

        var index = new SearchIndex(settings.IndexName)
        {
            Fields = fieldBuilder.Build(typeof(T)),
        };

        index.Analyzers.Add(analyzer);

        await searchIndexClient.CreateIndexAsync(index);
    }

    public async Task CreateIndexer()
    {

        var searchIndexer = new SearchIndexer(settings.IndexerName, settings.DataSourceName, settings.IndexName)
        {
            Schedule = new IndexingSchedule(TimeSpan.FromMinutes(settings.Interval))
        };

        await searchIndexerClient.CreateIndexerAsync(searchIndexer);
    }

    public async Task CreateDataSource()
    {
        var dataSource = new SearchIndexerDataSourceConnection(
            settings.DataSourceName,
            SearchIndexerDataSourceType.AzureSql,
            settings.ConnectionString.Default,
            new SearchIndexerDataContainer(settings.TableOrView))
        {
            DataChangeDetectionPolicy = new HighWaterMarkChangeDetectionPolicy(settings.HighWatermarkColumn),
            DataDeletionDetectionPolicy = new SoftDeleteColumnDeletionDetectionPolicy()
            {
                SoftDeleteColumnName = settings.SoftDeleteColumn,
                SoftDeleteMarkerValue = settings.SoftDeleteMarker
            }
        };
        await searchIndexerClient.CreateDataSourceConnectionAsync(dataSource);
    }

    [ExcludeFromCodeCoverage(Justification = AZURE_COGNITIVE_SEARCH)]
    public async Task DeleteIndexer()
    {
        try
        {
            if (await searchIndexerClient.GetIndexerAsync(settings.IndexerName) is not null)
            {
                await searchIndexerClient.DeleteIndexerAsync(settings.IndexerName);
            }
        }
        catch (RequestFailedException e) when (e.Status == 404)
        {
            logger.LogWarning(e, "The indexer {IndexerName} doesn't exist.", settings.IndexerName);
        }
    }

    [ExcludeFromCodeCoverage(Justification = AZURE_COGNITIVE_SEARCH)]
    public async Task DeleteIndex()
    {
        try
        {
            if (await searchIndexClient.GetIndexAsync(settings.IndexName) is not null)
            {
                await searchIndexClient.DeleteIndexAsync(settings.IndexName);
            }
        }
        catch (RequestFailedException e) when (e.Status == 404)
        {
            logger.LogWarning(e, "The index {IndexName} doesn't exist. No deletion occurred.", settings.IndexName);
        }
    }
    
    [ExcludeFromCodeCoverage(Justification = AZURE_COGNITIVE_SEARCH)]
    public async Task DeleteDataSource()
    {
        try
        {
            var existingDataSource = await searchIndexerClient.GetDataSourceConnectionAsync(settings.DataSourceName);
            if (existingDataSource is not null)
            {
                await searchIndexerClient.DeleteDataSourceConnectionAsync(existingDataSource);
            }
        }
        catch (RequestFailedException e) when (e.Status == 404)
        {
            logger.LogWarning(e, "No data source with the name {DataSourceName} was found in service {ServiceName}.", settings.DataSourceName, settings.ServiceName);
        }
    }
}
