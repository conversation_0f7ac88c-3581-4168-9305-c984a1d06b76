﻿using Axon.HAComms.Application.Models.Country;
using Axon.HAComms.Application.Models.DosageForm;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Application.Models.ProductType;
using Axon.HAComms.Application.Models.RoutesOfAdministration;
using Axon.HAComms.Application.Models.SubmissionType;
using Axon.HAComms.Application.Models.Tags;
using MediatR;

namespace Axon.HAComms.Application.Commands.Comments.Search;

public class SearchCommentCommandRequest : IRequest<SearchCommentCommandResponse>
{
    public DateTime? StartDate { get; }

    public DateTime? EndDate { get; }

    public List<ProductDtoModel>? Products { get; }

    public List<DosageFormModel>? DosageForms { get; }

    public List<RouteOfAdministrationModel>? RoutesOfAdministration { get; }

    public List<DrugSubstanceDtoModel>? DrugSubstances { get; }

    public List<CountryModel>? Countries { get; }

    public List<SubmissionTypeModel>? SubmissionTypes { get; }

    public List<string>? ApplicationNumbers { get; }

    public List<string>? SubmissionNumbers { get; }

    public List<TagModel>? Tags { get; }

    public List<ProductTypeModel>? ProductTypes { get; set; }

    public List<string>? ProductCodes { get; set; }

    public bool? IsGeneralGuidance { get; set; }

    public bool? IsQuestionIncluded { get; set; }

    public string? SearchText { get; }
    public int? Skip { get; }
    public int? Take { get; }
    public string? Sort { get; }
    public bool? Fuzzy { get; }

    public string? RequestType { get; set; }

    public SearchCommentCommandRequest(
        DateTime? startDate,
        DateTime? endDate,
        List<ProductDtoModel> products,
        List<DosageFormModel> dosageForms,
        List<RouteOfAdministrationModel> routesOfAdministration,
        List<DrugSubstanceDtoModel> drugSubstances,
        List<CountryModel> countries,
        List<SubmissionTypeModel> submissionTypes,
        List<string> applicationNumbers,
        List<string> submissionNumbers,
        List<TagModel> tags,
        List<ProductTypeModel> productTypes,
        List<string> productCodes,
        string searchText,
        bool? isGeneralGuidance,
        bool? isQuestionIncluded,
        int? skip,
        int? take,
        string? sort,
        bool? fuzzy,
        string requestType
        )
    {
        StartDate = startDate;
        EndDate = endDate;
        Products = products;
        DosageForms = dosageForms;
        RoutesOfAdministration = routesOfAdministration;
        DrugSubstances = drugSubstances;
        Countries = countries;
        SubmissionTypes = submissionTypes;
        ApplicationNumbers = applicationNumbers;
        SubmissionNumbers = submissionNumbers;
        Tags = tags;
        ProductTypes = productTypes;
        ProductCodes = productCodes;
        SearchText = searchText;
        IsGeneralGuidance = isGeneralGuidance;
        IsQuestionIncluded = isQuestionIncluded;
        Skip = skip;
        Take = take;
        Sort = sort;
        Fuzzy = fuzzy;
        RequestType = requestType;
    }
}
