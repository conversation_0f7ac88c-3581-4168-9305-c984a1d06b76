﻿using Axon.HAComms.Application.Models.RoutesOfAdministration;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.RoutesOfAdministration.PagedListQuery;

public class GetRoutesOfAdministrationPagedListQueryRequest(
    string[]? filters,
    int skip,
    int take,
    string? order)
    : IRequest<ApiPagedListResult<RouteOfAdministrationPagedListModel>>
{
    public string[]? Filters { get; } = filters;
    public int Skip { get; } = skip;
    public int Take { get; } = take;
    public string? Order { get; } = order;
}
