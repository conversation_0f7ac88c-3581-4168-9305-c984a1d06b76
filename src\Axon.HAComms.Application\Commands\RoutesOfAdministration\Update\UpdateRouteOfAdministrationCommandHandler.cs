using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Update;

internal class UpdateRouteOfAdministrationCommandHandler(
    IRouteOfAdministrationRepository repoRoutesOfAdministration,
    IMapper mapper,
    ILogger<UpdateRouteOfAdministrationCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<UpdateRouteOfAdministrationCommandRequest, UpdateRouteOfAdministrationCommandResponse>
{
    public async Task<UpdateRouteOfAdministrationCommandResponse> Handle(UpdateRouteOfAdministrationCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoRoutesOfAdministration.GetItemAsync(request.Id);
        var correlationId = correlationIdProvider.Provide();

        await auditService.LogAsync(
            correlationId, clientDetailsProvider.Provide(),
            AuditEventType.ROUTE_OF_ADMINISTRATION_UPDATED, AuditEventCategory.ROUTES_OF_ADMINISTRATION, AuditEventDescription.ROUTE_OF_ADMINISTRATION_UPDATE, entity,
            async () =>
            {
                entity.Name = request.Name;
                repoRoutesOfAdministration.UpdateItem(entity);
                await repoRoutesOfAdministration.SaveChangesAsync(userProvider);
                logger.LogInformation("Route Of Administration {Route} updated successfully.", entity.Name);
            });
            
        var response = mapper.Map<UpdateRouteOfAdministrationCommandResponse>(entity);
        response.Id = request.Id;
        return response;
    }
}
