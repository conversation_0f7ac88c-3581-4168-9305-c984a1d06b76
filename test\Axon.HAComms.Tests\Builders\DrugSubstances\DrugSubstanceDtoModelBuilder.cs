﻿using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.Tests.Builders.DrugSubstances;

public class DrugSubstanceDtoModelBuilder : IBuilder<DrugSubstanceDtoModel>
{
    private int id = Fake.DrugSubstance.Id;
    private string name = Fake.DrugSubstance.Name;

    public static DrugSubstanceDtoModelBuilder Default() => new();

    public DrugSubstanceDtoModelBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public DrugSubstanceDtoModelBuilder WithId(int id)
    {
        this.id = id;
        return this;
    }

    public DrugSubstanceDtoModel Build()
    {
        return new(id, name)
        {
            Id = id,
            Name = name
        };
    }
}
