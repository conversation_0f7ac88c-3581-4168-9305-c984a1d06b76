﻿using AutoMapper;
using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Tags.Create;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Create;

public class CreateTagCommandHandlerTests
{
    private readonly CreateTagCommandHandler sut;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public CreateTagCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new TagMappingProfile());
        });
        var mapper = mockMapper.CreateMapper();
        var tagsRepo = Substitute.For<ITagRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        auditService = Substitute.For<IAuditService>();
        var logger = Substitute.For<ILogger<CreateTagCommandHandler>>();
        sut = new CreateTagCommandHandler(tagsRepo, mapper, logger, auditService, correlationIdProvider, clientDetailsProvider, userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;

        var request = new CreateTagCommandRequest(tagName, tagDescription);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(request.Name);
        result.Description.Should().Be(request.Description);
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var tagName = Fake.Tag.Name;
        var tagDescription = Fake.Tag.Description;

        var request = new CreateTagCommandRequest(tagName, tagDescription);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        await auditService
            .ReceivedWithAnyArgs(1)
            .LogAsync(Guid.Empty, default, default, default, default, default, default);
        await auditService
            .Received(1)
            .LogAsync(correlationId, clientDetails, AuditEventType.TAG_CREATED, AuditEventCategory.TAGS, AuditEventDescription.TAG_CREATE,
                Arg.Any<Tag>(), Arg.Any<Func<Task>>());
    }
}
