﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.DrugSubstances;

[Collection(TestCollectionIDs.IntegrationTests)]
public class CreateDrugSubstancesIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly DrugSubstancesApi apiDrugSubstances = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task CreateDrugSubstances_ValidRequest_ReturnsOk()
    {
        //Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var requestObj = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);

        //Act
        var responseObj = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Name.Should().Be(drugSubstanceName);
        responseObj.Code.Should().Be(drugSubstanceCode);
        responseObj.Description.Should().Be(drugSubstanceDescription);
    }

    [Fact]
    public async Task CreateDrugSubstances_WithDuplicateCode_ThrowsAlreadyExistsException()
    {
        //Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var requestObj1 = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);

        var requestObj2 = new CreateDrugSubstanceCommandRequest(Fake.DrugSubstance.Name, drugSubstanceCode, drugSubstanceDescription);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);

        responseObj1.Should().NotBeNull();

        var responseObj2 = () => apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj2);

        //Assert
        var exception = await responseObj2.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("AlreadyExists");
    }

    [Fact]
    public async Task CreateDrugSubstances_WithDuplicateName_ThrowsAlreadyExistsException()
    {
        //Arrange
        var drugSubstanceName = Fake.DrugSubstance.Name;
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var requestObj1 = new CreateDrugSubstanceCommandRequest(drugSubstanceName, drugSubstanceCode, drugSubstanceDescription);
        var requestObj2 = new CreateDrugSubstanceCommandRequest(drugSubstanceName, Fake.DrugSubstance.Code, drugSubstanceDescription);

        //Act
        var responseObj1 = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj1);

        responseObj1.Should().NotBeNull();

        var responseObj2 = () => apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj2);

        //Assert
        var exception = await responseObj2.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("AlreadyExists");
    }

    [Fact]
    public async Task CreateDrugSubstances_WithEmptyName_ReturnsOk()
    {
        //Arrange
        var drugSubstanceCode = Fake.DrugSubstance.Code;
        var drugSubstanceDescription = Fake.DrugSubstance.Description;

        var requestObj = new CreateDrugSubstanceCommandRequest(string.Empty, drugSubstanceCode, drugSubstanceDescription);

        //Act
        var responseObj = await apiDrugSubstances.CreateDrugSubstanceAsync(TenantConstants.DEFAULT_TENANT, createDrugSubstanceCommandRequest: requestObj);

        //Assert
        responseObj.Should().NotBeNull();
        responseObj.Name.Should().Be(Constants.NotAssigned);
        responseObj.Code.Should().Be(drugSubstanceCode);
        responseObj.Description.Should().Be(drugSubstanceDescription);
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }

    public async Task DisposeAsync()
    {
        dbContext.DrugSubstances.Clear();
        await dbContext.SaveChangesAsync();
    }
}
