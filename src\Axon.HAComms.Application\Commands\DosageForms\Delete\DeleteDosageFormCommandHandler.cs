using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using Phlex.Core.FunctionalExtensions.Results;

namespace Axon.HAComms.Application.Commands.DosageForms.Delete;

public class DeleteDosageFormCommandHandler(
    IDosageFormsRepository repoDosageForms,
    IProductExtensionsRepository productExtensionsRepo,
    ILogger<DeleteDosageFormCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<DeleteDosageFormCommandRequest, Result>
{
    public async Task<Result> Handle(DeleteDosageFormCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoDosageForms.GetItemAsync(request.Id);

        if (await productExtensionsRepo.ExistsAsync(x => x.DosageForm.Id == entity.Id))
        {
            logger.LogWarning("Dosage form {DosageFormId} has associated products and cannot be deleted.", entity.Id);
            throw new AssociationExistsException("Dosage form", entity.Id);
        }

        repoDosageForms.DeleteItem(entity);
        await repoDosageForms.SaveChangesAsync(userProvider);

        auditService.Log(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DOSAGE_FORM_DELETED, AuditEventCategory.DOSAGE_FORMS,
            AuditEventDescription.DOSAGE_FORM_DELETE, entity);

        return Result.Success();
    }
}
