﻿using Axon.HAComms.Application.Models.DrugSubstances;
using MediatR;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Application.Queries.DrugSubstances.PagedListQuery;

public class GetDrugSubstancesPagedListQueryRequest(
    string[]? filters,
    int skip,
    int take,
    string? order)
    : IRequest<ApiPagedListResult<DrugSubstancePagedListModel>>
{
    public string[]? Filters { get; } = filters;
    public int Skip { get; } = skip;
    public int Take { get; } = take;
    public string? Order { get; } = order;
}
