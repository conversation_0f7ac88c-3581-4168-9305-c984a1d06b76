#This script is taking LASTCOMMITHASH environmnetal variable from pod, which is a part
#of axon-hacomms-api deployment and is in running state.
#Then Last commit hash of repository is taken and both are compared and output is published as agent variable
#is_commit_hash_different

param($cluster_name, $resource_group)

$ErrorActionPreference = "Stop"

function checkError
{
    param($message)
    if ($LASTEXITCODE -ne 0)
    {
        write-Error -Message $message -ErrorAction Stop
    }
}

function getCurrentBuildLastCommitHashCommit
{
    $commitHash = git log -1 --format=%H
    checkError "Error when getting last git commit hash"
    return $commitHash
}

function getLastCommitHashOfBuildInCluster
{
    az aks get-credentials -n $cluster_name -g $resource_group --admin --overwrite-existing
    checkError "Error when getting aks credentials"

    $pods = kubectl get po -o=json -n axon-hacomms | ConvertFrom-Json | select-object -ExpandProperty items
    checkError "Error when getting data from cluster"

    foreach ($pod in $pods)
    {
        if ($pod.spec.containers.name -eq "axon-hacomms-api" -and $pod.status.phase -eq "Running")
        {
            $api_pod_name = $pod.metadata.name
            break
        }
    }

    if ($api_pod_name -eq $null)
    {
        throw "Hacomms api pod in running state not found in the cluster"
    }

    $pod_environmental_variables = kubectl exec $api_pod_name -n axon-hacomms -- env
    checkError "Error when getting data from cluster"

    $lastCommitHashEnvVariable_Value = $pod_environmental_variables | select-string LASTCOMMITHASH

    if (-not$lastCommitHashEnvVariable_Value -or $lastCommitHashEnvVariable_Value -eq $null)
    {
        throw "Last commit hash in not available in a pod"
    }

    $pattern = "(?<=\=)(.*)"
    $match = [regex]::Match($lastCommitHashEnvVariable_Value, $pattern)
    return $match.Groups[1].Value
}

$lastCommitHashOfCurrentBuild = getCurrentBuildLastCommitHashCommit
write-host "Last commit hash of current build $lastCommitHashOfCurrentBuild"

$lastCommitHashOfBuildInCluster = getLastCommitHashOfBuildInCluster
write-host "Last commit hash of build run in cluster $lastCommitHashOfBuildInCluster"

if ($lastCommitHashOfBuildInCluster -ne $lastCommitHashOfCurrentBuild)
{
    write-host "There is new code in repository. Cannot deploy"
    $is_commit_hash_different = "true"
}
else
{
    write-host "Deployed code is the same as already run on cluster"
    $is_commit_hash_different = "false"
}

write-host "##vso[task.setvariable variable=is_commit_hash_different;isOutput=true]$is_commit_hash_different"
