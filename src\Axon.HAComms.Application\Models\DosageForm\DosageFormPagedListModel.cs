namespace Axon.HAComms.Application.Models.DosageForm
{
	public class DosageFormPagedListModel
	{
        public int Id { get; set; }
        public string Name { get; set; }
        public bool IsAssociatedToProduct { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
		public string? LastUpdatedBy { get; set; }

		public DosageFormPagedListModel(
			int id, 
			string name, 
			bool isAssociatedToProduct,
            DateTime? createdDate,
			string? createdBy,
            DateTime? lastUpdatedDate, 
			string? lastUpdatedBy)
		{
            Id = id;
			Name = name;
			IsAssociatedToProduct = isAssociatedToProduct;
            CreatedDate = createdDate;
			CreatedBy = createdBy;
            LastUpdatedDate = lastUpdatedDate;
			LastUpdatedBy = lastUpdatedBy;
		}
    }
}
