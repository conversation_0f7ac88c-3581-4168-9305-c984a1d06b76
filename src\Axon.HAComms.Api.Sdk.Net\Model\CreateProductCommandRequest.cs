/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// CreateProductCommandRequest
    /// </summary>
    [DataContract(Name = "CreateProductCommandRequest")]
    public partial class CreateProductCommandRequest : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateProductCommandRequest" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected CreateProductCommandRequest() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="CreateProductCommandRequest" /> class.
        /// </summary>
        /// <param name="name">name (required).</param>
        /// <param name="isActive">isActive.</param>
        /// <param name="productTypeIds">productTypeIds.</param>
        /// <param name="drugSubstanceIds">drugSubstanceIds.</param>
        /// <param name="productExtensions">productExtensions.</param>
        /// <param name="notAssigned">notAssigned.</param>
        public CreateProductCommandRequest(string name = default(string), bool isActive = default(bool), List<int> productTypeIds = default(List<int>), List<int> drugSubstanceIds = default(List<int>), List<ProductExtensionModel> productExtensions = default(List<ProductExtensionModel>), string notAssigned = default(string))
        {
            // to ensure "name" is required (not null)
            if (name == null)
            {
                throw new ArgumentNullException("name is a required property for CreateProductCommandRequest and cannot be null");
            }
            this.Name = name;
            this.IsActive = isActive;
            this.ProductTypeIds = productTypeIds;
            this.DrugSubstanceIds = drugSubstanceIds;
            this.ProductExtensions = productExtensions;
            this.NotAssigned = notAssigned;
        }

        /// <summary>
        /// Gets or Sets Name
        /// </summary>
        [DataMember(Name = "name", IsRequired = true, EmitDefaultValue = true)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets IsActive
        /// </summary>
        [DataMember(Name = "isActive", EmitDefaultValue = true)]
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or Sets ProductTypeIds
        /// </summary>
        [DataMember(Name = "productTypeIds", EmitDefaultValue = true)]
        public List<int> ProductTypeIds { get; set; }

        /// <summary>
        /// Gets or Sets DrugSubstanceIds
        /// </summary>
        [DataMember(Name = "drugSubstanceIds", EmitDefaultValue = true)]
        public List<int> DrugSubstanceIds { get; set; }

        /// <summary>
        /// Gets or Sets ProductExtensions
        /// </summary>
        [DataMember(Name = "productExtensions", EmitDefaultValue = true)]
        public List<ProductExtensionModel> ProductExtensions { get; set; }

        /// <summary>
        /// Gets or Sets NotAssigned
        /// </summary>
        [DataMember(Name = "notAssigned", EmitDefaultValue = true)]
        public string NotAssigned { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class CreateProductCommandRequest {\n");
            sb.Append("  Name: ").Append(Name).Append("\n");
            sb.Append("  IsActive: ").Append(IsActive).Append("\n");
            sb.Append("  ProductTypeIds: ").Append(ProductTypeIds).Append("\n");
            sb.Append("  DrugSubstanceIds: ").Append(DrugSubstanceIds).Append("\n");
            sb.Append("  ProductExtensions: ").Append(ProductExtensions).Append("\n");
            sb.Append("  NotAssigned: ").Append(NotAssigned).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // Name (string) minLength
            if (this.Name != null && this.Name.Length < 1)
            {
                yield return new System.ComponentModel.DataAnnotations.ValidationResult("Invalid value for Name, length must be greater than 1.", new [] { "Name" });
            }

            yield break;
        }
    }

}
