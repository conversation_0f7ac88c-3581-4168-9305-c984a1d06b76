﻿
using Axon.HAComms.Domain.Entities;

#nullable enable

namespace Axon.HAComms.Infrastructure.Extensions
{
	public static class DbContextExtensions
	{
		public static IQueryable<DrugSubstance> FilterBy(this IQueryable<DrugSubstance> data, string name, string code, string description)
		{
			if (!string.IsNullOrWhiteSpace(name))
			{
				data = data.Where(x => x.Name!.Contains(name));
			}

			if (!string.IsNullOrWhiteSpace(code))
			{
				data = data.Where(x => x.Code.Contains(code));
			}

			if (!string.IsNullOrWhiteSpace(description))
			{
				data = data.Where(x => x.Description!.Contains(description));
			}

			return data;
		}

		public static IQueryable<DrugSubstance> Order(this IQueryable<DrugSubstance> dbSet, string order)
		{
			return dbSet;
		}
	}
}
