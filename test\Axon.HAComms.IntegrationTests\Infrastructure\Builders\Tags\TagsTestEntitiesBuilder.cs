using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;

public static class TagsTestEntitiesBuilder
{
    public static async Task<List<Tag>> Build(HACommsContext dbContext, int entries = 100)
    {
        var list = new List<Tag>();

        for (var i = 0; i < entries; i++)
        {
            list.Add(CreateTagsBuilder.Default().Build());
        }

        dbContext.Tags.AddRange(list);
        await dbContext.SaveChangesAsync();

        return list;
    }
}
