﻿using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Base;
using Axon.HAComms.Infrastructure.Persistance.Configurations.Extensions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class DosageFormConfiguration : BaseEntityConfiguration<DosageForm>
{
    protected override void ConfigureEntity(EntityTypeBuilder<DosageForm> builder)
    {
        builder.Property(e => e.Name)
            .ConfigureNameField();
    }
}
