﻿using Axon.HAComms.Application.Common.Interfaces;
using Microsoft.Extensions.Configuration;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net;

namespace Axon.HAComms.Application.Services
{
    public class EmailService : IEmailService
    {
        private readonly string? senderEmail;
        private readonly ISendGridClient sendGridClient;

        public EmailService(IConfiguration configuration, ISendGridClient sendGridClient)
        {
            senderEmail = configuration["SoftDeleteHostedService:sender"];
            this.sendGridClient = sendGridClient;
        }

        public async Task<(bool, HttpStatusCode)> Send(string[] receivers, string subject, string messageContent)
        {
            SendGridMessage msg = new();
            msg.SetFrom(new EmailAddress(this.senderEmail));
            foreach (var receiver in receivers)
            {
                msg.AddTo(receiver);
            }
            msg.SetSubject(subject);
            msg.PlainTextContent = messageContent;

            Response response = await sendGridClient.SendEmailAsync(msg).ConfigureAwait(false);
            return (response.IsSuccessStatusCode, response.StatusCode);
        }
    }
}
