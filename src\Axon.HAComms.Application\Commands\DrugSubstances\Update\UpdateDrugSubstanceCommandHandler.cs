using AutoMapper;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Common;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.Application.Commands.DrugSubstances.Update;

internal class UpdateDrugSubstanceCommandHandler(
    IDrugSubstancesRepository repoDrugSubstances,
    IMapper mapper,
    ILogger<UpdateDrugSubstanceCommandHandler> logger,
    ICorrelationIdProvider correlationIdProvider,
    IClientDetailsProvider clientDetailsProvider,
    IUserProvider userProvider,
    IAuditService auditService) : IRequestHandler<UpdateDrugSubstanceCommandRequest, UpdateDrugSubstanceCommandResponse>
{
    public async Task<UpdateDrugSubstanceCommandResponse> Handle(UpdateDrugSubstanceCommandRequest request, CancellationToken cancellationToken)
    {
        var entity = await repoDrugSubstances.GetItemAsync(request.Id);

        await auditService.LogAsync(
            correlationIdProvider.Provide(), clientDetailsProvider.Provide(),
            AuditEventType.DRUG_SUBSTANCE_UPDATED, AuditEventCategory.DRUG_SUBSTANCES, AuditEventDescription.DRUG_SUBSTANCE_UPDATE, entity,
            async () =>
            {
                entity.Name = string.IsNullOrEmpty(request.Name.Trim()) ? Constants.NotAssigned : request.Name;
                entity.Description = request.Description;
                entity.Code = request.Code;
                repoDrugSubstances.UpdateItem(entity);
                await repoDrugSubstances.SaveChangesAsync(userProvider);
                logger.LogInformation("Drug Substance {DrugSubstance} updated successfully.", entity.Name);
            });

        var response = mapper.Map<UpdateDrugSubstanceCommandResponse>(entity);
        response.Id = request.Id;
        return response;
    }
}
