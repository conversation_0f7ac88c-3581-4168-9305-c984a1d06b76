<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo><!-- setting GenerateAssemblyInfo to false causes this bug https://github.com/dotnet/project-system/issues/3934 -->
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Axon.HAComms.Api.Sdk.Net</AssemblyName>
    <PackageId>Axon.HAComms.Api.Sdk.Net</PackageId>
    <OutputType>Library</OutputType>
    <Authors>OpenAPI</Authors>
    <Company>OpenAPI</Company>
    <AssemblyTitle>OpenAPI Library</AssemblyTitle>
    <Description>A library generated from a OpenAPI doc</Description>
    <Copyright>No Copyright</Copyright>
    <RootNamespace>Axon.HAComms.Api.Sdk.Net</RootNamespace>
    <Version>1.0.0</Version>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\Axon.HAComms.Api.Sdk.Net.xml</DocumentationFile>
    <RepositoryUrl>https://github.com/GIT_USER_ID/GIT_REPO_ID.git</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageReleaseNotes>Minor update</PackageReleaseNotes>
    <Nullable>annotations</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Polly" Version="8.1.0" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="System.Web" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web" />
  </ItemGroup>
</Project>
