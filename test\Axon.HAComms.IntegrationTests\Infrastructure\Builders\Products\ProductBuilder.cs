using Axon.HAComms.Domain.Entities;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;

namespace Axon.HAComms.IntegrationTests.Infrastructure.Builders.Products;

public class ProductBuilder
{
    private string name = Fake.Product.Name;
    private bool isActive = Fake.Product.IsActive;
    private List<DrugSubstance> drugSubstances = [];
    private List<ProductExtension> productExtensions = [];
    private List<ProductType> productTypes = [];
    private string tenant = TenantConstants.DEFAULT_TENANT;

    public static ProductBuilder Default() => new();

    public Product Build()
    {
        var result = new Product()
        {
            Name = name,
            IsActive = isActive,
            ProductExtensions = productExtensions,
            DrugSubstances = drugSubstances,
            ProductTypes = productTypes,
            Tenant = tenant
        };
        return result;
    }

    public ProductBuilder WithName(string productName)
    {
        name = productName;
        return this;
    }

    public ProductBuilder WithIsActive(bool productIsActive)
    {
        isActive = productIsActive;
        return this;
    }

    public ProductBuilder WithDrugSubstances(List<DrugSubstance> productDrugSubstances)
    {
        drugSubstances = productDrugSubstances;
        return this;
    }

    public ProductBuilder WithProductExtensions(params ProductExtension[] productExtensions)
    {
        this.productExtensions = productExtensions.ToList();
        return this;
    }

    public ProductBuilder WithProductTypes(params ProductType[] productTypes)
    {
        this.productTypes = productTypes.ToList();
        return this;
    }

    public ProductBuilder WithTenant(string tenant)
    {
        this.tenant = tenant;
        return this;
    }
}
