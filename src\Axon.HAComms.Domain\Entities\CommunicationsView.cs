﻿using Axon.HAComms.Domain.Entities.Base;

namespace Axon.HAComms.Domain.Entities;

public class CommunicationsView : MultiTenantEntity
{
    public string? Subject { get; set; }
    public int CountryId { get; set; }
    public string CountryName { get; set; } = string.Empty;
    public string? ProductNames { get; set; }
    public DateTime DateOfCommunication { get; set; }
    public bool IsCompleted { get; set; }
}
