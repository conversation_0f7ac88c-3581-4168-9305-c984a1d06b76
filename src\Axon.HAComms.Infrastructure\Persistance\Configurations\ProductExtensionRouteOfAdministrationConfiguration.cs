﻿using Axon.HAComms.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class ProductExtensionRouteOfAdministrationConfiguration : IEntityTypeConfiguration<ProductExtensionRouteOfAdministration>
{
    public void Configure(EntityTypeBuilder<ProductExtensionRouteOfAdministration> builder)
    {
        builder.Property(e => e.CreatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.LastUpdatedDate)
            .HasColumnType("datetime2");

        builder.Property(e => e.CreatedBy)
            .IsRequired(false)
            .HasMaxLength(256);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}
