﻿using MediatR;
using System.ComponentModel.DataAnnotations;

namespace Axon.HAComms.Application.Commands.RoutesOfAdministration.Create
{
    public class CreateRouteOfAdministrationCommandRequest : IRequest<CreateRouteOfAdministrationCommandResponse>
    {
        [Required]
        public string Name { get; }

        public CreateRouteOfAdministrationCommandRequest(string name)
        {
            this.Name = name;
        }
    }
}
