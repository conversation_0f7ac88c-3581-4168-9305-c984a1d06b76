﻿using Axon.Core.Shared.Audit;
using Axon.HAComms.Application.Queries.Audit.GetAuditsQuery;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.OData.Edm;
using Microsoft.OData.ModelBuilder;
using Microsoft.OData.UriParser;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Audit.GetAuditsQuery;

public class GetAuditsQueryValidatorTests
{
    private readonly GetAuditsQueryValidator validator = new();
    private readonly IEdmModel edmModel;
    private readonly HttpRequest httpRequest;

    public GetAuditsQueryValidatorTests()
    {
        var modelBuilder = new ODataConventionModelBuilder();
        modelBuilder.EntitySet<ApplicationAudit>("Audits");
        edmModel = modelBuilder.GetEdmModel();

        HttpContext context = new DefaultHttpContext();
        httpRequest = context.Request;
    }

    [Fact]
    public void Validate_TenantIsEmpty_ThrowsException()
    {
        // Arrange
        var oDataQueryContext = new ODataQueryContext(edmModel, typeof(ApplicationAudit), new ODataPath());
        var request = new GetAuditsQueryRequest(string.Empty, new ODataQueryOptions<ApplicationAudit>(oDataQueryContext, httpRequest));

        // Act
        var result = validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Tenant);
        Assert.Contains("'Tenant' must not be empty", result.Errors[0].ErrorMessage);
    }

    [Fact]
    public void Validate_CommunicationIdIsNotEmpty_DoesNotThrowException()
    {
        // Arrange
        var oDataQueryContext = new ODataQueryContext(edmModel, typeof(ApplicationAudit), new ODataPath());
        var request = new GetAuditsQueryRequest(Fake.Tenant, new ODataQueryOptions<ApplicationAudit>(oDataQueryContext, httpRequest));

        // Act
        var result = validator.TestValidate(request);

        //Assert
        result.IsValid.Should().BeTrue();
    }
}
