/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// GetEventConstantsQueryResponse
    /// </summary>
    [DataContract(Name = "GetEventConstantsQueryResponse")]
    public partial class GetEventConstantsQueryResponse : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetEventConstantsQueryResponse" /> class.
        /// </summary>
        /// <param name="eventCategories">eventCategories.</param>
        /// <param name="eventTypes">eventTypes.</param>
        public GetEventConstantsQueryResponse(List<string> eventCategories = default(List<string>), List<string> eventTypes = default(List<string>))
        {
            this.EventCategories = eventCategories;
            this.EventTypes = eventTypes;
        }

        /// <summary>
        /// Gets or Sets EventCategories
        /// </summary>
        [DataMember(Name = "eventCategories", EmitDefaultValue = true)]
        public List<string> EventCategories { get; set; }

        /// <summary>
        /// Gets or Sets EventTypes
        /// </summary>
        [DataMember(Name = "eventTypes", EmitDefaultValue = true)]
        public List<string> EventTypes { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class GetEventConstantsQueryResponse {\n");
            sb.Append("  EventCategories: ").Append(EventCategories).Append("\n");
            sb.Append("  EventTypes: ").Append(EventTypes).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
