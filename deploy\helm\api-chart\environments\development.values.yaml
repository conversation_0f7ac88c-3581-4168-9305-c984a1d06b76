image:
  repository: phlexglobal.azurecr.io/axon-hacomms-api
  pullPolicy: Always
  tag: dev

ingress:
  tls:
    - tlsSecretName: tls-app-dev-smartphlex-com
      hosts:
        - app-dev.smartphlex.com
  hosts:
    - host: app-dev.smartphlex.com
      paths:
        - path: /axon-hacomms-api/(.*)

replicas: 1
minAvailability: 0

keyVaultName: hac-dev-kv-eun
clientId: 167cd45b-7d4f-4b3d-8c05-a87f12c40609

corsOriginUrl0: https://app-dev.smartphlex.com
corsOriginUrl1: https://localhost:4070

aspNetCoreEnvironment: Production
azureSearch:
  isEnabled: true
  serviceName: ss-nonprod-ss-eun
  IndexName: hacomms-index-dev
  IndexerName: hacomms-indexer-dev
  DataSourceName: hacomms-db-dev

azureWorkload:
  appName: axon-hacomms-dev
  clientId: 6cc7e19d-3d23-455b-a2a3-f2a2e8af6d7c
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: '86400' # Token is valid for 1 day

AppScope: "api://smartphlex-dev/.default"
ApiHost: "https://app-dev.smartphlex.com/api/core"
GrpcHost: "http://axon-core-api-grpc.axon-core-dev.svc.cluster.local:9090"

DataProtectionBlobStorageUri: 'https://axndevstorageeun.blob.core.windows.net/'
DataProtectionKeyVaultKey: 'https://axn-dev-kv-eun.vault.azure.net/keys/AxonDataProtection'