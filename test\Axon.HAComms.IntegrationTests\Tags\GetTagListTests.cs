﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Applications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Communications;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.ProductExtensions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Submissions;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Tags;

[Collection(TestCollectionIDs.IntegrationTests)]
public class GetTagListTests(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly TagApi tagsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationsApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task GetTagList_GetRequest_ReturnsOk()
    {
        //Arrange
        var currentTagsCount = dbContext.Tags.Count();
        var tag1 = CreateTagsBuilder.Default().WithName(Fake.Tag.Name).WithDescription(Fake.Tag.Description).Build();
        var tag2 = CreateTagsBuilder.Default().WithName(Fake.Tag.Name).WithDescription(Fake.Tag.Description).Build();
        var tag3 = CreateTagsBuilder.Default().WithName(Fake.Tag.Name).WithDescription(Fake.Tag.Description).Build();
        await dbContext.Tags.AddRangeAsync(tag1, tag2, tag3);
        await dbContext.SaveChangesAsync();

        //Act
        var responseObj = await tagsApi.GetTagsListAsync(TenantConstants.DEFAULT_TENANT);

        //Assert
        responseObj.Data.Should().HaveCount(currentTagsCount + 3);
        var responseNames = responseObj.Data.Select(x => x.Name).ToArray();
        responseNames.Should().Contain(tag1.Name);
        responseNames.Should().Contain(tag2.Name);
        responseNames.Should().Contain(tag3.Name);


        dbContext.Tags.RemoveRange(tag1, tag2, tag3);
        await dbContext.SaveChangesAsync();
    }
    [Fact]
    public async Task GetPagedTagsList_ValidRequest_ReturnsOk()
    {
        //Arrange
        var currentTagCount = dbContext.Tags.Count();
        await TagsTestEntitiesBuilder.Build(dbContext, 56);            

        //Act
        var responseObj = await tagsApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT, null, currentTagCount + 50, 10);

        //Assert
        responseObj.Data.Should().HaveCount(6);
    }

    [Fact]
    public async Task GetPagedTagsList_ValidateAssociatedToCommentValue_ValidRequest_ReturnsOk()
    {
        //Arrange
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 10);

        var dateOfCommunication = DateTime.Now;
        var submissionType = await dbContext.SubmissionTypes.GetRandomEntity();
        var country = await dbContext.Countries.GetRandomEntity();
        var product = await ApiTestHelper.CreateProductForTest(dbContext, productsApi, 3);
        var productExtension = product.ProductExtensions[0];
        var selectedTagIds = new[] { tags[2], tags[5] }.Select(x => x.Id).ToArray();

        var submission = SubmissionModelBuilder.Default().Build();
        var application = ApplicationModelBuilder.Default()
            .WithSubmissions(submission).Build();
        var comment = CreateCommentCommandRequestBuilder.Default()
            .WithProductExtensions(new ProductExtensionCommentModel() { ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList() })
            .WithDrugSubstanceIds(product.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(selectedTagIds).Build();

        var request = CreateCommunicationCommandRequestBuilder.Default()
            .WithSubmissionTypeId(submissionType.Id)
            .WithCountryId(country.Id)
            .WithDateOfCommunication(dateOfCommunication)
            .WithApplications(application)
            .WithComment(comment).Build();

        //Act
        var response = await communicationsApi.CreateCommunicationAsync(TenantConstants.DEFAULT_TENANT, request);
        response.Should().NotBeNull();

        //Act
        var responseObj = await tagsApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT, null, 0, 100);

        //Assert
        responseObj.Data.Should().HaveCount(10);
        responseObj.Data.Any(x => x.IsAssociatedToComment).Should().BeTrue();
        responseObj.Data.Count(x => x.IsAssociatedToComment).Should().Be(2);
        responseObj.Data.Where(x => x.IsAssociatedToComment).Select(x => x.Id).Should().BeEquivalentTo(selectedTagIds);
    }

    [Fact]
    public async Task GetPagedTagsList_ValidFilterRequest_ReturnsFilteredItems()
    {
        //Arrange
        await TagsTestEntitiesBuilder.Build(dbContext, 77);
        var tag = await dbContext.Tags.GetRandomEntity();

        //Act
        var responseObj = await tagsApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT, [$"name=>{tag.Name}"], 0, 10);

        //Assert
        responseObj.Data.Should().Contain(x => x.Name.Equals(tag.Name));
    }

    [Fact]
    public async Task GetPagedTagsList_ValidOrderRequest_ReturnsOrderedItems()
    {
        //Arrange
        await TagsTestEntitiesBuilder.Build(dbContext, 25);

        //Act
        var responseObj = await tagsApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT,
            null, 0, 10, "name=>desc");

        //Assert
        responseObj.Data.Should().BeInDescendingOrder(x => x.Name);
    }

    [Fact]
    public async Task GetPagedTagList_PassInvalidTake_ReturnsFirstPage()
    {
        //Arrange
        await TagsTestEntitiesBuilder.Build(dbContext, 55);

        //Act
        var responseObj = await tagsApi.GetPagedTagsListAsync(TenantConstants.DEFAULT_TENANT, null, 15, -15);

        //Assert
        responseObj.Data.Should().HaveCount(40);
    }

    public async Task InitializeAsync()
    {
        await RouteOfAdministrationTestEntitiesBuilder.Build(dbContext, 5);
        await DosageFormsTestEntitiesBuilder.Build(dbContext, 5);
        fixture.AddHeaders("<EMAIL>");
    }

    public async Task DisposeAsync()
    {
        dbContext.Tags.Clear();
        dbContext.Communications.Clear();
        dbContext.DrugProducts.Clear();            
        dbContext.DosageForms.Clear();
        dbContext.RouteOfAdministrations.Clear();

        await dbContext.SaveChangesAsync();
    }
}
