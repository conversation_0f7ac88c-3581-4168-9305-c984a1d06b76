﻿using AutoMapper;
using Axon.HAComms.Application.Commands.Products.Create;
using Axon.HAComms.Application.Commands.Products.Update;
using Axon.HAComms.Application.Models.DrugSubstances;
using Axon.HAComms.Application.Models.Products;
using Axon.HAComms.Domain.Entities;

namespace Axon.HAComms.Application.Common.Mappings;

public class ProductsMappingProfile : Profile
{
    public ProductsMappingProfile()
    {
        CreateMap<Product, ProductModel>().ReverseMap();
        CreateMap<Product, ProductPagedListModel>()
            .ForMember(p => p.Substances, s => s.MapFrom(src => src.DrugSubstances.Select(d => new DrugSubstanceCodeAndName(d.Name!, d.Code))));
        CreateMap<CreateProductCommandRequest, Product>()
            .ForMember(dest => dest.DrugSubstanceProducts, o => o.Ignore())
            .ForMember(dest => dest.ProductProductTypes, o => o.Ignore())
            .ForMember(dest => dest.DrugSubstances, o => o.Ignore())
            .ForMember(dest => dest.ProductTypes, o => o.Ignore())
            .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
            .ForMember(dest => dest.Id, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore());
        CreateMap<Product, CreateProductCommandResponse>();
        CreateMap<UpdateProductCommandRequest, Product>()
            .ForMember(dest => dest.DrugSubstanceProducts, o => o.Ignore())
            .ForMember(dest => dest.ProductProductTypes, o => o.Ignore())
            .ForMember(dest => dest.DrugSubstances, o => o.Ignore())
            .ForMember(dest => dest.ProductTypes, o => o.Ignore())
            .ForMember(dest => dest.ProductExtensions, o => o.Ignore())
            .ForMember(dest => dest.IsDeleted, o => o.Ignore())
            .ForMember(dest => dest.CreatedDate, o => o.Ignore())
            .ForMember(dest => dest.CreatedBy, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedDate, o => o.Ignore())
            .ForMember(dest => dest.LastUpdatedBy, o => o.Ignore())
            .ForMember(dest => dest.ExternalId, o => o.Ignore())
            .ForMember(dest => dest.Tenant, o => o.Ignore());
        CreateMap<Product, UpdateProductCommandResponse>()
            .ForMember(dest => dest.UpdatedCommentsCount, o => o.Ignore());


    }
}
