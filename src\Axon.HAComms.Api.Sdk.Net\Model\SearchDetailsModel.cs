/*
 * Axon.HAComms.Api
 *
 * A REST API for Axon.HAComms.Api.
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Axon.HAComms.Api.Sdk.Net.Client.FileParameter;
using OpenAPIDateConverter = Axon.HAComms.Api.Sdk.Net.Client.OpenAPIDateConverter;

namespace Axon.HAComms.Api.Sdk.Net.Model
{
    /// <summary>
    /// SearchDetailsModel
    /// </summary>
    [DataContract(Name = "SearchDetailsModel")]
    public partial class SearchDetailsModel : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchDetailsModel" /> class.
        /// </summary>
        /// <param name="id">id.</param>
        /// <param name="subject">subject.</param>
        /// <param name="country">country.</param>
        /// <param name="dateOfCommunication">dateOfCommunication.</param>
        /// <param name="submissionType">submissionType.</param>
        /// <param name="applications">applications.</param>
        /// <param name="isCompleted">isCompleted.</param>
        /// <param name="lastUpdatedBy">lastUpdatedBy.</param>
        /// <param name="createdDate">createdDate.</param>
        /// <param name="lastUpdatedDate">lastUpdatedDate.</param>
        /// <param name="createdBy">createdBy.</param>
        /// <param name="comment">comment.</param>
        /// <param name="allProducts">allProducts.</param>
        /// <param name="containsGeneralGuidanceComments">containsGeneralGuidanceComments.</param>
        public SearchDetailsModel(int id = default(int), string subject = default(string), string country = default(string), DateTime dateOfCommunication = default(DateTime), string submissionType = default(string), List<ApplicationModel> applications = default(List<ApplicationModel>), bool isCompleted = default(bool), string lastUpdatedBy = default(string), DateTime createdDate = default(DateTime), DateTime lastUpdatedDate = default(DateTime), string createdBy = default(string), CommentDtoModel comment = default(CommentDtoModel), List<ProductDtoModel> allProducts = default(List<ProductDtoModel>), bool containsGeneralGuidanceComments = default(bool))
        {
            this.Id = id;
            this.Subject = subject;
            this.Country = country;
            this.DateOfCommunication = dateOfCommunication;
            this.SubmissionType = submissionType;
            this.Applications = applications;
            this.IsCompleted = isCompleted;
            this.LastUpdatedBy = lastUpdatedBy;
            this.CreatedDate = createdDate;
            this.LastUpdatedDate = lastUpdatedDate;
            this.CreatedBy = createdBy;
            this.Comment = comment;
            this.AllProducts = allProducts;
            this.ContainsGeneralGuidanceComments = containsGeneralGuidanceComments;
        }

        /// <summary>
        /// Gets or Sets Id
        /// </summary>
        [DataMember(Name = "id", EmitDefaultValue = false)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Subject
        /// </summary>
        [DataMember(Name = "subject", EmitDefaultValue = true)]
        public string Subject { get; set; }

        /// <summary>
        /// Gets or Sets Country
        /// </summary>
        [DataMember(Name = "country", EmitDefaultValue = true)]
        public string Country { get; set; }

        /// <summary>
        /// Gets or Sets DateOfCommunication
        /// </summary>
        [DataMember(Name = "dateOfCommunication", EmitDefaultValue = false)]
        public DateTime DateOfCommunication { get; set; }

        /// <summary>
        /// Gets or Sets SubmissionType
        /// </summary>
        [DataMember(Name = "submissionType", EmitDefaultValue = true)]
        public string SubmissionType { get; set; }

        /// <summary>
        /// Gets or Sets Applications
        /// </summary>
        [DataMember(Name = "applications", EmitDefaultValue = true)]
        public List<ApplicationModel> Applications { get; set; }

        /// <summary>
        /// Gets or Sets IsCompleted
        /// </summary>
        [DataMember(Name = "isCompleted", EmitDefaultValue = true)]
        public bool IsCompleted { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedBy
        /// </summary>
        [DataMember(Name = "lastUpdatedBy", EmitDefaultValue = true)]
        public string LastUpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets CreatedDate
        /// </summary>
        [DataMember(Name = "createdDate", EmitDefaultValue = false)]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or Sets LastUpdatedDate
        /// </summary>
        [DataMember(Name = "lastUpdatedDate", EmitDefaultValue = false)]
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or Sets CreatedBy
        /// </summary>
        [DataMember(Name = "createdBy", EmitDefaultValue = true)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Comment
        /// </summary>
        [DataMember(Name = "comment", EmitDefaultValue = false)]
        public CommentDtoModel Comment { get; set; }

        /// <summary>
        /// Gets or Sets AllProducts
        /// </summary>
        [DataMember(Name = "allProducts", EmitDefaultValue = true)]
        public List<ProductDtoModel> AllProducts { get; set; }

        /// <summary>
        /// Gets or Sets ContainsGeneralGuidanceComments
        /// </summary>
        [DataMember(Name = "containsGeneralGuidanceComments", EmitDefaultValue = true)]
        public bool ContainsGeneralGuidanceComments { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class SearchDetailsModel {\n");
            sb.Append("  Id: ").Append(Id).Append("\n");
            sb.Append("  Subject: ").Append(Subject).Append("\n");
            sb.Append("  Country: ").Append(Country).Append("\n");
            sb.Append("  DateOfCommunication: ").Append(DateOfCommunication).Append("\n");
            sb.Append("  SubmissionType: ").Append(SubmissionType).Append("\n");
            sb.Append("  Applications: ").Append(Applications).Append("\n");
            sb.Append("  IsCompleted: ").Append(IsCompleted).Append("\n");
            sb.Append("  LastUpdatedBy: ").Append(LastUpdatedBy).Append("\n");
            sb.Append("  CreatedDate: ").Append(CreatedDate).Append("\n");
            sb.Append("  LastUpdatedDate: ").Append(LastUpdatedDate).Append("\n");
            sb.Append("  CreatedBy: ").Append(CreatedBy).Append("\n");
            sb.Append("  Comment: ").Append(Comment).Append("\n");
            sb.Append("  AllProducts: ").Append(AllProducts).Append("\n");
            sb.Append("  ContainsGeneralGuidanceComments: ").Append(ContainsGeneralGuidanceComments).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
