﻿using Axon.Core.Shared.Audit;
using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Application.Commands.Tags.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Constants;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;

namespace Axon.HAComms.Tests.Commands.Tags.Delete;

public class DeleteTagCommandHandlerTests
{
    private readonly DeleteTagCommandHandler sut;
    private readonly ITagRepository tagsRepository;
    private readonly ICommentsRepository commentsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteTagCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "10.1.2.3");

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        tagsRepository = Substitute.For<ITagRepository>();
        commentsRepo = Substitute.For<ICommentsRepository>();
        auditService = Substitute.For<IAuditService>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<DeleteTagCommandHandler>>();
        sut = new DeleteTagCommandHandler(tagsRepository, commentsRepo, logger, auditService, correlationIdProvider, clientDetailsProvider, userProvider);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var entity = new Tag
        {
            Name = Fake.Tag.Name
        };

        tagsRepository.GetItemAsync(entity.Id).Returns(entity);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(false);

        var request = new DeleteTagCommandRequest(entity.Id);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new Tag
        {
            Name = Fake.Tag.Name
        };
        var request = new DeleteTagCommandRequest(entity.Id);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public void Handle_WithAssociatedComments_ThrowsAssociationException()
    {
        // Arrange
        var entity = new Tag
        {
            Name = Fake.Tag.Name
        };
        var request = new DeleteTagCommandRequest(entity.Id);
        tagsRepository.GetItemAsync(entity.Id).Returns(entity);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(true);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public async Task Handle_ValidRequest_LogsAudit()
    {
        // Arrange
        var entity = new Tag
        {
            Name = Fake.Tag.Name
        };

        tagsRepository.GetItemAsync(entity.Id).Returns(entity);
        commentsRepo.ExistsAsync(Arg.Any<Expression<Func<Comment, bool>>>()).Returns(false);

        var request = new DeleteTagCommandRequest(entity.Id);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, entity: default);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.TAG_DELETED, AuditEventCategory.TAGS, AuditEventDescription.TAG_DELETE,
                Arg.Any<Tag>());
    }
}
