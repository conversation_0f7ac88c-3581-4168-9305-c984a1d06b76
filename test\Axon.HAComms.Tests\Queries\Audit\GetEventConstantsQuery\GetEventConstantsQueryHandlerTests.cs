﻿using Axon.HAComms.Application.Queries.Audit.GetEventConstantsQuery;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.Tests.Queries.Audit.GetEventConstantsQuery;

public class GetEventConstantsQueryHandlerTests
{
    private readonly GetEventConstantsQueryHandler sut = new();

    [Fact]
    public async Task Handle_WhenCalled_ReturnsEventConstants()
    {
        // Arrange
        var request = new GetEventConstantsQueryRequest();

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.EventCategories.Should().NotBeNull();
        result.EventCategories.Count.Should().Be(8);
        result.EventTypes.Should().NotBeNull();
        result.EventTypes.Count.Should().Be(27);
    }
}
