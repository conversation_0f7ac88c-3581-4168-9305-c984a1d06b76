﻿using AutoMapper;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Application.Common.Mappings;
using Axon.HAComms.Application.Queries.DosageForms.ListQuery;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Tests.Builders;
using Axon.HAComms.Tests.Builders.DosageForms;
using FluentAssertions;
using NSubstitute;
using Xunit;

namespace Axon.HAComms.Tests.Queries.DosageForms.ListQuery;

public class GetDosageFormListQueryHandlerTests
{
    private readonly GetDosageFormListQueryHandler handler;
    private readonly IDosageFormsRepository dosageFormsRepo;

    public GetDosageFormListQueryHandlerTests()
    {
        dosageFormsRepo = Substitute.For<IDosageFormsRepository>();
        var mockMapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new DosageFormsMappingProfile());

        });
        var mapper = mockMapper.CreateMapper();
        handler = new GetDosageFormListQueryHandler(dosageFormsRepo, mapper);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectItems()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(3);

        dosageFormsRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetDosageFormListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[0].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[1].Name);
        result.Data.Select(r => r.Name).Should().Contain(queryableItems[2].Name);
    }

    [Fact]
    public async Task Handle_ListRequest_ReturnsCorrectOrder()
    {
        //Arrange
        var queryableItems = TestEntitiesGenerator<DosageForm, DosageFormBuilder>.Generate(3);

        dosageFormsRepo.GetItemsAsync().Returns(queryableItems);

        var request = new GetDosageFormListQueryRequest();

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        result.Data.Should().HaveCount(3);
        result.Data.Should().BeInAscendingOrder(x => x.Name);
    }
}
