﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.HAComms.Infrastructure.Persistance.Configurations;

public class AuditConfiguration : IEntityTypeConfiguration<Domain.Entities.Audit>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.Audit> builder)
    {
        builder.Property(p => p.Id)
            .HasColumnType("UNIQUEIDENTIFIER ROWGUIDCOL")
            .HasDefaultValueSql("NEWID()");

        builder.Property(p => p.LastUpdatedDate)
            .HasColumnType("[datetimeoffset](7)");

        builder.Property(p => p.StartDate)
            .HasColumnType("[datetimeoffset](7)");

        builder.Property(p => p.EndDate)
            .HasColumnType("[datetimeoffset](7)");

        // Adding indexes
        builder.HasIndex(p => p.StartDate);
        builder.HasIndex(p => p.EventCategory);
        builder.HasIndex(p => p.EventOutcome);
        builder.HasIndex(p => p.EventType);
        builder.HasIndex(p => p.UserEmail);
        builder.HasIndex(p => p.IpAddress);
    }
}
