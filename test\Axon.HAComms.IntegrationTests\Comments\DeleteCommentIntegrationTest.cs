﻿using Axon.HAComms.Api.Sdk.Net.Api;
using Axon.HAComms.Api.Sdk.Net.Client;
using Axon.HAComms.Api.Sdk.Net.Model;
using Axon.HAComms.Infrastructure.Persistance;
using Axon.HAComms.IntegrationTests.Extensions;
using Axon.HAComms.IntegrationTests.Infrastructure;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Comments;
using Axon.HAComms.IntegrationTests.Infrastructure.Builders.Tags;
using Axon.HAComms.IntegrationTests.Multitenancy;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Xunit;

namespace Axon.HAComms.IntegrationTests.Comments;

[Collection(TestCollectionIDs.IntegrationTests)]
public class DeleteCommentIntegrationTest(ApiTestFixture fixture) : IAsyncLifetime
{
    private readonly CommentsApi commentApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly CommunicationsApi communicationApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly ProductsApi productApi = new(fixture.ApiClient, fixture.ApiClient.BaseAddressUrl(), new HttpClientHandler());
    private readonly HACommsContext dbContext = fixture.DbContext;

    [Fact]
    public async Task DeleteComment_ValidId_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var createCommentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        createCommentResponse.Should().NotBeNull();
        createCommentResponse.Id.Should().NotBe(0);

        //Act
        await commentApi.DeleteCommentAsync(createCommentResponse.Id, TenantConstants.DEFAULT_TENANT);
        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var comment = getCommentsResponse.Data[0];
        comment.Id.Should().NotBe(createCommentResponse.Id);
    }

    [Fact]
    public async Task DeleteComment_InvalidId_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product1.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product1.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);
        commentResponse.Should().NotBeNull();
        commentResponse.Id.Should().NotBe(0);

        //Act
        var invalidCommentId = commentResponse.Id + int.MaxValue;
        var deleteCommentResponse = () => commentApi.DeleteCommentAsync(invalidCommentId, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await deleteCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain($"Entity \\\"Comment\\\" ({invalidCommentId}) was not found.");
    }

    [Fact]
    public async Task DeleteComment_CommunicationWithOneComment_ThrowsException()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var commentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Act
        var deleteCommentResponse = () => commentApi.DeleteCommentAsync(commentsResponse.Data[0].Id, TenantConstants.DEFAULT_TENANT);

        //Assert
        var exception = await deleteCommentResponse.Should().ThrowAsync<ApiException>();
        exception.And.Message.Should().Contain("Cannot delete the last comment for a communication.");
    }

    [Fact]
    public async Task DeleteComment_CommunicationWithMultipleProducts_LastComment_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var product2 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var productExtension = product2.ProductExtensions[0];
        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsNonGeneralGuidance()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithProductExtensions(new ProductExtensionCommentModel()
            {
                ProductExtensionId = productExtension.Id, RouteOfAdministrationIds = productExtension.RoutesOfAdministration.Select(r => r.Id).ToList()
            })
            .WithDrugSubstanceIds(product2.DrugSubstances.Select(d => d.Id).ToArray())
            .WithTagIds(tags.Select(x => x.Id).Take(2).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Act
        await commentApi.DeleteCommentAsync(commentResponse.Id, TenantConstants.DEFAULT_TENANT);
        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var comment = getCommentsResponse.Data[0];
        comment.Id.Should().NotBe(commentResponse.Id);

        getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product2.Id);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Should().BeEmpty();
    }

    [Fact]
    public async Task DeleteComment_CommunicationWithGeneralGuidanceAndProductComment_ReturnsOk()
    {
        //Arrange
        var product1 = await ApiTestHelper.CreateProductForTest(dbContext, productApi);
        var communication = await ApiTestHelper.CreateRandomCommunication(communicationApi, product1, dbContext);

        var tags = await TagsTestEntitiesBuilder.Build(dbContext, 4);
        var request = CreateCommentCommandRequestBuilder.Default()
            .AsCommentOnly()
            .AsGeneralGuidance()
            .WithProductExtensions()
            .WithCommunicationId(communication.Id)
            .WithDescription(Fake.Comment.Description)
            .WithTagIds(tags.Select(x => x.Id).ToArray()).Build();
        var commentResponse = await commentApi.CreateCommentAsync(TenantConstants.DEFAULT_TENANT, request);

        //Act
        await commentApi.DeleteCommentAsync(commentResponse.Id, TenantConstants.DEFAULT_TENANT);
        var getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT, product1.Id);

        //Assert
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Count.Should().Be(1);
        var comment = getCommentsResponse.Data[0];
        comment.Id.Should().NotBe(commentResponse.Id);

        getCommentsResponse = await commentApi.GetCommentsByCommunicationIdAsync(communication.Id, TenantConstants.DEFAULT_TENANT);
        getCommentsResponse.Should().NotBeNull();
        getCommentsResponse.Data.Should().BeEmpty();
    }

    public async Task DisposeAsync()
    {
        dbContext.Communications.Clear();
        dbContext.Comments.Clear();
        dbContext.DrugProducts.Clear();
        dbContext.ProductExtensions.Clear();
        dbContext.Submissions.Clear();
        dbContext.DosageForms.Clear();
        dbContext.Applications.Clear();
        dbContext.RouteOfAdministrations.Clear();
        await dbContext.SaveChangesAsync();
    }

    public Task InitializeAsync()
    {
        fixture.AddHeaders("<EMAIL>");
        return Task.CompletedTask;
    }
}
