﻿using Axon.Core.Shared.Audit.Services;
using Axon.Core.Shared.Audit;
using Axon.HAComms.Application.Commands.DosageForms.Delete;
using Axon.HAComms.Application.Common.Interfaces;
using Axon.HAComms.Domain.Entities;
using Axon.HAComms.Domain.Exceptions;
using Axon.HAComms.Tests.Common;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Linq.Expressions;
using Xunit;
using Axon.Core.Shared.CorrelationId;
using Axon.Core.Shared.User;
using Axon.HAComms.Domain.Constants;

namespace Axon.HAComms.Tests.Commands.DosageForms.Delete;

public class DeleteDosageFormCommandHandlerTests
{
    private readonly DeleteDosageFormCommandHandler sut;
    private readonly IDosageFormsRepository dosageFormRepository;
    private readonly IProductExtensionsRepository productExtensionsRepo;
    private readonly Guid correlationId;
    private readonly ClientDetails clientDetails;
    private readonly IAuditService auditService;

    public DeleteDosageFormCommandHandlerTests()
    {
        correlationId = Guid.NewGuid();
        clientDetails = new ClientDetails(Guid.NewGuid().ToString(), "<EMAIL>", "********");

        dosageFormRepository = Substitute.For<IDosageFormsRepository>();
        productExtensionsRepo = Substitute.For<IProductExtensionsRepository>();
        var userProvider = Substitute.For<IUserProvider>();
        var logger = Substitute.For<ILogger<DeleteDosageFormCommandHandler>>();
        auditService = Substitute.For<IAuditService>();

        var correlationIdProvider = Substitute.For<ICorrelationIdProvider>();
        correlationIdProvider
            .Provide()
            .Returns(correlationId);

        var clientDetailsProvider = Substitute.For<IClientDetailsProvider>();
        clientDetailsProvider
            .Provide()
            .Returns(clientDetails);

        sut = new DeleteDosageFormCommandHandler(dosageFormRepository, productExtensionsRepo, logger, correlationIdProvider, clientDetailsProvider, userProvider,
            auditService);
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var entity = new DosageForm
        {
            Name = Fake.DosageForm.Name
        };

        dosageFormRepository.GetItemAsync(entity.Id).Returns(entity);
        productExtensionsRepo.ExistsAsync(Arg.Any<Expression<Func<ProductExtension, bool>>>()).Returns(false);

        var request = new DeleteDosageFormCommandRequest(entity.Id);

        // Act
        var result = await sut.Handle(request, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        auditService
            .ReceivedWithAnyArgs(1)
            .Log(Guid.Empty, default, default, default, default, default, false);
        auditService
            .Received(1)
            .Log(correlationId, clientDetails, AuditEventType.DOSAGE_FORM_DELETED, AuditEventCategory.DOSAGE_FORMS, AuditEventDescription.DOSAGE_FORM_DELETE,
                Arg.Any<DosageForm>());
    }

    [Fact]
    public void Handle_NonExistingEntity_ThrowsEntityNotFoundException()
    {
        // Arrange
        var entity = new DosageForm
        {
            Name = Fake.DosageForm.Name
        };
        var request = new DeleteDosageFormCommandRequest(entity.Id);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }

    [Fact]
    public void Handle_WithAssociatedProducts_ThrowsAssociationException()
    {
        // Arrange
        var entity = new DosageForm
        {
            Name = Fake.DosageForm.Name
        };

        var request = new DeleteDosageFormCommandRequest(entity.Id);
        dosageFormRepository.GetItemAsync(entity.Id).Returns(entity);
        productExtensionsRepo.ExistsAsync(Arg.Any<Expression<Func<ProductExtension, bool>>>()).Returns(true);

        // Act
        var result = async () => { await sut.Handle(request, CancellationToken.None); };

        // Assert
        result.Should().ThrowAsync<EntityNotFoundException>();
    }
}
