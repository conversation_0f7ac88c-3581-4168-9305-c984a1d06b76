﻿using Newtonsoft.Json;
using System.Net;
using Axon.HAComms.Application.Extensions;
using Phlex.Core.Api.Abstractions;
using Phlex.Core.Api.Abstractions.Models;

namespace Axon.HAComms.Api.Middleware
{
    /// <summary>
    /// Global error handler to prevent internal stack trace from being returned to caller.
    /// </summary>
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate next;
        private readonly IHostEnvironment env;

        /// <summary>
        /// Global error handler to prevent internal stack trace from being returned to caller.
        /// </summary>
        public ExceptionMiddleware(RequestDelegate next, IHostEnvironment env)
        {
            this.next = next;
            this.env = env;
        }

        /// <summary>
        /// Invokes the next middleware and returns an <see cref="ErrorResult"/> if an exception occurs.
        /// </summary>
        public async Task InvokeAsync(HttpContext httpContext, ILogger<ExceptionMiddleware> logger)
        {
            try
            {
                await next(httpContext);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(httpContext, ex, env, logger);
            }
        }

        /// <summary>
        /// Returns the stack trace if development mode, otherwise returns a generic error.
        /// </summary>
        /// <returns><see cref="ErrorResult"/></returns>
        private static Task HandleExceptionAsync(HttpContext context, Exception exception, IHostEnvironment env,
            ILogger logger)
        {
            var errorId = Guid.NewGuid().ToString().Substring(0, 8).ToUpper();
            logger.LogError(exception, "Unhandled error occurred. ErrorID: {ErrorID}", errorId);
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";

            var errorResult = env.IsDevelopment() || env.IsIntegrationTest()
                ? new ErrorResult(ErrorTypes.INTERNAL_ERROR, $"Internal Server Error. ErrorID: {errorId}. {exception.Message}", exception.ToString())
                : new ErrorResult(ErrorTypes.INTERNAL_ERROR, $"Internal Server Error. ErrorID: {errorId}.");

            return context.Response.WriteAsync(JsonConvert.SerializeObject(errorResult));
        }
    }
}